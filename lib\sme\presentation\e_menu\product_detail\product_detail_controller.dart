import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../cart/cart_controller.dart';

class ProductDetailController extends GetxController {
  final Map<String, dynamic> product;
  var selectedQuantity = 1.0.obs;
  var defaultItems = <Map<String, dynamic>>[].obs;
  var selectedChoices = <String, List<Map<String, dynamic>>>{}.obs;
  var choices = <Map<String, dynamic>>[].obs;
  var note = ''.obs;
  var isEditMode = false;
  var cartIndex = -1;

  var originalPrice = 0.0.obs;
  var tempPrice = 0.0.obs;

  final CartSMEController cartController = Get.find<CartSMEController>();

  @override
  void onInit() {
    super.onInit();
    originalPrice.value = (product['Price'] as num?)?.toDouble() ?? 0.0;
    tempPrice.value = originalPrice.value;
  }

  void updateTempPrice(double newPrice) {
    tempPrice.value = newPrice;
    update();
  }

  ProductDetailController(dynamic productData) : product = _convertMap(productData) {
    debugPrint('Initial product data: $productData');
    debugPrint('Converted product: $product');

    if (Get.arguments != null && Get.arguments is Map) {
      final args = Get.arguments as Map;
      debugPrint('Arguments: $args');

      if (args['editMode'] == true) {
        isEditMode = true;
        cartIndex = args['cartIndex'] ?? -1;
        selectedQuantity.value = args['initialQuantity'] ?? 1;
        note.value = args['initialNote'] ?? '';

        if (product.isEmpty) {
          product.addAll(_convertMap(args['product'] ?? {}));
        }

        product['ItemName'] = product['ItemName'] ?? args['ItemName'];
        product['Price'] = product['Price'] ?? args['Price'];
        product['ItemDesc'] = product['ItemDesc'] ?? args['ItemDesc'];
        product['MediaUrl'] = product['MediaUrl'] ?? args['MediaUrl'];

        if (args['DefaultItems'] != null) {
          product['DefaultItems'] = args['DefaultItems'];
        }

        if (args['Choices'] != null) {
          product['Choices'] = args['Choices'];
        }

        final initialSelectedChoices = args['initialSelectedChoices'];
        if (initialSelectedChoices != null && initialSelectedChoices is Map) {
          selectedChoices.clear();
          initialSelectedChoices.forEach((choiceId, items) {
            if (items is List) {
              selectedChoices[choiceId.toString()] = List<Map<String, dynamic>>.from(
                items.map((item) => _convertMap(item)),
              );
            }
          });
        }
      }
    }

    if (isEditMode && product.isEmpty && cartIndex >= 0) {
      final cartItem = cartController.getCartItem(cartIndex);
      if (cartItem != null) {
        product.addAll(_convertMap(cartItem['product']));
        debugPrint('Product data loaded from cart: $product');
      }
    }

    if (product['DefaultItems'] != null && product['DefaultItems'] is List) {
      defaultItems.value = (product['DefaultItems'] as List)
          .map((item) => _convertMap(item))
          .toList();
      debugPrint('Default items loaded: ${defaultItems.length}');
    }

    if (product['Choices'] != null && product['Choices'] is List) {
      choices.value = (product['Choices'] as List)
          .map((choice) => _convertMap(choice))
          .toList();

      for (var choice in choices) {
        if (choice['Details'] != null && choice['Details'] is List) {
          choice['Details'] = (choice['Details'] as List)
              .map((detail) => _convertMap(detail))
              .toList();
        }
      }
      debugPrint('Choices loaded: ${choices.length}');
    }

    if (selectedChoices.isEmpty) {
      for (var choice in choices) {
        final choiceId = choice['ChoiceId']?.toString();
        if (choiceId != null && !selectedChoices.containsKey(choiceId)) {
          selectedChoices[choiceId] = [];
        }
      }
      debugPrint('Initialized selected choices: $selectedChoices');
    }
  }

  static Map<String, dynamic> _convertMap(dynamic originalMap) {
    if (originalMap == null) return {};

    if (originalMap is Map<String, dynamic>) {
      return originalMap;
    }

    if (originalMap is Map) {
      return originalMap.cast<String, dynamic>();
    }

    return {};
  }

  void toggleChoiceItem(String choiceId, Map<String, dynamic> item) {
    try {
      if (choiceId.isEmpty || item.isEmpty) return;

      final currentItems = List<Map<String, dynamic>>.from(
          selectedChoices[choiceId] ?? []
      );

      final itemIndex = currentItems.indexWhere(
              (i) => i['ItemId']?.toString() == item['ItemId']?.toString()
      );

      if (itemIndex >= 0) {
        currentItems.removeAt(itemIndex);
      } else {
        final choice = choices.firstWhere(
              (c) => c['ChoiceId']?.toString() == choiceId,
          orElse: () => {},
        );

        final maxChoice = (choice['MaxChoice'] as int?) ?? 1;
        if (currentItems.length < maxChoice) {
          currentItems.add(Map<String, dynamic>.from(item));
        }
      }

      selectedChoices[choiceId] = currentItems;
      update();
    } catch (e) {
      debugPrint('Error in toggleChoiceItem: $e');
    }
  }

  void increaseQuantity() {
    selectedQuantity.value++;
  }

  void decreaseQuantity() {
    if (selectedQuantity.value > (isEditMode ? 0 : 1)) {
      selectedQuantity.value--;
    }
  }




  void addToCart() {
    try {
      final Map<String, dynamic> selectedChoicesMap = {};
      selectedChoices.forEach((choiceId, items) {
        if (items.isNotEmpty) {
          selectedChoicesMap[choiceId] = List<Map<String, dynamic>>.from(items);
        }
      });

      final productToAdd = Map<String, dynamic>.from(product);

      final isPriceModified = tempPrice.value != originalPrice.value;

      productToAdd['Price'] = isPriceModified ? tempPrice.value : originalPrice.value;
      productToAdd['isPriceModified'] = isPriceModified;
      if (isPriceModified) {
        productToAdd['originalPrice'] = originalPrice.value;
      } else {
        productToAdd.remove('originalPrice');
      }

      productToAdd['itemSignature'] = _createItemSignature(
          product: productToAdd,
          selectedChoices: selectedChoicesMap,
          isPriceModified: isPriceModified
      );

      if (selectedChoicesMap.isNotEmpty) {
        productToAdd['SelectedChoices'] = selectedChoicesMap;
      } else {
        productToAdd.remove('SelectedChoices');
      }

      productToAdd['DefaultItems'] = product['DefaultItems'] ?? [];
      productToAdd['Choices'] = product['Choices'] ?? [];

      if (isEditMode && cartIndex >= 0) {
        cartController.replaceCartItem(
          index: cartIndex,
          product: productToAdd,
          quantity: selectedQuantity.value,
          note: note.value.trim(),
        );
        Get.back(result: true);
      } else {
        cartController.addToCart(
          product: productToAdd,
          quantity: selectedQuantity.value,
          note: note.value.trim(),
        );
        Get.back();
      }
    } catch (e) {
      debugPrint('Error in addToCart: $e');
    }
  }

  String _createItemSignature({
    required Map<String, dynamic> product,
    required Map<String, dynamic> selectedChoices,
    required bool isPriceModified
  }) {
    final buffer = StringBuffer();
    buffer.write(product['ItemId']);

    selectedChoices.keys.toList().sort();
    for (final key in selectedChoices.keys) {
      buffer.write('|$key:');
      final items = (selectedChoices[key] as List).map((e) => e['ItemId']).toList();
      items.sort();
      buffer.write(items.join(','));
    }

    if (isPriceModified) {
      buffer.write('|price:${product['Price']}');
    }

    return buffer.toString();
  }
}