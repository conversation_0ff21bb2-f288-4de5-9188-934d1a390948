import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../self_order/presentation/banner/banner_controller.dart';
import '../../self_order/presentation/e_menu/cart/cart_controller.dart';

class InactivityWrapper extends StatefulWidget {
  final Widget child;
  const InactivityWrapper({super.key, required this.child});

  @override
  State<InactivityWrapper> createState() => _InactivityWrapperState();
}

class _InactivityWrapperState extends State<InactivityWrapper> {
  Timer? _inactivityTimer;

  static const Duration timeout = Duration(minutes: 8);

  void _resetTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(timeout, _handleInactivity);
  }

  Future<void> _handleInactivity() async {
    final prefs = Get.find<SharedPreferences>();
    final token = prefs.getString('token_so');
    final rememberMe = prefs.getBool('rememberMe') ?? false;

    if (token != null && token.isNotEmpty && rememberMe) {
      Get.delete<BannerController>();
      // Clear the cart
      final cartController = Get.find<CartController>();
      cartController.clearCart();
      Get.offAllNamed('/banner-page');
    }
  }

  @override
  void initState() {
    super.initState();
    _resetTimer();
  }

  @override
  void dispose() {
    _inactivityTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) => _resetTimer(),
      child: widget.child,
    );
  }
}
