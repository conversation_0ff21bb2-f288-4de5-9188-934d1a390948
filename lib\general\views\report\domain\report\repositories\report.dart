import 'package:dartz/dartz.dart';
import '../entities/report.dart';

// Dashboard
abstract class OverviewRevenueRepository {
  Future<Either<String, OverviewRevenueEntity>> getOverviewRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

abstract class TopBottomBranchesRevenueRepository {
  Future<Either<String, TopBottomBranchesRevenueEntity>> getTopBottomBranchesRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

abstract class Revenue7DaysRepository {
  Future<Either<String, List<Revenue7DaysEntity>>> getRevenue7Days({
    required List<int> listBrandId,
  });
}

abstract class TopBranchesRevenueByDateRepository {
  Future<Either<String, List<TopBranchesRevenueByDateEntity>>> getTopBranchesRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

// By Item
abstract class TopAndBottomItemsRevenueRepository {
  Future<Either<String, TopAndBottomItemsRevenueEntity>> getTopAndBottomItemsRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

abstract class TopItemsRevenueByDateRepository {
  Future<Either<String, List<TopItemsRevenueByDateEntity>>> getTopItemsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

// By Category
abstract class TopAndBottomCategoriesByAmountRepository {
  Future<Either<String, TopAndBottomCategoriesByAmountEntity>> getTopAndBottomCategoriesByAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

abstract class TopCatsRevenueByDateRepository {
  Future<Either<String, List<TopCatsRevenueByDateEntity>>> getTopCatsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

// By Payment
abstract class TotalPaymentAmountRepository {
  Future<Either<String, List<TotalPaymentAmountEntity>>> getTotalPaymentAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}

abstract class TotalPaymentAmountByDateRepository {
  Future<Either<String, List<TotalPaymentAmountByDateEntity>>> getTotalPaymentAmountByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
