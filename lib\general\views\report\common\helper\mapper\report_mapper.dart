import '../../../data/report/models/report.dart';
import '../../../domain/report/entities/report.dart';

// Dashboard
class OverviewRevenueMapper {
  static OverviewRevenueEntity toEntity(OverviewRevenueModel model) {
    return OverviewRevenueEntity(
        paymentAmount: model.paymentAmount,
        itemAmount: model.itemAmount,
        discountAmount: model.discountAmount,
        totalBill: model.totalBill,
        vatAmount: model.vatAmount,
        totalInvoice: model.totalInvoice,
    );
  }
}

class TopBottomBranchesRevenueMapper {
  static TopBottomBranchesRevenueEntity toEntity(TopBottomBranchesRevenueModel model) {
    return TopBottomBranchesRevenueEntity(
        topBranchesRevenue: model.topBranchesRevenue.map((e) => BranchesRevenueEntity(
            branchId: e.branchId,
            branchName: e.branchName,
            paymentAmount: e.paymentAmount,
            paidQuantity: e.paidQuantity,
            itemAmount: e.itemAmount,
            discountAmount: e.discountAmount,
            vatAmount: e.vatAmount,
            svcAmount: e.svcAmount,
            canceledAmount: e.canceledAmount,
            customerQty: e.customerQty,
            unPaidAmount: e.unPaidAmount,
            focAmount: e.focAmount,
            avgPayment: e.avgPayment,
            orderBranch: e.orderBranch)).toList(),
      bottomBranchesRevenue: model.bottomBranchesRevenue.map((e) =>
          BranchesRevenueEntity(
              branchId: e.branchId,
              branchName: e.branchName,
              paymentAmount: e.paymentAmount,
              paidQuantity: e.paidQuantity,
              itemAmount: e.itemAmount,
              discountAmount: e.discountAmount,
              vatAmount: e.vatAmount,
              svcAmount: e.svcAmount,
              canceledAmount: e.canceledAmount,
              customerQty: e.customerQty,
              unPaidAmount: e.unPaidAmount,
              focAmount: e.focAmount,
              avgPayment: e.avgPayment,
              orderBranch: e.orderBranch)).toList(),
    );
  }
}

class Revenue7DaysMapper {
  static Revenue7DaysEntity toEntity(Revenue7DaysModel model) {
    return Revenue7DaysEntity(
      rDay: model.rDay ?? "",
      paymentAmount: model.paymentAmount ?? 0,
    );
  }

  static List<Revenue7DaysEntity> toEntityList(List<Revenue7DaysModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }
}

class TopBranchesRevenueByDateMapper {
  static TopBranchesRevenueByDateEntity toEntity(TopBranchesRevenueByDateModel model) {
    return TopBranchesRevenueByDateEntity(
        revenueDate: model.revenueDate,
        revenues: model.revenues
    );
  }
}

// By Item
class TopAndBottomItemsRevenueMapper {
  static TopAndBottomItemsRevenueEntity toEntity(TopAndBottomItemsRevenueModel model) {
    return TopAndBottomItemsRevenueEntity(
      topItemsByTotalQuantity: model.topItemsByTotalQuantity.map((e) => ItemEntity(
        itemName: e.itemName,
        totalQuantity: e.totalQuantity,
        totalAmount: e.totalAmount,
      )).toList(),
      topItemsByTotalAmount: model.topItemsByTotalAmount.map((e) => ItemEntity(
        itemName: e.itemName,
        totalQuantity: e.totalQuantity,
        totalAmount: e.totalAmount,
      )).toList(),
      bottomItems: model.bottomItems.map((e) => ItemEntity(
        itemName: e.itemName,
        totalQuantity: e.totalQuantity,
        totalAmount: e.totalAmount,
      )).toList(),
    );
  }
}

class TopItemsRevenueByDateMapper {
  static TopItemsRevenueByDateEntity toEntity(TopItemsRevenueByDateModel model) {
    return TopItemsRevenueByDateEntity(
        revenueDate: model.revenueDate,
        revenues: model.revenues
    );
  }
}

// By Category
class TopAndBottomCategoriesByAmountMapper {
  static TopAndBottomCategoriesByAmountEntity toEntity(TopAndBottomCategoriesByAmountModel model) {
    return TopAndBottomCategoriesByAmountEntity(
      topCategoriesByQuantity: model.topCategoriesByQuantity.map((e) => CategoriesByAmountEntity(
          groupName: e.groupName,
          totalQuantity: e.totalQuantity,
          totalAmount: e.totalAmount,
        )).toList(),
      topCategoriesByAmount: model.topCategoriesByAmount.map((e) => CategoriesByAmountEntity(
          groupName: e.groupName,
          totalQuantity: e.totalQuantity,
          totalAmount: e.totalAmount,
        )).toList(),
      bottomCategoriesByAmount: model.bottomCategoriesByAmount.map((e) => CategoriesByAmountEntity(
          groupName: e.groupName,
          totalQuantity: e.totalQuantity,
          totalAmount: e.totalAmount,
        )).toList(),
    );
  }
}

class TopCatsRevenueByDateMapper {
  static TopCatsRevenueByDateEntity toEntity(TopCatsRevenueByDateModel model) {
    return TopCatsRevenueByDateEntity(
        revenueDate: model.revenueDate,
        revenues: model.revenues
    );
  }
}

// By Payment
class TotalPaymentAmountMapper {
  static TotalPaymentAmountEntity toEntity(TotalPaymentAmountModel model) {
    return TotalPaymentAmountEntity(
      payName: model.payName,
      paymentAmount: model.paymentAmount,
      percent: model.percent,
    );
  }

  static List<TotalPaymentAmountEntity> toEntityList(List<TotalPaymentAmountModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }
}

class TotalPaymentAmountByDateMapper {
  static TotalPaymentAmountByDateEntity toEntity(TotalPaymentAmountByDateModel model) {
    return TotalPaymentAmountByDateEntity(
      paymentDate: model.paymentDate,
      payments: model.payments,
    );
  }
}
