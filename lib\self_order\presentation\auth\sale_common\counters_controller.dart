import 'package:get/get.dart';

import '../../../domain/entities/sale_common/counters_response_entity.dart';
import '../../../domain/usecases/sale_common/couters_usecase.dart';
import 'branches_controller.dart';

class CounterController extends GetxController {
  final GetCountersUseCase getCountersUseCase;

  CounterController({required this.getCountersUseCase});

  var counters = <CounterResponseEntity>[].obs;
  var selectedCounter = Rxn<CounterResponseEntity>();
  var isLoading = false.obs;
  var error = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // <PERSON><PERSON><PERSON> lại khi branchId thay đổi
    ever(Get.find<BranchesController>().selectedBranch, (branch) {
      if (branch != null) {
        fetchCounters(branch.branchId ?? 0);
      }
    });
  }


  Future<void> fetchCounters(int branchId) async {
    try {
      isLoading(true);
      final response = await getCountersUseCase(branchId);
      counters.value = response;
      selectedCounter.value = null;
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading(false);
    }
  }
}
