import '../../../domain/entities/sale_common/branches_response_entity.dart';
import '../../../domain/repositories/sale_common/branches_repository.dart';
import '../../datasources/sale_common/branches_remote_data_source.dart';

class BranchesRepositoryImpl implements BranchesRepository {
  final BranchesRemoteDataSource remoteDataSource;

  BranchesRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<BranchesResponseEntity>> branches() async {

    final response = await remoteDataSource.branches();

    return response.map((e) => BranchesResponseEntity(
      branchId: e.branchId,
      branchName: e.branchName,
      branchNo: e.branchNo,
      branchParentId: e.branchParentId,
    )).toList();
  }
}