import '../../../domain/entities/order/order_request_entity.dart';

class OrderRequestModel {
  final int menuId;
  final int timeSaleMenuId;
  final int branchId;
  final String customerPhone;
  final String customerNo;
  final String customerName;
  final String orderNote;
  final String orderType;
  final List<OrderDetailModel> orderDetails;

  OrderRequestModel({
    required this.menuId,
    required this.timeSaleMenuId,
    required this.branchId,
    required this.customerPhone,
    required this.customerNo,
    required this.customerName,
    required this.orderNote,
    required this.orderType,
    required this.orderDetails,
  });

  Map<String, dynamic> toJson() => {
    'MenuId': menuId,
    'TimeSaleMenuId': timeSaleMenuId,
    'BranchId': branchId,
    'CustomerPhone': customerPhone,
    'CustomerNo': customerNo,
    'CustomerName': customerName,
    'OrderNote': orderNote,
    'OrderType': orderType,
    'OrderDetails': orderDetails.map((detail) => detail.toJson()).toList(),
  };

  factory OrderRequestModel.fromEntity(OrderRequestEntity entity) {
    return OrderRequestModel(
      menuId: entity.menuId,
      timeSaleMenuId: entity.timeSaleMenuId,
      branchId: entity.branchId,
      customerPhone: entity.customerPhone,
      customerNo: entity.customerNo,
      customerName: entity.customerName,
      orderNote: entity.orderNote,
      orderType: entity.orderType,
      orderDetails: entity.orderDetails
          .map((detail) => OrderDetailModel.fromEntity(detail))
          .toList(),
    );
  }
}

class OrderDetailModel {
  final String itemNo;
  final String itemName;
  final int itemQuantity;
  final double itemPrice;
  final double discountAmount;
  final String itemNote;
  final List<ChildrenModel>? children;

  OrderDetailModel({
    required this.itemNo,
    required this.itemName,
    required this.itemQuantity,
    required this.itemPrice,
    required this.discountAmount,
    required this.itemNote,
    this.children,
  });

  Map<String, dynamic> toJson() => {
    'ItemNo': itemNo,
    'ItemName': itemName,
    'ItemQuantity': itemQuantity,
    'ItemPrice': itemPrice,
    'DiscountAmount': discountAmount,
    'ItemNote': itemNote,
    'Childrens': children?.map((child) => child.toJson()).toList(),
  };

  factory OrderDetailModel.fromJson(Map<String, dynamic> json) {
    return OrderDetailModel(
      itemNo: json['ItemNo'],
      itemName: json['ItemName'],
      itemQuantity: json['ItemQuantity'],
      itemPrice: json['ItemPrice'].toDouble(),
      discountAmount: json['DiscountAmount'].toDouble(),
      itemNote: json['ItemNote'],
      children: json['Childrens'] != null
          ? List<ChildrenModel>.from(
          json['Childrens'].map((x) => ChildrenModel.fromJson(x)))
          : null,
    );
  }

  factory OrderDetailModel.fromEntity(OrderDetailEntity entity) {
    return OrderDetailModel(
      itemNo: entity.itemNo,
      itemName: entity.itemName,
      itemQuantity: entity.itemQuantity,
      itemPrice: entity.itemPrice,
      discountAmount: entity.discountAmount,
      itemNote: entity.itemNote,
      children: entity.children
          ?.map((child) => ChildrenModel.fromEntity(child))
          .toList(),
    );
  }
}

class ChildrenModel {
  final String itemNo;
  final String itemName;
  final int itemQuantity;
  final double itemPrice;
  final double discountAmount;

  ChildrenModel({
    required this.itemNo,
    required this.itemName,
    required this.itemQuantity,
    required this.itemPrice,
    required this.discountAmount,
  });

  Map<String, dynamic> toJson() => {
    'ItemNo': itemNo,
    'ItemName': itemName,
    'ItemQuantity': itemQuantity,
    'ItemPrice': itemPrice,
    'DiscountAmount': discountAmount,
  };

  factory ChildrenModel.fromJson(Map<String, dynamic> json) {
    return ChildrenModel(
      itemNo: json['ItemNo'],
      itemName: json['ItemName'],
      itemQuantity: json['ItemQuantity'],
      itemPrice: json['ItemPrice'].toDouble(),
      discountAmount: json['DiscountAmount'].toDouble(),
    );
  }

  factory ChildrenModel.fromEntity(ChildrenEntity entity) {
    return ChildrenModel(
      itemNo: entity.itemNo,
      itemName: entity.itemName,
      itemQuantity: entity.itemQuantity,
      itemPrice: entity.itemPrice,
      discountAmount: entity.discountAmount,
    );
  }
}