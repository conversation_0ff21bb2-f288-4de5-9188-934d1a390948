import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class ImageCacheHelper {
  static Widget cachedBanner({
    required String url,
    required double borderRadius,
    BoxFit fit = BoxFit.cover,
  }) {
    // Kiểm tra cache ở đây

    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: CachedNetworkImage(
        imageUrl: url,
        fit: fit,
        width: double.infinity,
        placeholder: (context, url) => Container(
          color: Colors.grey.shade300,
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey,
          child: const Icon(Icons.broken_image),
        ),
      ),
    );
  }
}
