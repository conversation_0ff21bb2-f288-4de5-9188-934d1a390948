class AuthValidator {
  static final validCharacters = RegExp(r'^[a-zA-Z0-9\s]+$');

  static String? validatorUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập tên đăng nhập';
    }
    if (containsVietnamese(value)) {
      return 'Tên đăng nhập không được có dấu';
    }
    if (!validCharacters.hasMatch(value)) {
      return 'Tên đăng nhập chỉ được chứa chữ cái và số';
    }

    if (value.contains(' ')) {
      return 'Tên đăng nhập không được có khoảng trắng';
    }

    if (value.length < 3) {
      return 'Tên đăng nhập phải có ít nhất 3 ký tự';
    }
    if (value.length > 50) {
      return 'Tên đăng nhập không được quá 50 ký tự';
    }
    return null;
  }

  static bool containsVietnamese(String input) {
    final regex = RegExp(r'[^\x00-\x7F]');
    return regex.hasMatch(input);
  }

  static String? validatorPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập mật khẩu';
    }
    if (value.length < 4) {
      return 'Mật khẩu phải có ít nhất 4 ký tự';
    }
    if (value.length > 50) {
      return 'Mật khẩu không được quá 50 ký tự';
    }
    return null;
  }

  static String? validatorConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập lại mật khẩu';
    }
    if (value != password) {
      return 'Mật khẩu không khớp';
    }
    return null;
  }

  static String? validatorName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập tên của bạn';
    }
  

    return null;
  }

  static String? validatorPhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập số điện thoại';
    }
    if (value.length <=5 || value.length >= 15) {
      return 'Số điện thoại phải từ 5-15 ký tự';
    }
    return null;
  }

  static String? validatorBrandName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập tên thương hiệu/đơn vị';
    }
    return null;
  }

  static String? validatorBranchName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập tên chi nhánh/cửa hàng';
    }
    return null;
  }

  static String? validatorBusinessType(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng chọn loại hình kinh doanh';
    }
    return null;
  }

  static String? validatorIsEnterprise(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng chọn loại hình doanh nghiệp';
    }
    return null;
  }
}
