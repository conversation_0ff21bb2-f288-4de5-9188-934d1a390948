import 'package:hive/hive.dart';
import '../../models/e_menu/menu_tree_model.dart';

abstract class MenuLocalDataSource {
  Future<void> cacheMenuTree(MenuTreeModel model);
  Future<MenuTreeModel?> getCachedMenuTree();
}

class MenuLocalDataSourceImpl implements MenuLocalDataSource {
  final String boxName = 'menuBox';

  @override
  Future<void> cacheMenuTree(MenuTreeModel model) async {
    final box = await Hive.openBox<MenuTreeModel>(boxName);
    await box.put('menu_tree', model);
  }

  @override
  Future<MenuTreeModel?> getCachedMenuTree() async {
    final box = await Hive.openBox<MenuTreeModel>(boxName);
    return box.get('menu_tree');
  }
}
