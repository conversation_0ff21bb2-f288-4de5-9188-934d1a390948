import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

Widget shimmerLoading(double width) {
  return Shimmer.fromColors(
    baseColor: Colors.grey.shade300,
    highlightColor: Colors.grey.shade100,
    child: Column(
      children: [
        // Search bar placeholder
        Padding(
          padding: EdgeInsets.all(width * 0.03),
          child: Container(
            height: width * 0.1,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(width * 0.02),
            ),
          ),
        ),
        // Categories placeholder
        SizedBox(
          height: width * 0.15,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: width * 0.03),
            itemCount: 6,
            separatorBuilder: (_, __) => SizedBox(width: width * 0.03),
            itemBuilder: (_, __) => Container(
              width: width * 0.15,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(width * 0.02),
              ),
            ),
          ),
        ),
        SizedBox(height: width * 0.02),
        // Products placeholder
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: width * 0.03),
            child: GridView.builder(
              itemCount: 12,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisSpacing: width * 0.03,
                crossAxisSpacing: width * 0.03,
                childAspectRatio: 0.75,
              ),
              itemBuilder: (_, __) => Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(width * 0.02),
                ),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
