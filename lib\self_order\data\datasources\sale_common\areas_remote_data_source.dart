import '../../../../core/constants/api_url.dart';
import '../../../../core/network/dio_client.dart';
import '../../models/sale_common/areas_response_model.dart';
import '../../models/sale_common/counters_response_model.dart';

abstract class AreasRemoteDataSource {
  Future<List<AreasModel>> getAreas(int branchId, int counterId);
}

class AreasRemoteDataSourceImpl implements AreasRemoteDataSource {
  final DioClient dioClient;

  AreasRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<AreasModel>> getAreas(int branchId, int counterId) async {
    try {
      final response = await dioClient.get(
        ApiUrl.areas,
        queryParameters: {'branchId': branchId, 'counterId': counterId},
      );
      return AreasListResponseModel.fromJson(response.data).areas;
    } catch (e) {
      rethrow;
    }
  }
}
