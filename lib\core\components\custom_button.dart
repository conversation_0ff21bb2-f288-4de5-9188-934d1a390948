import 'package:flutter/material.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/theme/app_style.dart';
import 'package:gls_self_order/core/widgets/default_button.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  final Color color;
  final Color? textColor;
  final Color? borderColor;
  final double fontSize;
  const CustomButton(
      {super.key,
      required this.text,
      required this.onTap,
      required this.color,
      this.textColor,
        this.borderColor,
        this.fontSize = 16
      });

  @override
  Widget build(BuildContext context) {
    return DefaultButton(
      onPress: onTap,
      title: text,
      titleStyle: PrimaryFont.bold.copyWith(
        fontSize: fontSize,
        color: textColor ?? AppColors.background,
      ),
      color: color,
      widthPercentage: 1,
      borderRadius: 10,
      heightPercentage: 0.12,
      borderColor: borderColor,
    );
  }
}
