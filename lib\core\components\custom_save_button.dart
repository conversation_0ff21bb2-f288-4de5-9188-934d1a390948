import 'package:flutter/material.dart';

class CustomSaveButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool hasPadding;
  const CustomSaveButton({super.key, required this.onTap, this.hasPadding = true});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(hasPadding ? 10 : 0),
        child: Image.asset('assets/images/general/save.png', width: 30,),
      ),
    );
  }
}
