class OrderResponseModel {
  final bool success;
  final int code;
  final String message;
  final OrderResultModel? result;
  final String? errorDetail;

  OrderResponseModel({
    required this.success,
    required this.code,
    required this.message,
    this.result,
    this.errorDetail,
  });

  factory OrderResponseModel.fromJson(Map<String, dynamic> json) {

    print("${json}");
    print("${json['Success']}");

    return OrderResponseModel(
      success: json['Success'] ?? false,
      code: json['Code'] ?? 0,
      message: json['Message'] ?? '',
      result: json['Result'] != null
          ? OrderResultModel.fromJson(json['Result'])
          : null,
      errorDetail: json['ErrorDetail'],
    );
  }
}

class OrderResultModel {
  final OrderResponse orderResponse;
  final List<OrderDetailResponseModel> orderDetails;

  OrderResultModel({
    required this.orderResponse,
    required this.orderDetails,
  });

  factory OrderResultModel.fromJson(Map<String, dynamic> json) {
    print("${json}");
    print("${json['OrderResponse']}");
    return OrderResultModel(
      orderResponse: OrderResponse.fromJson(json['OrderResponse']),
      orderDetails: List<OrderDetailResponseModel>.from(
          json['OrderDetails'].map((x) => OrderDetailResponseModel.fromJson(x))),
    );
  }
}

class OrderResponse {
  final int orderId;
  final int branchId;
  final String branchName;
  final String orderCode;
  final String? ticketCode;
  final String orderType;
  final String statusCode;
  final String? paymentMethod;
  final String? paymentKey;
  final String? paymentMsg;
  final String customerPhone;
  final String customerNo;
  final String customerName;
  final double orderSubTotalVAT;
  final double orderDiscountAmountVAT;
  final double orderVATAmount;
  final double orderTotalVAT;
  final double orderSubTotalView;
  final double orderDiscountAmountView;
  final double orderTotalView;
  final String orderNote;
  final String createdAt;
  final int createdBy;
  final String rowVersion;

  OrderResponse({
    required this.orderId,
    required this.branchId,
    required this.branchName,
    required this.orderCode,
    this.ticketCode,
    required this.orderType,
    required this.statusCode,
    this.paymentMethod,
    this.paymentKey,
    this.paymentMsg,
    required this.customerPhone,
    required this.customerNo,
    required this.customerName,
    required this.orderSubTotalVAT,
    required this.orderDiscountAmountVAT,
    required this.orderVATAmount,
    required this.orderTotalVAT,
    required this.orderSubTotalView,
    required this.orderDiscountAmountView,
    required this.orderTotalView,
    required this.orderNote,
    required this.createdAt,
    required this.createdBy,
    required this.rowVersion,
  });

  factory OrderResponse.fromJson(Map<String, dynamic> json) {
    return OrderResponse(
      orderId: json['OrderId'],
      branchId: json['BranchId'],
      branchName: json['BranchName'],
      orderCode: json['OrderCode'],
      ticketCode: json['TicketCode'],
      orderType: json['OrderType'],
      statusCode: json['StatusCode'],
      paymentMethod: json['PaymentMethod'],
      paymentKey: json['PaymentKey'],
      paymentMsg: json['PaymentMsg'],
      customerPhone: json['CustomerPhone'],
      customerNo: json['CustomerNo'],
      customerName: json['CustomerName'],
      orderSubTotalVAT: json['OrderSubTotalVAT'].toDouble(),
      orderDiscountAmountVAT: json['OrderDiscountAmountVAT'].toDouble(),
      orderVATAmount: json['OrderVATAmount'].toDouble(),
      orderTotalVAT: json['OrderTotalVAT'].toDouble(),
      orderSubTotalView: json['OrderSubTotalView'].toDouble(),
      orderDiscountAmountView: json['OrderDiscountAmountView'].toDouble(),
      orderTotalView: json['OrderTotalView'].toDouble(),
      orderNote: json['OrderNote'],
      createdAt: json['CreatedAt'],
      createdBy: json['CreatedBy'],
      rowVersion: json['RowVersion'],
    );
  }
}

class OrderDetailResponseModel {
  final int detailId;
  final bool isParent;
  final String detailCode;
  final int orderId;
  final String orderCode;
  final String? ticketItemId;
  final String itemNo;
  final String itemName;
  final String? itemNote;
  final double itemVATPer;
  final bool includeVAT;
  final double itemQty;
  final double itemPriceVAT;
  final double subTotalVAT;
  final double discountAmountVAT;
  final double vatAmount;
  final double totalAmountVAT;
  final double itemPrice;
  final double subTotal;
  final double discountAmount;
  final double totalAmount;
  final String createdDate;
  final List<OrderDetailResponseModel>? children;

  OrderDetailResponseModel({
    required this.detailId,
    required this.isParent,
    required this.detailCode,
    required this.orderId,
    required this.orderCode,
    this.ticketItemId,
    required this.itemNo,
    required this.itemName,
    this.itemNote,
    required this.itemVATPer,
    required this.includeVAT,
    required this.itemQty,
    required this.itemPriceVAT,
    required this.subTotalVAT,
    required this.discountAmountVAT,
    required this.vatAmount,
    required this.totalAmountVAT,
    required this.itemPrice,
    required this.subTotal,
    required this.discountAmount,
    required this.totalAmount,
    required this.createdDate,
    this.children,
  });

  factory OrderDetailResponseModel.fromJson(Map<String, dynamic> json) {
    return OrderDetailResponseModel(
      detailId: json['DetailId'],
      isParent: json['IsParent'],
      detailCode: json['DetailCode'],
      orderId: json['OrderId'],
      orderCode: json['OrderCode'],
      ticketItemId: json['TicketItemId'],
      itemNo: json['ItemNo'],
      itemName: json['ItemName'],
      itemNote: json['ItemNote'],
      itemVATPer: json['ItemVATPer'].toDouble(),
      includeVAT: json['IncludeVAT'],
      itemQty: json['ItemQty'],
      itemPriceVAT: json['ItemPriceVAT'].toDouble(),
      subTotalVAT: json['SubTotalVAT'].toDouble(),
      discountAmountVAT: json['DiscountAmountVAT'].toDouble(),
      vatAmount: json['VATAmount'].toDouble(),
      totalAmountVAT: json['TotalAmountVAT'].toDouble(),
      itemPrice: json['ItemPrice'].toDouble(),
      subTotal: json['SubTotal'].toDouble(),
      discountAmount: json['DiscountAmount'].toDouble(),
      totalAmount: json['TotalAmount'].toDouble(),
      createdDate: json['CreatedDate'],
      children: json['Children'] != null
          ? List<OrderDetailResponseModel>.from(
          json['Children'].map((x) => OrderDetailResponseModel.fromJson(x)))
          : null,
    );
  }
}