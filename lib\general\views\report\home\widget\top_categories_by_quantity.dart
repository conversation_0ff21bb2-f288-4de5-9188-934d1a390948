import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/helper/format_helper.dart';
import '../../domain/report/entities/report.dart';

class TopCategoriesByQuantity extends StatelessWidget {
  final double width;
  final List<CategoriesByAmountEntity> topCategoriesByQuantity;

  const TopCategoriesByQuantity({
    super.key,
    required this.width,
    required this.topCategoriesByQuantity,
  });

  @override
  Widget build(BuildContext context) {
    bool isEmptyData = topCategoriesByQuantity.isEmpty ||
        topCategoriesByQuantity.every((e) => (e.totalQuantity ?? 0) == 0);

    List<CategoriesByAmountEntity> displayData = isEmptyData
        ? [CategoriesByAmountEntity(groupName: "Không có dữ liệu", totalQuantity: 0, totalAmount: 0)]
        : topCategoriesByQuantity;

    Map<String, double> groupedCategories = {};
    for (var category in displayData) {
      String categoryName = category.groupName ?? "Không xác định";
      double quantity = category.totalQuantity ?? 0;
      groupedCategories[categoryName] = (groupedCategories[categoryName] ?? 0) + quantity;
    }

    List<MapEntry<String, double>> sortedCategories = groupedCategories.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final top10Categories = sortedCategories.take(10).toList();

    final List<Color> colors = [
      Colors.blue, Colors.green, Colors.red, Colors.orange, Colors.purple,
      Colors.teal, Colors.yellow, Colors.pink, Colors.cyan, Colors.brown
    ];

    List<Map<String, dynamic>> data = [];
    bool hasMultilineName = false;

    for (int i = 0; i < top10Categories.length; i++) {
      String formattedName = _formatCategoryName(top10Categories[i].key, 10);
      if (formattedName.contains("\n")) {
        hasMultilineName = true;
      }

      data.add({
        "categoryName": formattedName,
        "quantity": top10Categories[i].value.toInt(),
        "color": colors[i % colors.length],
      });
    }

    num maxQuantity = data.map((e) => e["quantity"]).reduce((a, b) => a > b ? a : b);
    num maxYAxis = maxQuantity > 0 ? (maxQuantity * 1.2) : 1;

    double extraBottomPadding = hasMultilineName ? width * 0.05 : width * 0.015;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: width * 0.015, vertical: width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.015),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.015),
                child: Center(
                  child: Text(
                    "Nhóm hàng bán chạy theo số lượng (Top 10)".toUpperCase(),
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                    maxLines: 2,
                  ),
                ),
              ),
            ),
            SizedBox(height: width * 0.03),
            SizedBox(
              height: width * 0.65,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Container(
                  padding: EdgeInsets.only(bottom: extraBottomPadding),
                  width: width * 1.8,
                  child: Chart(
                    data: data,
                    variables: {
                      'categoryName': Variable(
                        accessor: (Map map) => map['categoryName'] as String,
                        scale: OrdinalScale(),
                      ),
                      'quantity': Variable(
                        accessor: (Map map) => map['quantity'] as num,
                        scale: LinearScale(
                          min: 0,
                          max: maxYAxis,
                          tickCount: 5,
                          formatter: (num value) => FormatHelper.formatCurrency(value),
                        ),
                      ),
                    },
                    marks: [
                      IntervalMark(
                        position: Varset('categoryName') * Varset('quantity'),
                        color: ColorEncode(
                          variable: 'categoryName',
                          values: data.length < 2
                              ? [Colors.grey, Colors.grey]
                              : data.map((e) => e["color"] as Color).toList(),
                        ),
                        label: LabelEncode(
                          encoder: (tuple) => Label(
                            FormatHelper.formatCurrency(tuple['quantity'] as num),
                            LabelStyle(
                              textStyle: PrimaryFont.bold.copyWith(
                                fontSize: width * 0.03,
                                color: AppColors.text,
                              ),
                              offset: Offset(0, -10),
                            ),
                          ),
                        ),
                      ),
                    ],
                    axes: [
                      AxisGuide(
                        line: PaintStyle(strokeWidth: 0),
                        label: LabelStyle(
                          textStyle: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          offset: Offset(0, 10),
                        ),
                      ),
                      AxisGuide(
                        label: LabelStyle(
                          textStyle: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          offset: const Offset(-5, 0),
                        ),
                        grid: PaintStyle(
                          strokeColor: AppColors.shadow.withOpacity(0.3),
                          strokeWidth: 1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: width * 0.01),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(top10Categories.length, (i) {
                  return _buildLegendItem(i, top10Categories, colors, width);
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatCategoryName(String name, int maxLength) {
    List<String> words = name.split(' ');
    String newName = '';
    int lineLength = 0;

    for (String word in words) {
      if (lineLength + word.length > maxLength) {
        newName += '\n';
        lineLength = 0;
      }
      newName += (lineLength == 0 ? '' : ' ') + word;
      lineLength += word.length + 1;
    }

    return newName;
  }

  Widget _buildLegendItem(int index, List<MapEntry<String, double>> top10Categories, List<Color> colors, double width) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Row(
        children: [
          Container(
            width: width * 0.03,
            height: width * 0.03,
            color: colors[index % colors.length],
          ),
          SizedBox(width: width * 0.01),
          Text(
            top10Categories[index].key,
            style: PrimaryFont.regular.copyWith(
              fontSize: width * 0.028,
              color: AppColors.text,
            ),
          ),
        ],
      ),
    );
  }
}
