import 'package:get/get.dart';
import '../../../domain/entities/sale_common/tables_reponse_entity.dart';
import '../../../domain/usecases/sale_common/tables_usecase.dart';
import 'areas_controller.dart';
import 'branches_controller.dart';

class TablesController extends GetxController {
  final GetTablesUseCase getTablesUseCase;

  TablesController({required this.getTablesUseCase});

  var tables = <TablesResponseEntity>[].obs;
  var selectedTable = Rxn<TablesResponseEntity>();
  var isLoading = false.obs;
  var error = ''.obs;

  @override
  void onInit() {
    super.onInit();

    final branchController = Get.find<BranchesController>();
    final areaController = Get.find<AreasController>();

    everAll([
      branchController.selectedBranch,
      areaController.selectedArea,
    ], (_) {
      final branch = branchController.selectedBranch.value;
      final area = areaController.selectedArea.value;

      if (branch != null && area != null) {
        fetchTables(branch.branchId ?? 0, area.areaId ?? 0);
      }
    });
  }

  Future<void> fetchTables(int branchId, int areaId) async {
    try {
      isLoading(true);
      final response = await getTablesUseCase(branchId, areaId);
      tables.value = response;
      selectedTable.value = null;
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading(false);
    }
  }

}
