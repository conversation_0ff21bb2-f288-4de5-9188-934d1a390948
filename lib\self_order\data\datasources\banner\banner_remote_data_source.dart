import '../../../../core/constants/api_url.dart';
import '../../../../core/network/dio_client.dart';
import '../../models/banner/banner_response_model.dart';

abstract class BannerRemoteDataSource {
  Future<List<BannerResponseModel>> getBanner();
}

class BannerRemoteDataSourceImpl implements BannerRemoteDataSource {
  final DioClient dioClient;

  BannerRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<BannerResponseModel>> getBanner() async {
    try {
      final response = await dioClient.get(
        ApiUrl.banner,
      );
      return BannerListResponseModel.fromJson(response.data).result;
    } catch (e) {
      rethrow;
    }
  }
}
