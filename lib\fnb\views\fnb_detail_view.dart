import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class FnbDetailView extends StatefulWidget {
  const FnbDetailView({super.key});

  @override
  State<FnbDetailView> createState() => _FnbDetailViewState();
}

class _FnbDetailViewState extends State<FnbDetailView> {
  //variable
  List items = [
    'Canh chua cá hú',
    'Cơm chiên hải sản',
    'Tôm xào ngũ sắc',
    '<PERSON>u muống xào tỏi'
  ];

  //function

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: '<PERSON><PERSON> sách kiểm món'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  color: Color(0xff59DBFF).withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(10)),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.all(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(text: 'Bàn: 3'),
                  CustomText(text: 'Bill: 2805'),
                  CustomText(text: '🍽 Khách: 3'),
                  CustomText(text: '🕓14:03'),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppColors.shadow))),
              margin: EdgeInsets.fromLTRB(10, 0, 10, 10),
              padding: EdgeInsets.fromLTRB(10, 0, 10, 10),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomText(
                          text: 'Món đã gọi',
                          bold: true,
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomText(
                          text: 'Trạng thái',
                          bold: true,
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomText(
                          text: 'Trả món',
                          bold: true,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: ListView(
                children: [
                  for (dynamic item in items)
                    Container(
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                        width: 1,
                        color: AppColors.shadow.withValues(alpha: 0.3),
                      ))),
                      padding: EdgeInsets.fromLTRB(0, 0, 0, 5),
                      margin: EdgeInsets.fromLTRB(10, 0, 10, 5),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Column(
                              children: [
                                Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                      color: Color(0xff59DBFF)
                                          .withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(10)),
                                  margin: EdgeInsets.fromLTRB(0, 5, 5, 5),
                                  padding: EdgeInsets.all(5),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CustomText(
                                        text: item,
                                        bold: true,
                                        maxLines: 2,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          CustomText(
                                            text: '1 Phần',
                                            size: 13,
                                          ),
                                          CustomText(
                                            text: 'Thực dùng: 1',
                                            size: 13,
                                          ),
                                        ],
                                      ),
                                      CustomText(
                                        text: 'Ghi chú: chua vừa',
                                        size: 15,
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  width: double.infinity,
                                  decoration:
                                      BoxDecoration(color: AppColors.button),
                                  margin: EdgeInsets.fromLTRB(5, 5, 10, 0),
                                  padding: EdgeInsets.all(2),
                                  child: CustomText(
                                    text: 'Xong',
                                    color: Colors.white,
                                    bold: true,
                                    textAlign: TextAlign.center,
                                    maxLines: 3,
                                  ),
                                )
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 5,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      width: 27,
                                      height: 27,
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          border: Border.all(
                                              width: 1,
                                              color: AppColors.danger),
                                          borderRadius:
                                              BorderRadius.circular(180)),
                                      child: Center(
                                        child: Icon(
                                          Icons.remove,
                                          color: AppColors.danger,
                                          size: 19,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 29,
                                      height: 29,
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          border: Border.all(
                                              width: 1,
                                              color: AppColors.primary)),
                                      child: Center(
                                        child: CustomText(
                                          text: '0',
                                          bold: true,
                                          color: AppColors.primary,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 27,
                                      height: 27,
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          border: Border.all(
                                              width: 1,
                                              color: AppColors.button),
                                          borderRadius:
                                              BorderRadius.circular(180)),
                                      child: Center(
                                        child: Icon(
                                          Icons.add,
                                          color: AppColors.button,
                                          size: 19,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                CustomText(
                                  text: 'Trả món: 0',
                                  color: AppColors.danger,
                                  italic: true,
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
