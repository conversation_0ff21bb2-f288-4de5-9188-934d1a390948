// Thêm vào domain/usecases/order/update_status_usecase.dart
import '../../repositories/order/order_repository.dart';
import '../../entities/order/status_response_entity.dart';
import '../../entities/order/status_request_entity.dart';

class UpdateStatusUseCase {
  final OrderRepository repository;

  UpdateStatusUseCase({required this.repository});

  Future<StatusResponseEntity> call(StatusRequestEntity request) async {
    return await repository.updateStatus(request);
  }
}