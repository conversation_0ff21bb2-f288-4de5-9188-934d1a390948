import 'package:hive/hive.dart';

part 'menu_tree_model.g.dart';

@HiveType(typeId: 1)
class MenuTreeModel extends HiveObject {
  @HiveField(0)
  final String lastUpdatedAt;

  @HiveField(1)
  final Map<String, dynamic> fullJson;

  MenuTreeModel({
    required this.lastUpdatedAt,
    required this.fullJson,
  });

  factory MenuTreeModel.fromJson(Map<String, dynamic> json) {
    return MenuTreeModel(
      lastUpdatedAt: json['LastUpdatedAt'] ?? '',
      fullJson: json,
    );
  }

  Map<String, dynamic> toJson() => fullJson;
}
