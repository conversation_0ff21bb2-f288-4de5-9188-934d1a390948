import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_header_modal.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:gls_self_order/general/controllers/order_controller.dart';
import 'package:intl/intl.dart';

class CreateBillView extends StatefulWidget {
  final String orderCode;
  const CreateBillView({super.key, required this.orderCode});

  @override
  State<CreateBillView> createState() => _CreateBillViewState();
}

class _CreateBillViewState extends State<CreateBillView> {
  //variable
  OrderController orderController = Get.find();
  CustomerController customerController = Get.find();
  TextEditingController dateController = TextEditingController();
  TextEditingController customerNameController = TextEditingController();
  TextEditingController customerPhoneController = TextEditingController();
  TextEditingController customerAddressController = TextEditingController();
  TextEditingController companyNameController = TextEditingController();
  TextEditingController taxCodeController = TextEditingController();
  TextEditingController companyAddressController = TextEditingController();
  TextEditingController searchCustomerController = TextEditingController();
  TextEditingController noteController = TextEditingController();
  DateTime today = DateTime.now();
  DateTime invoiceDate = DateTime.now();
  int customerId = 0;
  String taxCodeError = ''; // Thêm biến để lưu lỗi mã số thuế
  String customerNameError = ''; // Thêm biến để lưu lỗi tên khách hàng
  List customerList = [];
  dynamic customerPagination = {
    'current_page': 1,
    'page_size': 50,
    'total_page': 0,
    'total_records': 0,
    'item_count': 0
  };
  List genders = [
    {'id': 1, 'name': 'Nam'},
    {'id': 2, 'name': 'Nữ'},
  ];

  //function
  @override
  void initState() {
    super.initState();
    getCustomerList(null);
    dateController.text = DateFormat('dd/MM/yyyy').format(invoiceDate);
      customerNameController.addListener(() {
    if (customerNameError.isNotEmpty &&
        customerNameController.text.trim().isNotEmpty) {
      setState(() {
        customerNameError = "";
      });
    }
  });
  }

  getCustomerList(keySearch) async {
    List resp = await customerController.getList(
        customerPagination['current_page'],
        customerPagination['page_size'],
        '2025-06-01',
        '2077-01-01',
        keySearch);
    customerList = resp[0];
    dynamic pagination = resp[1];
    if (pagination != null) {
      customerPagination['total_page'] = pagination['TotalPages'];
      customerPagination['total_records'] = pagination['TotalRecords'];
      customerPagination['item_count'] = pagination['ItemCount'];
    }
  }

  pickCustomer() async {
    dynamic customer = {
      'id': null,
      'name': null,
      'phone': null,
      'address': null
    };
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        useSafeArea: true,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, StateSetter setStateIn) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                child: Column(
                  children: [
                    CustomHeaderModal(
                      title: 'Chọn khách hàng',
                      append: InkWell(
                        onTap: () async {
                          dynamic customer_ = await addCustomer();
                          if (customer_ != null) {
                            customer = customer_;
                            Get.back();
                          }
                        },
                        child: Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(10),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: searchCustomerController,
                              showLabel: false,
                              hint: 'Tìm kiếm khách hàng',
                              space: false,
                            ),
                          ),
                          InkWell(
                            onTap: () async {
                              AppFunction.showLoading();
                              await getCustomerList(
                                  searchCustomerController.text);
                              setStateIn(() {});
                              AppFunction.hideLoading();
                            },
                            child: Container(
                              width: 45,
                              height: 45,
                              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
                              decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Icon(
                                Icons.search,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          for (dynamic item in customerList)
                            InkWell(
                              onTap: () {
                                customer = {
                                  'id': item['CustomerId'],
                                  'name': item['FullName'],
                                  'phone': item['PhoneNumber'],
                                  'address': item['CustomerAdress']
                                };
                                Get.back();
                              },
                              child: renderItemCustomer(item),
                            )
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          );
        });
    return customer;
  }

  Future<DateTime?> selectDateOfBirth(BuildContext context) async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: today,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (date != null) {
      DateTime dateOfBirth = DateTime(
        date.year,
        date.month,
        date.day,
      );
      return dateOfBirth;
    }
    return null;
  }

  searchCompany() async {
    if (taxCodeController.text.trim().isEmpty) {
      setState(() {
        taxCodeError = 'Vui lòng nhập mã số thuế';
      });
      return;
    }
    AppFunction.showLoading();

    dynamic result =
        await orderController.searchCompany(taxCodeController.text);

    if (result != null) {
      companyNameController.text = result['companyName'];
      companyAddressController.text = result['companyAddress'];
      setState(() {
        taxCodeError = orderController.texCodeError.value;
      });
    }
    AppFunction.hideLoading();
  }

  save() async {
    // Reset error trước khi validation
    setState(() {
      taxCodeError = '';
      customerNameError = '';
    });
    if (customerNameController.text.trim().isEmpty) {
      setState(() {
        customerNameError = 'Vui lòng nhập tên khách hàng';
      });
      return;
    }


    // Kiểm tra nếu mã số thuế trống
    if (taxCodeController.text.trim().isEmpty) {
      setState(() {
        taxCodeError = 'Vui lòng nhập mã số thuế';
      });
      return;
    }


    
    // Nếu có nhập mã số thuế, tự động kiểm tra bằng API
    if (taxCodeController.text.trim().isNotEmpty) {
      AppFunction.showLoading();
      dynamic item =
          await orderController.getSearchCompany(taxCodeController.text);
      AppFunction.hideLoading();

      if (item == null ||
          item['ten_cty'] == null ||
          item['ten_cty'].toString().trim().isEmpty) {
        // Không có tên công ty = không phải mã số thuế công ty
        setState(() {
          taxCodeError = 'Đây không phải mã số thuế công ty, kiểm tra lại';
        });
        return;
      }

      // Có tên công ty, tự động điền thông tin
      companyNameController.text = item['ten_cty'] ?? '';
      companyAddressController.text = item['dia_chi'] ?? '';
    }

    // Tiến hành lưu
    bool success = await orderController.postCreateInvoice(
        widget.orderCode,
        DateFormat('yyyy-MM-ddTHH:mm:ss').format(invoiceDate),
        customerId,
        customerNameController.text,
        customerPhoneController.text,
        customerAddressController.text,
        taxCodeController.text,
        companyNameController.text,
        companyAddressController.text,
        noteController.text);
    if (success) {
      Get.back(result: success);
    }
  }

  Widget renderItemCustomer(item) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Container(
              //   width: 50,
              //   height: 50,
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(180)
              //   ),
              //   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
              //   clipBehavior: Clip.antiAlias,
              //   child: Image.asset('assets/images/general/avatar.jpg'),
              // ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: CustomText(
                            text: item['FullName'] ?? '',
                            bold: true,
                            size: 18,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CustomText(text: '📞${item['PhoneNumber'] ?? ''}'),
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  addCustomer() async {
    TextEditingController nameController = TextEditingController();
    TextEditingController phoneController = TextEditingController();
    TextEditingController dobController = TextEditingController();
    TextEditingController addressController = TextEditingController();
    int gender = 1;
    DateTime? dateOfBirth;
    dynamic customer;
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        useSafeArea: true,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, StateSetter setStateIn) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                child: Column(
                  children: [
                    CustomHeaderModal(
                        title: 'Thêm khách hàng',
                        append: CustomSaveButton(
                          onTap: () async {
                            int customerId =
                                await customerController.postCreate(
                                    nameController.text,
                                    phoneController.text,
                                    dateOfBirth != null
                                        ? DateFormat('yyyy-MM-dd')
                                            .format(dateOfBirth!)
                                        : null,
                                    gender,
                                    addressController.text);
                            if (customerId != 0) {
                              customer = {
                                'id': customerId,
                                'name': nameController.text,
                                'phone': phoneController.text,
                                'address': addressController.text
                              };
                              Get.back();
                            }
                          },
                          hasPadding: false,
                        )),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          CustomTextField(
                            controller: nameController,
                            label: 'Họ tên',
                            required: true,
                          ),
                          CustomTextField(
                            controller: phoneController,
                            label: 'Số điện thoại',
                          ),
                          CustomTextField(
                            controller: dobController,
                            label: 'Ngày sinh',
                            readOnly: true,
                            onTap: () async {
                              dateOfBirth = await selectDateOfBirth(context);
                              if (dateOfBirth != null) {
                                dobController.text =
                                    AppFunction.formatDateNoTime(dateOfBirth);
                              }
                            },
                          ),
                          Padding(
                            padding: EdgeInsets.fromLTRB(0, 0, 0, 2.5),
                            child: CustomText(
                              text: 'Giới tính',
                            ),
                          ),
                          DecoratedBox(
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(7.5),
                                border: Border.all(
                                    width: 0.5,
                                    color:
                                        Colors.black.withValues(alpha: 0.2))),
                            child: DropdownButton(
                              value: gender,
                              items: [
                                for (dynamic item in genders)
                                  DropdownMenuItem(
                                    value: item['id'],
                                    child: CustomText(text: item['name']),
                                  )
                              ],
                              onChanged: (value) {
                                gender = int.parse(value.toString());
                                setStateIn(() {});
                              },
                              underline: Container(),
                              isExpanded: true,
                              padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          CustomTextField(
                            controller: addressController,
                            label: 'Địa chỉ',
                            maxLines: 3,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          );
        });
    return customer;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Xuất hoá đơn'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            CustomTextField(
              controller: dateController,
              label: 'Ngày',
              readOnly: true,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: customerNameController,
                    label: 'Khách hàng:',
                    readOnly: true,
                    onChanged: (value) {
                      if(customerNameError.isNotEmpty){
                        setState(() {
                          customerNameError = "";
                        });
                      }
                    },
                  ),
                ),
                
                if (customerNameController.text.isNotEmpty)
                  InkWell(
                    onTap: () {
                      customerId = 0;
                      customerNameController.text = '';
                      customerPhoneController.text = '';
                      customerAddressController.text = '';
                      setState(() {});
                    },
                    child: Container(
                      width: 30,
                      height: 30,
                      margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                      decoration: BoxDecoration(
                          color: AppColors.shadow,
                          borderRadius: BorderRadius.circular(10)),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  
                if (customerNameController.text.isEmpty)
                  InkWell(
                    onTap: () async {
                      dynamic customer = await pickCustomer();
                      customerId = customer['id'] ?? 0;
                      customerNameController.text = customer['name'];
                      customerPhoneController.text = customer['phone'];
                      customerAddressController.text = customer['address'];
                      setState(() {});
                    },
                    child: Container(
                      width: 30,
                      height: 30,
                      margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                      decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(10)),
                      child: Icon(
                        Icons.search,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            // Add this error display for customerNameError
            if (customerNameError.isNotEmpty)
              Padding(
                padding: EdgeInsets.fromLTRB(0, 5, 0, 10),
                child: CustomText(
                  text: customerNameError,
                  color: AppColors.danger,
                  size: 14,
                ),
              ),
            CustomTextField(
              controller: customerPhoneController,
              label: "Số điện thoại",
              readOnly: true,
            ),
            CustomTextField(
              controller: customerAddressController,
              label: "Địa chỉ",
              maxLines: 3,
              readOnly: true,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: CustomTextField(
                        controller: taxCodeController,
                        label: 'Mã số thuế:',
                        onChanged: (value) {
                          // Reset error khi người dùng thay đổi text
                          if (taxCodeError.isNotEmpty) {
                            setState(() {
                              taxCodeError = '';
                            });
                          }
                        },
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        searchCompany();
                      },
                      child: Container(
                        width: 30,
                        height: 30,
                        margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                        decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(10)),
                        child: Icon(
                          Icons.search,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                // Hiển thị lỗi nếu có
                if (taxCodeError.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.fromLTRB(0, 5, 0, 10),
                    child: CustomText(
                      text: taxCodeError,
                      color: AppColors.danger,
                      size: 14,
                    ),
                  ),
              ],
            ),
            CustomTextField(
              controller: companyNameController,
              label: "Tên doanh nghiệp",
            ),
            CustomTextField(
              controller: companyAddressController,
              label: "Địa chỉ doanh nghiệp",
              maxLines: 3,
            ),
            CustomTextField(
              controller: noteController,
              label: "Ghi chú",
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }
}
