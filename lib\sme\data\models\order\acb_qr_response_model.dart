class ACBQRResponseModel {
  ACBQRResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.result,
  });

  final bool success;
  final int code;
  final String message;
  final ACBQRResultModel? result;

  factory ACBQRResponseModel.fromJson(Map<String, dynamic> json){
    return ACBQRResponseModel(
      success: json["Success"],
      code: json["Code"],
      message: json["Message"],
      result: json["Result"] == null ? null : ACBQRResultModel.fromJson(json["Result"]),
    );
  }

}

class ACBQRResultModel {
  ACBQRResultModel({
    required this.date,
    required this.qrPay,
  });

  final DateTime? date;
  final String qrPay;

  factory ACBQRResultModel.fromJson(Map<String, dynamic> json){
    return ACBQRResultModel(
      date: DateTime.tryParse(json["date"] ?? ""),
      qrPay: json["qrPay"],
    );
  }

}

