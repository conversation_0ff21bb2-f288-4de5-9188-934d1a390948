import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';

class GeneralController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();

  //function
  getPaymentMethodList() async {
    List list = [];
    try {
      final response = await dioClient.get(
          SmeUrl.paymentMethodList,
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
    }

    return list;
  }

  getItemList(type) async {
    List list = [];
    try {
      final response = await dioClient.get(
        SmeUrl.receiptItemList,
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        for (dynamic item in data['Result']) {
          if (item['ReceiptItemType'] == type) {
            list.add(item);
          }
        }
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
    }

    return list;
  }

  getGroupList(type) async {
    List list = [];
    try {
      final response = await dioClient.get(
        SmeUrl.receiptGroupList,
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        for (dynamic item in data['Result']) {
          if (item['ReceiptGroupType'] == type) {
            list.add(item);
          }
        }
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
    }

    return list;
  }

  getBusinessTypeList() async {
    List list = [];
    try {
      final response = await dioClient.get(
        SmeUrl.businessTypeList,
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
    }

    return list;
  }
}