import '../../entities/order/order_reponse_entity.dart';
import '../../entities/order/order_request_entity.dart';
import '../../repositories/order/order_repository.dart';

class CreateOrderUseCase {
  final OrderRepository repository;

  CreateOrderUseCase({required this.repository});

  Future<OrderResponseEntity> call(OrderRequestEntity request) async {
    return await repository.createOrder(request);
  }
}