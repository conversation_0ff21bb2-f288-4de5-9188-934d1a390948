// notification_model.dart
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final String type;
  final int timestamp;
  final bool isRead;
  final Map<String, dynamic> data;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.timestamp,
    required this.data,
    this.isRead = false,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      type: json['extra_data']['type'] ?? 'system',
      timestamp: json['timestamp'] ?? 0,
      data: json['extra_data'] ?? {},
      isRead: json['isRead'] ?? false,
    );
  }

  NotificationModel copyWith({
    bool? isRead,
  }) {
    return NotificationModel(
      id: id,
      title: title,
      body: body,
      type: type,
      timestamp: timestamp,
      data: data,
      isRead: isRead ?? this.isRead,
    );
  }
}