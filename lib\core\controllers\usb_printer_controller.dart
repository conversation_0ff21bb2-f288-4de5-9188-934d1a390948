import 'dart:typed_data';

import 'package:drago_usb_printer/drago_usb_printer.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/services/capture_bill_service.dart';
import 'package:gls_self_order/core/services/capture_qr_service.dart';
import 'package:image/image.dart' as img;

class UsbPrinterController extends GetxController {
  //variable
  DragoUsbPrinter dragoUsbPrinter = DragoUsbPrinter();
  RxBool isConnected = false.obs;
  RxList devices = [].obs;

  //function
  getDeviceList() async {
    List<Map<String, dynamic>> results = [];
    results = await DragoUsbPrinter.getUSBDeviceList();
    devices.value = results;
  }

  connect(device) async {
    int vendorId = int.parse(device['vendorId']);
    int productId = int.parse(device['productId']);
    bool? connected = await dragoUsbPrinter.connect(vendorId, productId);
    if (connected ?? false) {
      AppFunction.showSuccess('Kết nối thành công');
      isConnected.value = true;
      GlobalVar.typePrinter = 'usb';
    }
    else {
      AppFunction.showError('Kết nối thất bại');
    }
  }

  printBill(decoded) async {
    if (isConnected.value) {
      List<int> ticket = await billData(decoded);
      Uint8List dataToSend = Uint8List.fromList(ticket);
      final result = await dragoUsbPrinter.write(dataToSend);
    } else {
      AppFunction.showError('Máy in chưa được kết nối');
    }
  }

  billData(decoded) async{
    List<int> bytes = [];
    // Using default profile
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);
    bytes += generator.reset();

    if (decoded != null) {
      bytes += generator.image(decoded, align: PosAlign.center);
    }

    bytes += generator.feed(1);
    bytes += generator.cut();
    return bytes;
  }

  printQr() async {
    if (isConnected.value) {
      List<int> ticket = await qrData();
      Uint8List dataToSend = Uint8List.fromList(ticket);
      final result = await dragoUsbPrinter.write(dataToSend);
    } else {
      Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
    }
  }

  qrData() async {
    List<int> bytes = [];
    // Using default profile
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);
    bytes += generator.reset();

    final pngBytes = await captureQrImage();
    final decoded = img.decodeImage(pngBytes);
    if (decoded != null) {
      bytes += generator.image(decoded, align: PosAlign.center);
    }

    bytes += generator.feed(1);
    bytes += generator.cut();
    return bytes;
  }
}