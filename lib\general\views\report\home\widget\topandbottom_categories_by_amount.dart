import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/helper/format_helper.dart';
import '../../domain/report/entities/report.dart';

class TopAndBottomCategoriesByAmount extends StatelessWidget {
  final double width;
  final List<CategoriesByAmountEntity> categories;
  final String title;

  const TopAndBottomCategoriesByAmount({
    super.key,
    required this.width,
    required this.categories,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    List<CategoriesByAmountEntity> sortedCategories = [...categories];
    sortedCategories.sort((a, b) => (b.totalAmount ?? 0).compareTo(a.totalAmount ?? 0));

    double totalRevenue = sortedCategories.fold(0.0, (sum, e) => sum + (e.totalAmount ?? 0));
    bool isEmptyData = totalRevenue == 0;

    List<Color> colors = [
      Colors.blue,
      Colors.orange,
      Colors.pinkAccent,
      Colors.green,
      Colors.purpleAccent,
    ];

    List<double> calculatedPercentages = [];

    List<PieChartSectionData> sections = isEmptyData
        ? [
      PieChartSectionData(
        value: 1,
        color: Colors.grey.withOpacity(0.3),
        radius: width * 0.12,
        title: "0%",
        titleStyle: TextStyle(fontSize: width * 0.03, color: Colors.white),
      ),
    ]
        : sortedCategories.asMap().entries.map((entry) {
      int index = entry.key;
      var category = entry.value;
      double revenue = category.totalAmount ?? 0;
      double percentage = (revenue / totalRevenue) * 100;
      calculatedPercentages.add(percentage);

      return PieChartSectionData(
        value: revenue,
        title: "${percentage.toStringAsFixed(1)}%",
        color: colors[index % colors.length],
        radius: width * 0.12,
        titleStyle: TextStyle(fontSize: width * 0.03, color: Colors.white),
      );
    }).toList();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Container(
        padding: EdgeInsets.all(width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                title.toUpperCase(),
                style: PrimaryFont.bold.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.04,
                ),
              ),
            ),
            SizedBox(height: width * 0.02),
            SizedBox(
              height: width * 0.5,
              child: PieChart(
                PieChartData(
                  sections: sections,
                  borderData: FlBorderData(show: false),
                  centerSpaceRadius: width * 0.12,
                  sectionsSpace: 2,
                ),
              ),
            ),
            SizedBox(height: width * 0.02),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: isEmptyData
                  ? [
                Text(
                  "Không có dữ liệu",
                  style: PrimaryFont.regular.copyWith(
                    color: AppColors.text,
                    fontSize: width * 0.03,
                  ),
                ),
              ]
                  : sortedCategories.asMap().entries.map((entry) {
                int index = entry.key;
                var category = entry.value;
                double percentage = calculatedPercentages[index];

                return Padding(
                  padding: EdgeInsets.symmetric(vertical: width * 0.01),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: width * 0.03,
                          height: width * 0.03,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: colors[index % colors.length],
                          ),
                        ),
                        SizedBox(width: width * 0.015),
                        Text(
                          "${category.groupName} - ${FormatHelper.formatCurrency(category.totalAmount ?? 0)} - ${category.totalQuantity ?? 0} phần - ${percentage.toStringAsFixed(1)}%",
                          style: PrimaryFont.regular.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.03,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}