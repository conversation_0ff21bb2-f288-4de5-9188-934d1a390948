import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

class DeviceInfo {
  static Future<String> getDeviceId() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    try {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        final androidId = androidInfo.id ?? 'android-unknown';
        print('📱 ANDROID ID: $androidId');
        return androidId;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        final iosId = iosInfo.identifierForVendor ?? 'ios-unknown';
        print('📱 IOS ID: $iosId');
        return iosId;
      }
    } catch (e) {
      print('Error getting device ID: $e');
    }

    print('Using fallback device ID');
    return 'unknown-device-${DateTime.now().millisecondsSinceEpoch}';
  }
}