import 'package:intl/intl.dart';

// Dashboard
class OverviewRevenueModel {
  OverviewRevenueModel({
    required this.paymentAmount,
    required this.itemAmount,
    required this.discountAmount,
    required this.totalBill,
    required this.vatAmount,
    required this.totalInvoice,
  });

  final double? paymentAmount;
  final double? itemAmount;
  final double? discountAmount;
  final double? totalBill;
  final double? vatAmount;
  final double? totalInvoice;

  factory OverviewRevenueModel.fromJson(Map<String, dynamic> json){
    return OverviewRevenueModel(
      paymentAmount: _parseDouble(json['PaymentAmount']),
      itemAmount: _parseDouble(json['ItemAmount']),
      discountAmount: _parseDouble(json['DiscountAmount']),
      totalBill: _parseDouble(json['TotalBill']),
      vatAmount: _parseDouble(json['VATAmount']),
      totalInvoice: _parseDouble(json['TotalInvoice']),
    );
  }
  static double? _parseDouble(dynamic value) {
    if (value == null || value is Map)
      return 0.0;
    return (value is num)
        ? value.toDouble()
        : double.tryParse(value.toString()) ?? 0.0;
  }

  static int? _parseInt(dynamic value) {
    if (value == null || value is Map)
      return 0;
    return (value is int) ? value : int.tryParse(value.toString()) ?? 0;
  }

}

class TopBottomBranchesRevenueModel {
  TopBottomBranchesRevenueModel({
    required this.topBranchesRevenue,
    required this.bottomBranchesRevenue,
  });

  final List<BranchesRevenueModel> topBranchesRevenue;
  final List<BranchesRevenueModel> bottomBranchesRevenue;

  factory TopBottomBranchesRevenueModel.fromJson(Map<String, dynamic> json){
    return TopBottomBranchesRevenueModel(
      topBranchesRevenue: json["TopBranchesRevenue"] == null ? [] : List<BranchesRevenueModel>.from(json["TopBranchesRevenue"]!.map((x) => BranchesRevenueModel.fromJson(x))),
      bottomBranchesRevenue: json["BottomBranchesRevenue"] == null ? [] : List<BranchesRevenueModel>.from(json["BottomBranchesRevenue"]!.map((x) => BranchesRevenueModel.fromJson(x))),
    );
  }

}

class BranchesRevenueModel {
  BranchesRevenueModel({
    required this.branchId,
    required this.branchName,
    required this.paymentAmount,
    required this.paidQuantity,
    required this.itemAmount,
    required this.discountAmount,
    required this.vatAmount,
    required this.svcAmount,
    required this.canceledAmount,
    required this.customerQty,
    required this.unPaidAmount,
    required this.focAmount,
    required this.avgPayment,
    required this.orderBranch,
  });

  final int? branchId;
  final String? branchName;
  final double? paymentAmount;
  final int? paidQuantity;
  final double? itemAmount;
  final int? discountAmount;
  final int? vatAmount;
  final int? svcAmount;
  final int? canceledAmount;
  final int? customerQty;
  final int? unPaidAmount;
  final double? focAmount;
  final double? avgPayment;
  final String? orderBranch;


  factory BranchesRevenueModel.fromJson(Map<String, dynamic> json) {
    return BranchesRevenueModel(
      canceledAmount: _parseInt(json["CanceledAmount"]),
      branchName: json["BranchName"],
      svcAmount: _parseInt(json["SvcAmount"]),
      itemAmount: _parseDouble(json["ItemAmount"]),
      focAmount: _parseDouble(json["FOCAmount"]),
      branchId: _parseInt(json["BranchId"]),
      customerQty: _parseInt(json["CustomerQty"]),
      paidQuantity: _parseInt(json["PaidQuantity"]),
      avgPayment: _parseDouble(json["AVGPayment"]),
      discountAmount: _parseInt(json["DiscountAmount"]),
      orderBranch: json["OrderBranch"],
      unPaidAmount: _parseInt(json["UnPaidAmount"]),
      vatAmount: _parseInt(json["VATAmount"]),
      paymentAmount: _parseDouble(json["PaymentAmount"]),
    );
  }

  static int _parseInt(dynamic value, {int defaultValue = 0}) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    return int.tryParse(value.toString()) ?? defaultValue;
  }

  static double _parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    return double.tryParse(value.toString()) ?? defaultValue;
  }
}

class Revenue7DaysModel {
  Revenue7DaysModel({
    required this.rDay,
    required this.paymentAmount,
  });

  final String? rDay;
  final int? paymentAmount;

  factory Revenue7DaysModel.fromJson(Map<String, dynamic> json) {
    return Revenue7DaysModel(
      rDay: json["R_Day"],
      paymentAmount: (json["PaymentAmount"] as num?)?.toInt(),
    );
  }
}

class TopBranchesRevenueByDateModel {
  TopBranchesRevenueByDateModel({
    required this.revenueDate,
    required this.revenues,
  });

  final DateTime? revenueDate;
  final Map<String, double> revenues;

  factory TopBranchesRevenueByDateModel.fromJson(Map<String, dynamic> json) {
    final dateStr = json['RevenueDate'] as String?;
    final date = dateStr != null
        ? DateFormat('dd/MM/yyyy').parse(dateStr)
        : null;

    final Map<String, double> revenues =
    Map<String, dynamic>.from(json['Revenues'] as Map).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
    );

    return TopBranchesRevenueByDateModel(
      revenueDate: date,
      revenues: revenues,
    );
  }


}

// By Item
class TopAndBottomItemsRevenueModel {
  TopAndBottomItemsRevenueModel({
    required this.topItemsByTotalQuantity,
    required this.topItemsByTotalAmount,
    required this.bottomItems,
  });

  final List<Item> topItemsByTotalQuantity;
  final List<Item> topItemsByTotalAmount;
  final List<Item> bottomItems;

  factory TopAndBottomItemsRevenueModel.fromJson(Map<String, dynamic> json) {
    return TopAndBottomItemsRevenueModel(
      topItemsByTotalQuantity: json["TopItemsByTotalQuantity"] == null
          ? []
          : List<Item>.from(
          json["TopItemsByTotalQuantity"]!.map((x) => Item.fromJson(x))),
      topItemsByTotalAmount: json["TopItemsByTotalAmount"] == null
          ? []
          : List<Item>.from(
          json["TopItemsByTotalAmount"]!.map((x) => Item.fromJson(x))),
      bottomItems: json["BottomItems"] == null
          ? []
          : List<Item>.from(json["BottomItems"]!.map((x) => Item.fromJson(x))),
    );
  }
}
class Item {
  Item({
    required this.itemName,
    required this.totalQuantity,
    required this.totalAmount,
  });

  final String? itemName;
  final double? totalQuantity;
  final double? totalAmount;

  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      itemName: json["ItemName"],
      totalQuantity: (json["TotalQuantity"] as num?)?.toDouble(),
      totalAmount: (json["TotalAmount"] as num?)?.toDouble(),
    );
  }

}

class TopItemsRevenueByDateModel {
  TopItemsRevenueByDateModel({
    required this.revenueDate,
    required this.revenues,
  });

  final DateTime? revenueDate;
  final Map<String, double> revenues;

  factory TopItemsRevenueByDateModel.fromJson(Map<String, dynamic> json) {
    return TopItemsRevenueByDateModel(
      revenueDate: _parseDate(json["RevenueDate"] ?? ""),
      revenues: (json["Revenues"] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, (value as num).toDouble()),
      ) ??
          {},
    );
  }

  static DateTime? _parseDate(String dateString) {
    try {
      return DateFormat('M/d/yyyy h:mm:ss a').parse(dateString);
    } catch (e) {
      return null;
    }
  }
}

// By Category
class TopAndBottomCategoriesByAmountModel {
  TopAndBottomCategoriesByAmountModel({
    required this.topCategoriesByQuantity,
    required this.topCategoriesByAmount,
    required this.bottomCategoriesByAmount,
  });

  final List<CategoriesByAmountModel> topCategoriesByQuantity;
  final List<CategoriesByAmountModel> topCategoriesByAmount;
  final List<CategoriesByAmountModel> bottomCategoriesByAmount;

  factory TopAndBottomCategoriesByAmountModel.fromJson(Map<String, dynamic> json) {
    return TopAndBottomCategoriesByAmountModel(
      topCategoriesByQuantity: json["TopItemGroupsByTotalQuantity"] == null
          ? []
          : List<CategoriesByAmountModel>.from(json["TopItemGroupsByTotalQuantity"]!
          .map((x) => CategoriesByAmountModel.fromJson(x))),
      topCategoriesByAmount: json["TopItemGroupsByTotalAmount"] == null
          ? []
          : List<CategoriesByAmountModel>.from(json["TopItemGroupsByTotalAmount"]!
          .map((x) => CategoriesByAmountModel.fromJson(x))),
      bottomCategoriesByAmount: json["BottomItemGroups"] == null
          ? []
          : List<CategoriesByAmountModel>.from(json["BottomItemGroups"]!
          .map((x) => CategoriesByAmountModel.fromJson(x))),
    );
  }
}

class CategoriesByAmountModel {
  CategoriesByAmountModel({
    required this.groupName,
    required this.totalQuantity,
    required this.totalAmount,
  });

  final String? groupName;
  final double? totalQuantity;
  final double? totalAmount;

  factory CategoriesByAmountModel.fromJson(Map<String, dynamic> json) {
    return CategoriesByAmountModel(
      groupName: json["GroupName"] ?? "Không có tên",
      totalQuantity: (json["TotalQuantity"] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json["TotalAmount"] as num?)?.toDouble() ?? 0.0,
    );
  }
}

class TopCatsRevenueByDateModel {
  TopCatsRevenueByDateModel({
    required this.revenueDate,
    required this.revenues,
  });

  final DateTime? revenueDate;
  final Map<String, double> revenues;

  factory TopCatsRevenueByDateModel.fromJson(Map<String, dynamic> json) {
    DateTime? parsedDate;
    final rawDate = json["RevenueDate"] as String?;
    if (rawDate != null) {
      try {
        parsedDate = DateFormat("M/d/yyyy hh:mm:ss a").parse(rawDate);
      } catch (_) {
        try {
          parsedDate = DateFormat("dd/MM/yyyy").parse(rawDate);
        } catch (_) {
          parsedDate = null;
        }
      }
    }

    final revenues = (json["Revenues"] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
    ) ?? {};

    return TopCatsRevenueByDateModel(
      revenueDate: parsedDate,
      revenues: revenues,
    );
  }
}

// By Payment
class TotalPaymentAmountModel {
  TotalPaymentAmountModel({
    required this.payName,
    required this.paymentAmount,
    required this.percent,
  });

  final String? payName;
  final double? paymentAmount;
  final double? percent;

  factory TotalPaymentAmountModel.fromJson(Map<String, dynamic> json){
    return TotalPaymentAmountModel(
      payName: json["PayName"],
      paymentAmount:  json["PaymentAmount"],
      percent: json["Percent"],
    );
  }

}

class TotalPaymentAmountByDateModel {
  TotalPaymentAmountByDateModel({
    required this.paymentDate,
    required this.payments,
  });

  final DateTime? paymentDate;
  final Map<String, double> payments;

  factory TotalPaymentAmountByDateModel.fromJson(Map<String, dynamic> json) {
    return TotalPaymentAmountByDateModel(
      paymentDate: _parseDate(json["PaymentDate"]),
      payments: (json["Payments"] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, (value as num).toDouble()),
      ) ?? {},
    );
  }

  static DateTime? _parseDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return null;

    try {
      return DateFormat("M/d/yyyy hh:mm:ss a").parse(dateStr);
    } catch (e) {
      print("⚠️ Lỗi parse ngày: $dateStr");
      return null;
    }
  }
}