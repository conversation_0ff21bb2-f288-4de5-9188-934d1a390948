class ZALOQRResponseEntity {
  final bool success;
  final int code;
  final String message;
  final ZALOQRResultEntity? result;

  ZALOQRResponseEntity({
    required this.success,
    required this.code,
    required this.message,
    this.result,
  });
}

class ZALOQRResultEntity {
  final int returnCode;
  final String returnMessage;
  final int subReturnCode;
  final String subReturnMessage;
  final String zpTransToken;
  final String orderUrl;
  final String orderToken;
  final String qrCode;

  ZALOQRResultEntity({
    required this.returnCode,
    required this.returnMessage,
    required this.subReturnCode,
    required this.subReturnMessage,
    required this.zpTransToken,
    required this.orderUrl,
    required this.orderToken,
    required this.qrCode,
  });
}