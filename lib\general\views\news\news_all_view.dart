import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/news_controller.dart';
import 'package:gls_self_order/general/views/news/news_detail_view.dart';

class NewsAllView extends StatefulWidget {
  const NewsAllView({super.key});

  @override
  State<NewsAllView> createState() => _NewsAllViewState();
}

class _NewsAllViewState extends State<NewsAllView> {
  //variable
  NewsController newsController = Get.find();
  List newsList = [];

  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    newsList = await newsController.getNews();
    setState(() {

    });
  }

  Widget renderNewsItem(item) {
    return InkWell(
      onTap: () {
        Get.to(() => NewsDetailView(id: item['ID']));
      },
      child: Container(
        width: double.infinity,
        height: 150,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
                color: Colors.grey.withValues(alpha: 0.05),
                spreadRadius: 0,
                blurRadius: 1,
                offset: Offset(0, 3)
            ),
          ],
        ),
        clipBehavior: Clip.antiAlias,
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
                width: 150,
                height: 150,
                child: item['CoverImageURL'].isNotEmpty
                    ? Image.network(item['CoverImageURL'], fit: BoxFit.cover,)
                    : Image.asset('assets/images/general/no_image.jpg')
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: CustomText(text: item['Title'] ?? '', bold: true, maxLines: 5,),
                    ),
                    Row(
                      children: [
                        Icon(Icons.calendar_month),
                        Expanded(child: CustomText(text: AppFunction.formatDateNoTime(item['UpdatedDate'])))
                      ],
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Tin tức - Khuyến mãi'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            for (dynamic item in newsList)
              renderNewsItem(item)
          ],
        ),
      ),
    );
  }
}
