import 'package:flutter/material.dart';

class LeftArrowPainter extends CustomPainter {
  final Color color;
  LeftArrowPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width, 0);           // điểm trên cùng
    path.lineTo(0, size.height / 2);      // điểm giữa bên trái
    path.lineTo(size.width, size.height); // điểm dưới cùng
    path.close();

    canvas.drawPath(path, paint);

    final borderPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke;
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}