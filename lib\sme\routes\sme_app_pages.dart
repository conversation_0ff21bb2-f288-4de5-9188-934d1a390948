import 'package:get/get.dart';
import 'package:gls_self_order/sme/routes/sme_app_routes.dart';
import '../presentation/check_out/check_out_binding.dart';
import '../presentation/check_out/check_out_page.dart';
import '../presentation/e_menu/cart/cart_binding.dart';
import '../presentation/e_menu/e_menu_binding.dart';
import '../presentation/e_menu/e_menu_page.dart';
import '../presentation/e_menu/product_detail/product_detail_binding.dart';
import '../presentation/e_menu/product_detail/product_detail_page.dart';

class SmeAppPages {
  static final pages = [
    GetPage(
      name: SmeAppRoutes.menu,
      page: () => EMenuPage(),
      binding: EMenuBinding(),
    ),

    GetPage(
      name: SmeAppRoutes.productDetail,
      page: () => const ProductDetailPage(),
      binding: ProductDetailBinding(),
    ),
    GetPage(
      name: SmeAppRoutes.checkout,
      page: () => CheckoutPage(),
      bindings: [
        CartSMEBinding(),
        CheckoutBinding(),
      ]
    ),
  ];
} 