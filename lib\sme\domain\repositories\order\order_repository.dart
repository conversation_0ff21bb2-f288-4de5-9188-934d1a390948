import '../../entities/order/acb_qr_response_entity.dart';
import '../../entities/order/order_reponse_entity.dart';
import '../../entities/order/order_request_entity.dart';
import '../../entities/order/status_request_entity.dart';
import '../../entities/order/status_response_entity.dart';
import '../../entities/order/zalo_qr_response_entity.dart';

abstract class OrderRepository {
  Future<OrderResponseEntity> createOrder(OrderRequestEntity request);

  Future<ZALOQRResponseEntity> generateZALOQR(String orderCode);

  Future<ACBQRResponseEntity> generateACBQR(String orderCode);

  Future<StatusResponseEntity> updateStatus(StatusRequestEntity request);
}