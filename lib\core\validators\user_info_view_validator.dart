class UserInfoViewValidator {
  static String? validatorEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập email';
    }
    return null;
  }
  static String? validatorPhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập số điện thoại';
    }
    return null;
  }

  static String? validatorAddress(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập địa chỉ';
    }
    return null;
  }

  static String? validatorBranchName(String? value){
    if(value == null || value.isEmpty){
      return 'Vui lòng nhập tên chi nhánh';
    }
    return null;
  }

  static String? validatorObjectName(String? value){
    if(value == null || value.isEmpty){
      return 'Vui lòng nhập tên tài khoản';
    }
    return null;
  }
  
}