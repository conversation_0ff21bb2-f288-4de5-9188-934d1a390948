import 'package:flutter/animation.dart';
import 'package:get/get.dart';

import '../../domain/usecases/banner/banner_usecase.dart';

class BannerController extends GetxController {
  final BannerUseCase bannerUseCase;

  late AnimationController rippleController;
  late Animation<double> rippleAnimation;

  late AnimationController handController;
  late Animation<Offset> handOffsetAnimation;

  RxList<String> bannerImages = RxList<String>();
  RxBool isLoading = true.obs;

  BannerController({
    required this.bannerUseCase,
  });
  @override
  void onInit() {
    super.onInit();
    _fetchBanners(); // gọi API tại đây
  }
  void initAnimations(TickerProvider vsync) {
    rippleController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: vsync,
    )..repeat();

    rippleAnimation = Tween<double>(begin: 0.0, end: 60.0).animate(
      CurvedAnimation(parent: rippleController, curve: Curves.easeOut),
    );

    handController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: vsync,
    )..repeat(reverse: true);

    handOffsetAnimation = Tween<Offset>(
      begin: const Offset(0, 0),
      end: const Offset(0, -0.05),
    ).animate(
      CurvedAnimation(parent: handController, curve: Curves.easeInOut),
    );
  }

  Future<void> _fetchBanners() async {
    try {
      isLoading(true);
      final banners = await bannerUseCase();
      bannerImages.value = banners
          .where((banner) => banner.fileUrl != null)
          .map((banner) => banner.fileUrl!)
          .toList();
    } catch (e) {
      Get.snackbar('Error', 'Failed to load banners');
    } finally {
      isLoading(false);
    }
  }

  @override
  void onClose() {
    rippleController.dispose();
    handController.dispose();
    super.onClose();
  }
}
