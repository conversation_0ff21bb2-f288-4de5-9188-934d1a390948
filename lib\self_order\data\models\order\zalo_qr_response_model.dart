class ZALOQRResponseModel {
  final bool success;
  final int code;
  final String message;
  final ZALOQRResultModel? result;

  ZALOQRResponseModel({
    required this.success,
    required this.code,
    required this.message,
    this.result,
  });

  factory ZALOQRResponseModel.fromJson(Map<String, dynamic> json) {
    return ZALOQRResponseModel(
      success: json['Success'] ?? false,
      code: json['Code'] ?? 0,
      message: json['Message'] ?? '',
      result: json['Result'] != null ? ZALOQRResultModel.fromJson(json['Result']) : null,
    );
  }
}

class ZALOQRResultModel {
  final int returnCode;
  final String returnMessage;
  final int subReturnCode;
  final String subReturnMessage;
  final String zpTransToken;
  final String orderUrl;
  final String orderToken;
  final String qrCode;

  ZALOQRResultModel({
    required this.returnCode,
    required this.returnMessage,
    required this.subReturnCode,
    required this.subReturnMessage,
    required this.zpTransToken,
    required this.orderUrl,
    required this.orderToken,
    required this.qrCode,
  });

  factory ZALOQRResultModel.fromJson(Map<String, dynamic> json) {
    return ZALOQRResultModel(
      returnCode: json['return_code'] ?? 0,
      returnMessage: json['return_message'] ?? '',
      subReturnCode: json['sub_return_code'] ?? 0,
      subReturnMessage: json['sub_return_message'] ?? '',
      zpTransToken: json['zp_trans_token'] ?? '',
      orderUrl: json['order_url'] ?? '',
      orderToken: json['order_token'] ?? '',
      qrCode: json['qr_code'] ?? '',
    );
  }
}