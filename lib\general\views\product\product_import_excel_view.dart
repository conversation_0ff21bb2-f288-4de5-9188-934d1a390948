import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/product_import_controller.dart';

class ProductImportExcelView extends StatefulWidget {
  const ProductImportExcelView({super.key});

  @override
  State<ProductImportExcelView> createState() => _ProductImportExcelViewState();
}

class _ProductImportExcelViewState extends State<ProductImportExcelView> {
  final ProductImportController controller = Get.put(ProductImportController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const CustomTitleAppBar(title: 'Import sản phẩm từ Excel'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: const CustomBackButton(),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // File picker section
            _buildFilePickerSection(),
            const SizedBox(height: 20),

            // Import results section
            Expanded(
              child: Obx(() {
                if (controller.importedData.isEmpty) {
                  return _buildEmptyState();
                } else {
                  return _buildImportResults();
                }
              }),
            ),

            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildFilePickerSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.primary, width: 2),
        borderRadius: BorderRadius.circular(12),
        color: AppColors.primary.withValues(alpha: 0.05),
      ),
      child: Column(
        children: [
          Icon(
            Icons.file_upload_outlined,
            size: 48,
            color: AppColors.primary,
          ),
          const SizedBox(height: 12),
          const CustomText(
            text: 'Chọn file Excel để import',
            size: 16,
            bold: true,
          ),
          const SizedBox(height: 8),
          const CustomText(
            text: 'Hỗ trợ định dạng .xlsx, .xls',
            size: 14,
            color: Colors.grey,
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: _showFormatGuide,
            child: const Text(
              'Xem hướng dẫn định dạng file',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
          const SizedBox(height: 16),
          CustomButton(
            text: 'Chọn file Excel',
            onTap: _pickExcelFile,
            color: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.table_chart_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const CustomText(
            text: 'Chưa có dữ liệu import',
            size: 16,
            color: Colors.grey,
          ),
          const SizedBox(height: 8),
          const CustomText(
            text: 'Vui lòng chọn file Excel để bắt đầu',
            size: 14,
            color: Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildImportResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Summary section
        _buildSummarySection(),
        const SizedBox(height: 16),

        // Data table
        Expanded(
          child: _buildDataTable(),
        ),
      ],
    );
  }

  Widget _buildSummarySection() {
    return Obx(() {
      final total = controller.importedData.length;
      final valid = controller.validData.length;
      final invalid = controller.invalidData.length;

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Expanded(
              child: _buildSummaryItem(
                'Tổng số dòng',
                total.toString(),
                Colors.blue,
              ),
            ),
            Expanded(
              child: _buildSummaryItem(
                'Dòng hợp lệ',
                valid.toString(),
                Colors.green,
              ),
            ),
            Expanded(
              child: _buildSummaryItem(
                'Dòng lỗi',
                invalid.toString(),
                Colors.red,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        CustomText(
          text: value,
          size: 24,
          bold: true,
          color: color,
        ),
        const SizedBox(height: 4),
        CustomText(
          text: label,
          size: 12,
          color: Colors.grey,
        ),
      ],
    );
  }

  Widget _buildDataTable() {
    return Obx(() {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SingleChildScrollView(
          child: DataTable(
            columnSpacing: 12,
            horizontalMargin: 8,
            headingRowColor: WidgetStateProperty.all(
                AppColors.primary.withValues(alpha: 0.1)),
            columns: const [
              DataColumn(label: CustomText(text: 'STT', bold: true)),
              DataColumn(label: CustomText(text: 'Barcode', bold: true)),
              DataColumn(label: CustomText(text: 'Tên sản phẩm', bold: true)),
              DataColumn(label: CustomText(text: 'Danh mục', bold: true)),
              DataColumn(label: CustomText(text: 'Đơn vị', bold: true)),
              DataColumn(label: CustomText(text: 'Giá vốn', bold: true)),
              DataColumn(label: CustomText(text: 'Giá bán', bold: true)),
              DataColumn(label: CustomText(text: 'VAT %', bold: true)),
              DataColumn(label: CustomText(text: 'Trạng thái', bold: true)),
            ],
            rows: controller.importedData.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isValid = controller.isValidRow(item);

              return DataRow(
                color: WidgetStateProperty.all(
                  isValid ? null : Colors.red.withValues(alpha: 0.1),
                ),
                cells: [
                  DataCell(CustomText(text: (index + 1).toString())),
                  DataCell(CustomText(text: item['barcode'] ?? '')),
                  DataCell(
                    SizedBox(
                      width: 150,
                      child: CustomText(
                        text: item['productName'] ?? '',
                        maxLines: 2,
                      ),
                    ),
                  ),
                  DataCell(CustomText(text: item['category'] ?? '')),
                  DataCell(CustomText(text: item['unit'] ?? '')),
                  DataCell(CustomText(
                      text: item['originalPrice']?.toString() ?? '')),
                  DataCell(CustomText(text: item['price']?.toString() ?? '')),
                  DataCell(
                      CustomText(text: item['vatPercent']?.toString() ?? '')),
                  DataCell(
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isValid ? Colors.green : Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: CustomText(
                        text: isValid ? 'Hợp lệ' : 'Lỗi',
                        color: Colors.white,
                        size: 12,
                        bold: true,
                      ),
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ),
      );
    });
  }

  Widget _buildActionButtons() {
    return Obx(() {
      if (controller.importedData.isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Hủy bỏ',
                onTap: () {
                  controller.clearData();
                },
                color: Colors.grey,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomButton(
                text: 'Lưu dữ liệu hợp lệ (${controller.validData.length})',
                onTap: controller.validData.isNotEmpty
                    ? () => _saveValidData()
                    : () {},
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> _pickExcelFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result != null) {
        File file = File(result.files.single.path!);
        await controller.importFromExcel(file);
      }
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể đọc file Excel: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _saveValidData() async {
    // Controller will handle navigation and success message
    await controller.saveValidData();
  }

  void _showFormatGuide() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const CustomText(
          text: 'Hướng dẫn định dạng file Excel',
          bold: true,
        ),

        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildFormatRow(
                  'A', 'Barcode', 'Mã vạch sản phẩm (có thể để trống)'),
              _buildFormatRow('B', 'Danh mục', 'Tên danh mục sản phẩm'),
              _buildFormatRow('C', 'Tên sản phẩm', 'Tên sản phẩm'),
              _buildFormatRow(
                  'D', 'Đơn vị tính', 'Đơn vị tính (cái, kg, lít...)'),
              _buildFormatRow('E', 'Giá vốn', 'Giá vốn (số)'),
              _buildFormatRow('F', 'Giá bán', 'Giá bán (số)'),
              _buildFormatRow('G', 'Đã bao gồm VAT', '0 hoặc 1'),
              _buildFormatRow('H', '% VAT', 'Phần trăm VAT (0-100)'),
              _buildFormatRow('I', 'Tình trạng', '0: Ngừng bán, 1: Đang bán'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange),
                ),
                child: const CustomText(
                  text: 'Lưu ý: Dòng đầu tiên sẽ được bỏ qua (header). '
                      'Các dòng trống sẽ được tự động bỏ qua.',
                  size: 12,
                  maxLines: 3,
                ),
              ),
            ],
          ),
        ),
        actions: [
          CustomButton(
            text: 'Đóng',
            onTap: () => Navigator.of(context).pop(),
            color: AppColors.primary,
          ),
          // TextButton(
          //   onPressed: () => Navigator.of(context).pop(),
          //   child: const CustomText(text: 'Đóng'),
          // ),
        ],
      ),
    );
  }

  Widget _buildFormatRow(String column, String name, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: CustomText(
                text: column,
                color: Colors.white,
                size: 12,
                bold: true,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: name,
                  bold: true,
                  size: 14,
                ),
                CustomText(
                  text: description,
                  size: 12,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
