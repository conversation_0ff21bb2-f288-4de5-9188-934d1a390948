import 'package:get/get.dart';

import '../../core/classes/app_function.dart';
import '../../core/constants/sme_url.dart';
import '../../core/network/dio_client.dart';
import '../classes/user_info.dart';

class RevenueController extends GetxController {
  final DioClientSME dioClient = DioClientSME();

  // Observable variables for the summary data
  RxDouble paymentAmount = 0.0.obs;
  RxDouble itemAmount = 0.0.obs;
  RxDouble discountAmount = 0.0.obs;
  RxInt totalBill = 0.obs;
  RxDouble vatAmount = 0.0.obs;
  RxInt totalInvoice = 0.obs;

  // Function to fetch revenue data
  Future<void> fetchRevenueData({required String dateFrom, required String dateTo}) async {
    try {
      final response = await dioClient.post(
          SmeUrl.revenueOverview,
          data: {
            "DateFrom": dateFrom,
            "DateTo": dateTo,
            "listBrandId": [UserInfo.branchId]
          }
      );

      dynamic data = response.data;
      if (data['Success']) {
        final result = data['Result'];
        paymentAmount.value = result['PaymentAmount']?.toDouble() ?? 0.0;
        itemAmount.value = result['ItemAmount']?.toDouble() ?? 0.0;
        discountAmount.value = result['DiscountAmount']?.toDouble() ?? 0.0;
        totalBill.value = result['TotalBill'] ?? 0;
        vatAmount.value = result['VATAmount']?.toDouble() ?? 0.0;
        totalInvoice.value = result['TotalInvoice'] ?? 0;
      } else {
        AppFunction.showError(data['Message'] ?? 'Failed to fetch revenue data');
      }
    } catch (e) {
      AppFunction.showError('Error fetching revenue data: ${e.toString()}');
    }
  }

  // Helper function to format currency
  String formatCurrency(double amount) {
    return AppFunction.formatMoney(amount);
  }
}
