import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class DeleteAccountView extends StatefulWidget {
  const DeleteAccountView({super.key});

  @override
  State<DeleteAccountView> createState() => _DeleteAccountViewState();
}

class _DeleteAccountViewState extends State<DeleteAccountView> {
  //variable
  bool confirm = false;
  //function

  sendRequest() async {
    if (!confirm) {
      AppFunction.showError('Vui lòng đồng ý với điều khoản');
    }
    else {
      AppFunction.showLoading();
      await Future.delayed(Duration(seconds: 2));
      AppFunction.hideLoading();
      AppFunction.showSuccess('Đã gửi yêu cầu xoá tài khoản, chúng tôi sẽ liên hệ với bạn trong vòng 24h để xác nhận thông tin');
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Yêu cầu xoá tài khoản'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [

        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(10),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        confirm = !confirm;
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 25,
                          height: 25,
                          margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 2,
                                  color: confirm ? AppColors.primary : AppColors.shadow
                              ),
                              borderRadius: BorderRadius.circular(5),
                              color: confirm ? AppColors.primary : Colors.white
                          ),
                          child: confirm ? Center(child: Icon(Icons.check, color: Colors.white, size: 22,)) : null,
                        ),
                        Expanded(
                          child: CustomText(text: 'Khi bạn thực hiện xoá tài khoản, toàn bộ dữ liệu liên quan bao gồm thông tin cá nhân, lịch sử hoạt động, cài đặt và các nội dung đã lưu sẽ bị xoá vĩnh viễn khỏi hệ thống và không thể khôi phục. Vui lòng cân nhắc kỹ trước khi tiếp tục.', bold: true, maxLines: 10,),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
              height: 50,
              child: CustomButton(text: 'Gửi yêu cầu', onTap: () {
                sendRequest();
              }, color: AppColors.danger),
            )
          ],
        ),
      ),
    );
  }
}
