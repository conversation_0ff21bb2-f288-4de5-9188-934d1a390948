// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'menu_tree_sme_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MenuTreeSMEModelAdapter extends TypeAdapter<MenuTreeSMEModel> {
  @override
  final int typeId = 2;

  @override
  MenuTreeSMEModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MenuTreeSMEModel(
      lastUpdatedAt: fields[0] as String,
      fullJson: (fields[1] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, MenuTreeSMEModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.lastUpdatedAt)
      ..writeByte(1)
      ..write(obj.fullJson);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MenuTreeSMEModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
