import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/views/auth/register_view.dart';

class RegisterTypeView extends StatefulWidget {
  const RegisterTypeView({super.key});

  @override
  State<RegisterTypeView> createState() => _RegisterTypeViewState();
}

class _RegisterTypeViewState extends State<RegisterTypeView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Đăng ký tài khoản'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomText(text: 'Bạn là?', bold: true, size: 24,),
            Padding(
              padding: EdgeInsets.fromLTRB(0, 10, 0, 20),
              child: InkWell(
                onTap: () {
                  Get.to(() => RegisterView(type: 'free',));
                },
                child: Container(
                  width: 270,
                  decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10)
                  ),
                  padding: EdgeInsets.all(20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomText(text: 'Khách hàng tự do', bold: true, size: 18,),
                      Icon(Icons.arrow_forward_ios)
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                Get.to(() => RegisterView(type: 'acb',));
              },
              child: Container(
                width: 270,
                decoration: BoxDecoration(
                    color: AppColors.button.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10)
                ),
                padding: EdgeInsets.all(20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CustomText(text: 'Khách hàng của ACB', bold: true, size: 18,),
                    Icon(Icons.arrow_forward_ios)
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
