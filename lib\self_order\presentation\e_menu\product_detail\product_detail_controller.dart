import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../cart/cart_controller.dart';

class ProductDetailController extends GetxController {
  final Map<String, dynamic> product;
  var selectedQuantity = 1.obs;
  var defaultItems = <Map<String, dynamic>>[].obs;
  var selectedChoices = <String, List<Map<String, dynamic>>>{}.obs;
  var choices = <Map<String, dynamic>>[].obs;
  var note = ''.obs;
  var isEditMode = false;
  var cartIndex = -1;

  final CartController cartController = Get.find<CartController>();

  ProductDetailController(dynamic productData) : product = _convertMap(productData) {
    debugPrint('Initial product data: $productData');
    debugPrint('Converted product: $product');

    // Xử lý edit mode
    // Xử lý edit mode
    if (Get.arguments != null && Get.arguments is Map) {
      final args = Get.arguments as Map;
      debugPrint('Arguments: $args');

      if (args['editMode'] == true) {
        isEditMode = true;
        cartIndex = args['cartIndex'] ?? -1;
        selectedQuantity.value = args['initialQuantity'] ?? 1;
        note.value = args['initialNote'] ?? '';

        // Đảm bảo product có đủ dữ liệu
        if (product.isEmpty) {
          product.addAll(_convertMap(args['product'] ?? {}));
        }

        product['ItemName'] = product['ItemName'] ?? args['ItemName'];
        product['Price'] = product['Price'] ?? args['Price'];
        product['ItemDesc'] = product['ItemDesc'] ?? args['ItemDesc'];
        product['ImageUrl'] = product['ImageUrl'] ?? args['ImageUrl'];

        // Xử lý defaultItems
        if (args['DefaultItems'] != null) {
          product['DefaultItems'] = args['DefaultItems'];
        }

        // Xử lý choices
        if (args['Choices'] != null) {
          product['Choices'] = args['Choices'];
        }

        // Khôi phục selected choices
        final initialSelectedChoices = args['initialSelectedChoices'];
        if (initialSelectedChoices != null && initialSelectedChoices is Map) {
          selectedChoices.clear();
          initialSelectedChoices.forEach((choiceId, items) {
            if (items is List) {
              selectedChoices[choiceId.toString()] = List<Map<String, dynamic>>.from(
                items.map((item) => _convertMap(item)),
              );
            }
          });
        }
      }
    }

    // Đảm bảo product có đầy đủ dữ liệu
    if (isEditMode && product.isEmpty && cartIndex >= 0) {
      final cartItem = cartController.getCartItem(cartIndex);
      if (cartItem != null) {
        product.addAll(_convertMap(cartItem['product']));
        debugPrint('Product data loaded from cart: $product');
      }
    }

    // Xử lý defaultItems
    if (product['DefaultItems'] != null && product['DefaultItems'] is List) {
      defaultItems.value = (product['DefaultItems'] as List)
          .map((item) => _convertMap(item))
          .toList();
      debugPrint('Default items loaded: ${defaultItems.length}');
    }

    // Xử lý Choices
    if (product['Choices'] != null && product['Choices'] is List) {
      choices.value = (product['Choices'] as List)
          .map((choice) => _convertMap(choice))
          .toList();

      // Convert cả Details trong mỗi Choice
      for (var choice in choices) {
        if (choice['Details'] != null && choice['Details'] is List) {
          choice['Details'] = (choice['Details'] as List)
              .map((detail) => _convertMap(detail))
              .toList();
        }
      }
      debugPrint('Choices loaded: ${choices.length}');
    }

    // Khởi tạo selectedChoices nếu chưa có
    if (selectedChoices.isEmpty) {
      for (var choice in choices) {
        final choiceId = choice['ChoiceId']?.toString();
        if (choiceId != null && !selectedChoices.containsKey(choiceId)) {
          selectedChoices[choiceId] = [];
        }
      }
      debugPrint('Initialized selected choices: $selectedChoices');
    }
  }
  // Hàm chuyển đổi Map<dynamic, dynamic> sang Map<String, dynamic>
  static Map<String, dynamic> _convertMap(dynamic originalMap) {
    if (originalMap == null) return {};

    // Nếu đã là Map<String, dynamic> thì return luôn
    if (originalMap is Map<String, dynamic>) {
      return originalMap;
    }

    // Convert từ Map<dynamic, dynamic> sang Map<String, dynamic>
    if (originalMap is Map) {
      return originalMap.cast<String, dynamic>();
    }

    return {};
  }

  void toggleChoiceItem(String choiceId, Map<String, dynamic> item) {
    try {
      if (choiceId.isEmpty || item.isEmpty) return;

      final currentItems = List<Map<String, dynamic>>.from(
          selectedChoices[choiceId] ?? []
      );

      final itemIndex = currentItems.indexWhere(
              (i) => i['ItemId']?.toString() == item['ItemId']?.toString()
      );

      if (itemIndex >= 0) {
        currentItems.removeAt(itemIndex);
      } else {
        final choice = choices.firstWhere(
              (c) => c['ChoiceId']?.toString() == choiceId,
          orElse: () => {},
        );

        final maxChoice = (choice['MaxChoice'] as int?) ?? 1;
        if (currentItems.length < maxChoice) {
          currentItems.add(Map<String, dynamic>.from(item));
        }
      }

      selectedChoices[choiceId] = currentItems;
      update();
    } catch (e) {
      debugPrint('Error in toggleChoiceItem: $e');
    }
  }

  void increaseQuantity() {
    selectedQuantity.value++;
  }

  void decreaseQuantity() {
    if (selectedQuantity.value > 1) {
      selectedQuantity.value--;
    }
  }

  void addToCart() {
    try {
      final Map<String, dynamic> selectedChoicesMap = {};
      selectedChoices.forEach((choiceId, items) {
        if (items.isNotEmpty) {
          selectedChoicesMap[choiceId] = List<Map<String, dynamic>>.from(items);
        }
      });

      // Đảm bảo product có Price hợp lệ
      if (product['Price'] == null) {
        debugPrint('Warning: Product price is null');
        product['Price'] = 0; // Gán giá trị mặc định nếu null
      }

      if (isEditMode && cartIndex >= 0) {
        final updatedProduct = Map<String, dynamic>.from(product);
        if (selectedChoicesMap.isNotEmpty) {
          updatedProduct['SelectedChoices'] = selectedChoicesMap;
        } else {
          updatedProduct.remove('SelectedChoices');
        }

        // Đảm bảo giữ lại các thông tin quan trọng từ product gốc
        updatedProduct['DefaultItems'] = product['DefaultItems'] ?? [];
        updatedProduct['Choices'] = product['Choices'] ?? [];

        cartController.cartItems[cartIndex] = {
          'product': updatedProduct,
          'quantity': selectedQuantity.value,
          'note': note.value.trim(),
        };

        cartController.calculateTotal();
        Get.back(result: true);
      } else {
        // Phần thêm mới
        final productToAdd = Map<String, dynamic>.from(product);
        if (selectedChoicesMap.isNotEmpty) {
          productToAdd['SelectedChoices'] = selectedChoicesMap;
        }

        cartController.addToCart(
          productToAdd,
          selectedQuantity.value,
          selectedChoices: selectedChoicesMap.isNotEmpty ? selectedChoicesMap : null,
          note: note.value.trim(),
        );
        Get.back();
      }
    } catch (e) {
      debugPrint('Error in addToCart: $e');
      // Có thể thêm thông báo lỗi cho người dùng ở đây
    }
  }

}