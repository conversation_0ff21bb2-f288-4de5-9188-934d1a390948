class LoginResponseModel {
  final bool success;
  final int code;
  final String message;
  final String result;
  final String errorDetail;

  LoginResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.result,
    required this.errorDetail,
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) {
    return LoginResponseModel(
      success: json['Success'] ?? false,
      code: json['Code'] ?? 0,
      message: json['Message'] ?? '',
      result: json['Result'] ?? '',
      errorDetail: json['ErrorDetail'] ?? '',
    );
  }
}