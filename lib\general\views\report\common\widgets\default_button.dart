import 'package:flutter/material.dart';
import '../../core/configs/theme/app_colors.dart';
import '../../core/configs/theme/app_style.dart';

class DefaultButton extends StatelessWidget {
  final VoidCallback onPress;
  final String title;
  final Color color;
  final double widthPercentage;
  final double heightPercentage;
  final Widget? child;
  final double borderRadius;
  final TextStyle? titleStyle;

  const DefaultButton({
    super.key,
    required this.onPress,
    required this.title,
    required this.color,
    this.widthPercentage = 1,
    this.heightPercentage = 1,
    this.child,
    this.borderRadius = 16,
    this.titleStyle,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonWidth = screenWidth * widthPercentage;
    final buttonHeight = screenWidth * heightPercentage;

    return InkWell(
      onTap: onPress,
      child: Container(
        width: buttonWidth,
        height: buttonHeight,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: child != null
            ? Align(
                alignment: Alignment.centerLeft,
                child: child,
              )
            : Center(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: titleStyle ??
                      PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                      ),
                ),
              ),
      ),
    );
  }
}
