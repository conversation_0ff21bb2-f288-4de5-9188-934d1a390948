import 'dart:io';
import 'package:excel/excel.dart' as excel;
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';

class ProductImportController extends GetxController {
  final ProductController productController = Get.find<ProductController>();

  // Observable lists
  final RxList<Map<String, dynamic>> importedData =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> validData = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> invalidData = <Map<String, dynamic>>[].obs;

  // Loading state
  final RxBool isLoading = false.obs;

  // Import data from Excel file
  Future<void> importFromExcel(File file) async {
    try {
      isLoading.value = true;
      AppFunction.showLoading();

      // Clear previous data
      clearData();

      // Read Excel file
      var bytes = file.readAsBytesSync();
      var excelFile = excel.Excel.decodeBytes(bytes);

      // Get first sheet
      var sheet = excelFile.tables.keys.first;
      var table = excelFile.tables[sheet];

      if (table == null) {
        throw Exception('Không thể đọc dữ liệu từ file Excel');
      }

      // Process rows (skip header row)
      for (int i = 1; i < table.maxRows; i++) {
        var row = table.rows[i];

        // Skip empty rows
        if (row.every((cell) =>
            cell?.value == null || cell!.value.toString().trim().isEmpty)) {
          continue;
        }

        // Extract data from row
        Map<String, dynamic> productData = {
          'barcode':
              _getCellValue(row, 0).isEmpty ? null : _getCellValue(row, 0),
          'category': _getCellValue(row, 1),
          'productName': _getCellValue(row, 2),
          'unit': _getCellValue(row, 3),
          'originalPrice': _parseNumber(_getCellValue(row, 4)) ?? 0.0,
          'price': _parseNumber(_getCellValue(row, 5)) ?? 0.0,
          'isVatIncluded': _parseBoolean(_getCellValue(row, 6)),
          'vatPercent': _parseNumber(_getCellValue(row, 7)) ?? 0.0,
          'status': (_parseNumber(_getCellValue(row, 8)) ?? 1.0).toInt(),
          'rowIndex': i + 1, // For error reporting
        };
        importedData.add(productData);
      }

      // Validate data
      _validateData();
    } catch (e) {
      AppFunction.showError('Không thể import file Excel: $e');
    } finally {
      isLoading.value = false;
      AppFunction.hideLoading();
    }
  }

  // Get cell value safely
  String _getCellValue(List<excel.Data?> row, int index) {
    if (index >= row.length || row[index] == null) {
      return '';
    }
    return row[index]!.value?.toString().trim() ?? '';
  }

  // Parse number from string
  double? _parseNumber(String value) {
    if (value.isEmpty) return null;

    // Remove common formatting but keep decimal point
    String cleanValue = value
        .replaceAll(',', '')
        .replaceAll(' ', '')
        .replaceAll('%', ''); // Remove % sign if present

    // Try to parse as double
    double? result = double.tryParse(cleanValue);
    return result;
  }

  // Parse boolean from string/number
  bool _parseBoolean(String value) {
    if (value.isEmpty) return false;

    // Check for common boolean representations
    String lowerValue = value.toLowerCase();
    if (lowerValue == 'true' || lowerValue == '1' || lowerValue == 'yes') {
      return true;
    }

    return false;
  }

  // Validate imported data
  void _validateData() {
    validData.clear();
    invalidData.clear();

    for (var item in importedData) {
      if (isValidRow(item)) {
        validData.add(item);
      } else {
        invalidData.add(item);
      }
    }
  }

  // Check if a row is valid
  bool isValidRow(Map<String, dynamic> item) {
    // Required fields validation
    if (item['productName'] == null ||
        item['productName'].toString().trim().isEmpty) {
      return false;
    }

    if (item['category'] == null ||
        item['category'].toString().trim().isEmpty) {
      return false;
    }

    if (item['unit'] == null || item['unit'].toString().trim().isEmpty) {
      return false;
    }

    // Price validation
    double price = (item['price'] as double?) ?? 0.0;
    if (price <= 0) {
      return false;
    }

    double originalPrice = (item['originalPrice'] as double?) ?? 0.0;
    if (originalPrice <= 0) {
      return false;
    }

    // VAT validation
    double vatPercent = (item['vatPercent'] as double?) ?? 0.0;
    if (vatPercent < 0 || vatPercent > 100) {
      return false;
    }

    // Barcode validation (optional but if provided, should not be empty)
    if (item['barcode'] != null && item['barcode'].toString().trim().isEmpty) {
      item['barcode'] = null; // Set to null if empty
    }

    return true;
  }

  // Get validation errors for a row
  List<String> getRowErrors(Map<String, dynamic> item) {
    List<String> errors = [];

    if (item['productName'] == null ||
        item['productName'].toString().trim().isEmpty) {
      errors.add('Tên sản phẩm không được để trống');
    }

    if (item['category'] == null ||
        item['category'].toString().trim().isEmpty) {
      errors.add('Danh mục không được để trống');
    }

    if (item['unit'] == null || item['unit'].toString().trim().isEmpty) {
      errors.add('Đơn vị tính không được để trống');
    }

    double price = (item['price'] as double?) ?? 0.0;
    if (price <= 0) {
      errors.add('Giá bán phải lớn hơn 0');
    }

    double originalPrice = (item['originalPrice'] as double?) ?? 0.0;
    if (originalPrice <= 0) {
      errors.add('Giá vốn phải lớn hơn 0');
    }

    double vatPercent = (item['vatPercent'] as double?) ?? 0.0;
    if (vatPercent < 0 || vatPercent > 100) {
      errors.add('VAT phải từ 0% đến 100%');
    }

    return errors;
  }

  // Save valid data to server
  Future<bool> saveValidData() async {
    try {
      AppFunction.showLoading();

      // Ensure ProductController is ready
      await productController.getSaleMenu();

      int successCount = 0;

      for (var item in validData) {
        try {
          // Debug: Check item data
          if (item['productName'] == null ||
              item['productName'].toString().trim().isEmpty) {
            continue; // Skip invalid items
          }

          // Safe conversion
          double price = 0.0;
          double originalPrice = 0.0;
          double vatPercent = 0.0;
          int status = 1;

          // Convert price
          if (item['price'] is int) {
            price = (item['price'] as int).toDouble();
          } else if (item['price'] is double) {
            price = item['price'] as double;
          }

          // Convert originalPrice
          if (item['originalPrice'] is int) {
            originalPrice = (item['originalPrice'] as int).toDouble();
          } else if (item['originalPrice'] is double) {
            originalPrice = item['originalPrice'] as double;
          }

          // Convert vatPercent
          if (item['vatPercent'] is int) {
            vatPercent = (item['vatPercent'] as int).toDouble();
          } else if (item['vatPercent'] is double) {
            vatPercent = item['vatPercent'] as double;
          }

          // Convert status
          if (item['status'] is double) {
            status = (item['status'] as double).toInt();
          } else if (item['status'] is int) {
            status = item['status'] as int;
          }

          // Call API to create product from import
          bool success = await productController.createProductFromImport(
            item['productName']?.toString() ?? '',
            item['category']?.toString() ?? '',
            item['unit']?.toString() ?? '',
            price,
            originalPrice,
            vatPercent,
            (item['isVatIncluded'] as bool?) ?? false,
            status,
            item['barcode']?.toString(),
          );

          if (success) {
            successCount++;
          }
        } catch (e) {
          // Continue with next item if error occurs
        }
      }

      AppFunction.hideLoading();

      if (successCount > 0) {
        // Close import screen and go back to ProductView
        Get.back(); // Close import screen
        Get.back(); // Go back to ProductView

        // Show single success message
        AppFunction.showSuccess(
            'Đã import thành công $successCount/${validData.length} sản phẩm');

        return true;
      } else {
        AppFunction.showError('Không có sản phẩm nào được lưu');

        return false;
      }
    } catch (e) {
      AppFunction.hideLoading();
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  // Clear all data
  void clearData() {
    importedData.clear();
    validData.clear();
    invalidData.clear();
  }

  @override
  void onClose() {
    clearData();
    super.onClose();
  }
}
