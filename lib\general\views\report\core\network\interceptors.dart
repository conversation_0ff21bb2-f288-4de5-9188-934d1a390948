import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart' as get_x;
import 'package:gls_self_order/general/views/auth/login_view.dart';

/// This interceptor is used to show request and response logs
class LoggerInterceptor extends Interceptor {
  Logger logger = Logger(printer: PrettyPrinter(methodCount: 0, colors: true, printEmojis: true));

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final options = err.requestOptions;
    final requestPath = options.path;
    logger.e('${options.method} request ==> $requestPath');
    logger.d('Error type: ${err.error} \n '
        'Error message: ${err.message} \n'
        'Request data: ${options.data}');

    // Check for -1403 error code in error response
    if (err.response?.data != null && err.response!.data is Map) {
      final data = err.response!.data as Map<String, dynamic>;
      if (data['Code'] == -1403 || data['code'] == -1403) {
        logger.e('Received -1403 error code in error response, redirecting to LoginView');
        _redirectToLogin();
      }
    }

    handler.next(err);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    logger.i('╔═══════════════════════════════════════════════════════════════════════');
    logger.i('╟ Request: ${options.method} ${options.uri}');
    logger.i('╟ Headers: ${options.headers}');
    if (options.data != null) {
      logger.i('╟ Body: ${options.data}');
    }
    if (options.queryParameters.isNotEmpty) {
      logger.i('╟ Query Parameters: ${options.queryParameters}');
    }
    logger.i('╚═══════════════════════════════════════════════════════════════════════');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    logger.d('STATUSCODE: ${response.statusCode} \n '
        'STATUSMESSAGE: ${response.statusMessage} \n'
        'HEADERS: ${response.headers} \n'
        'Data: ${response.data}'); // Debug log

    // Check for -1403 error code in response data
    if (response.data != null && response.data is Map) {
      final data = response.data as Map<String, dynamic>;
      if (data['Code'] == -1403 || data['code'] == -1403) {
        logger.e('Received -1403 error code, redirecting to LoginView');
        _redirectToLogin();
      }
    }

    handler.next(response); // continue with the Response
  }

  void _redirectToLogin() {
    // Clear any stored tokens
    SharedPreferences.getInstance().then((prefs) {
      prefs.remove('token');
      prefs.remove('token_so');
      prefs.remove('account');
    });

    // Navigate to LoginView
    get_x.Get.offAll(() => const LoginView());
  }
}

class AuthorizationInterceptor extends Interceptor {

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    final token = sharedPreferences.getString('token');
    options.headers['Authorization'] = "Bearer $token";
    handler.next(options);
  }
}
