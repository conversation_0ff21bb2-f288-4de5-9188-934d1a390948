import 'package:flutter/animation.dart';
import 'package:get/get.dart';

import '../../../core/network/dio_client.dart';
import '../../data/datasources/banner/banner_remote_data_source.dart';
import '../../data/repositories/banner/banner_repository_impl.dart';
import '../../domain/repositories/banner/banner_repository.dart';
import '../../domain/usecases/banner/banner_usecase.dart';
import '../auth/login/auth_controller.dart';
import 'banner_controller.dart';

class BannerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<BannerRemoteDataSource>(
          () => BannerRemoteDataSourceImpl(dioClient: Get.find<DioClient>()),
    );

    Get.lazyPut<BannerRepository>(
          () => BannerRepositoryImpl(remoteDataSource: Get.find<BannerRemoteDataSource>()),
    );

    Get.lazyPut<BannerUseCase>(
          () => BannerUseCase(repository: Get.find<BannerRepository>()),
    );

    Get.lazyPut<BannerController>(() => BannerController(
      bannerUseCase: Get.find(),
    ));

    Get.find<AuthSOController>();
  }
}