class OrderRequestEntity {
  final int menuId;
  final int timeSaleMenuId;
  final int branchId;
  final String customerPhone;
  final String customerNo;
  final String customerName;
  final String orderNote;
  final String orderType;
  final List<OrderDetailEntity> orderDetails;

  OrderRequestEntity({
    required this.menuId,
    required this.timeSaleMenuId,
    required this.branchId,
    required this.customerPhone,
    required this.customerNo,
    required this.customerName,
    required this.orderNote,
    required this.orderType,
    required this.orderDetails,
  });
}

class OrderDetailEntity {
  final String itemNo;
  final String itemName;
  final int itemQuantity;
  final double itemPrice;
  final double discountAmount;
  final String itemNote;
  final List<ChildrenEntity>? children;

  OrderDetailEntity({
    required this.itemNo,
    required this.itemName,
    required this.itemQuantity,
    required this.itemPrice,
    required this.discountAmount,
    required this.itemNote,
    this.children,
  });
}

class ChildrenEntity {
  final String itemNo;
  final String itemName;
  final int itemQuantity;
  final double itemPrice;
  final double discountAmount;

  ChildrenEntity({
    required this.itemNo,
    required this.itemName,
    required this.itemQuantity,
    required this.itemPrice,
    required this.discountAmount,
  });
}