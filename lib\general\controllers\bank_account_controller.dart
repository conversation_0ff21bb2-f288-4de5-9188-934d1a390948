import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/network/dio_client.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import '../classes/user_info.dart';
import '../views/account/payment_confi/cancel_otp_verification_view.dart';
import '../views/account/payment_confi/otp_verification_view.dart';

class BankAccountController extends GetxController {
  final DioClientSME dioClient = DioClientSME();

  // Form variables
  String customerType = 'PERS'; // 'PERS' or 'ORG'
  String accountNumber = '';
  String username = '';
  String phoneNumber = '';
  String virtualAccountNumber = '';
  String realTimeNotification = 'ALL'; // 'CREDIT' | 'DEBIT' | 'ALL' | 'NONE'
  String pastDayNotification = 'NONE'; // Only required for ORG
  int bankAccountAutoId = 0;

  // Loading state
  bool isLoading = false;
  bool hasExistingAccount = false;
  bool acceptedTerms = false;
  bool useMainAccount = true;
  bool useVirtualAccount = false;
  TextEditingController virtualAccountController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchExistingAccount();
  }

  Future<void> fetchExistingAccount() async {
    isLoading = true;
    update();

    try {
      final response = await dioClient.post(
        SmeUrl.acbGetByOrg,
        data: {
          "BranchId": UserInfo.branchId,
        },
      );
      //
      dynamic data = response.data;

      if (data['Success'] == true && data['Result'] != null && data['Result'].isNotEmpty) {
        final accountData = data['Result'][0];
        _fillFormWithExistingData(accountData);
        hasExistingAccount = true;
      } else {
        hasExistingAccount = false;
      }
    } catch (e) {
      debugPrint('Error fetching existing account: $e');
      hasExistingAccount = false;
    } finally {
      isLoading = false;
      update();
    }
  }

  void _fillFormWithExistingData(Map<String, dynamic> accountData) {
    customerType = accountData['CustomerType'] ?? 'PERS';
    accountNumber = accountData['AccountNumber'] ?? '';
    username = accountData['Username'] ?? '';
    phoneNumber = accountData['PhoneNumber'] ?? '';
    virtualAccountNumber = accountData['VirtualAccountNumber'] ?? '';
    virtualAccountController.text = virtualAccountNumber;
    realTimeNotification = accountData['CreditOrDebitRealTime'] ?? 'ALL';
    pastDayNotification = accountData['CreditOrDebitPastDay'] ?? 'NONE';
    bankAccountAutoId = accountData['AutoId'] ?? 0;
    debugPrint('AutoId set to: $bankAccountAutoId');
    // Cập nhật trạng thái sử dụng tài khoản
    useMainAccount = accountData['UseMainAccount'] ?? true;
    useVirtualAccount = accountData['UseVirtualAccount'] ?? false;

    debugPrint('UseMainAccount: $useMainAccount');
    debugPrint('UseVirtualAccount: $useVirtualAccount');
  }

  // Register bank account
  Future<bool> registerBankAccount() async {
    if (accountNumber.isEmpty) {
      AppFunction.showError('Vui lòng nhập số tài khoản');
      return false;
    }

    if (customerType == 'ORG' && username.isEmpty) {
      AppFunction.showError('Vui lòng nhập username');
      return false;
    }

    if (phoneNumber.isEmpty) {
      AppFunction.showError('Vui lòng nhập số điện thoại');
      return false;
    }

    if (!acceptedTerms) {
      AppFunction.showError('Vui lòng chấp nhận điều khoản và điều kiện dịch vụ');
      return false;
    }

    if (customerType == 'ORG' && pastDayNotification.isEmpty) {
      AppFunction.showError('Vui lòng chọn loại thông báo qua ngày');
      return false;
    }

    String branchName = "";
    var currentBranch = UserInfo.branches.firstWhere(
          (branch) => branch['BranchId'] == UserInfo.branchId,
      orElse: () => {'BranchName': ''},
    );
    branchName = currentBranch['BranchName'];

    isLoading = true;
    update();

    try {
      final response = await dioClient.post(
          SmeUrl.acbRegister,
          data: {
            "BranchId": UserInfo.branchId,
            "BranchName": branchName,
            "CustomerType": customerType,
            "AccountNumber": accountNumber,
            "Username": customerType == 'ORG' ? username : null,
            "PhoneNumber": phoneNumber,
            "VirtualAccountType": virtualAccountNumber.isNotEmpty ? "LOCPHAT" : null,
            "VirtualAccountNumber": virtualAccountNumber.isNotEmpty ? virtualAccountNumber : null,
            "CreditOrDebitRealTime": realTimeNotification,
            "CreditOrDebitPastDay": customerType == 'ORG' ? pastDayNotification : 'NONE'
          }
      );

      dynamic data = response.data;

      if (data['Success'] == true) {
        final requestId = data['Result']['AcbResponse']['ResponseData']['RequestId'];
        final authId = data['Result']['AcbResponse']['ResponseData']['AuthorizationId'];
        final autoId = data['Result']['SystemResponse']['AutoId'];
        final brandId = data['Result']['SystemResponse']['BranchId'];

        Get.to(() => OtpVerificationView(
          requestId: requestId,
          authorizationId: authId,
          autoId: autoId,
          branchId: brandId,
        ));
        return true;
      } else {
        AppFunction.showError(data['Message'] ?? 'Có lỗi xảy ra khi đăng ký tài khoản');
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi xảy ra: ${e.toString()}');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  void printInputData() {
    debugPrint('=== ENTERED BANK ACCOUNT INFORMATION ===');
    debugPrint('Customer Type: $customerType');
    debugPrint('Account Number: $accountNumber');
    if (customerType == 'ORG') {
      debugPrint('Username: $username');
    }
    debugPrint('Phone Number: $phoneNumber');
    debugPrint('Virtual Account: ${virtualAccountNumber.isEmpty ? "Empty" : virtualAccountNumber}');
    debugPrint('Real-time Notification: $realTimeNotification');
    if (customerType == 'ORG') {
      debugPrint('Past-day Notification: $pastDayNotification');
    }
    debugPrint('BranchId: ${UserInfo.branchId}');
    debugPrint('BranchName: ${UserInfo.branches.firstWhere(
          (branch) => branch['BranchId'] == UserInfo.branchId,
      orElse: () => {'BranchName': ''},
    )['BranchName']}');

    // Print the exact structure that will be sent to API
    debugPrint('\n=== WILL SEND THIS REQUEST DATA ===');
    debugPrint('''
    {
      "BranchId": "${UserInfo.branchId}",
      "BranchName": "${UserInfo.branches.firstWhere(
              (branch) => branch['BranchId'] == UserInfo.branchId,
          orElse: () => {'BranchName': ''},
        )['BranchName']}",
      "CustomerType": "$customerType",
      "AccountNumber": "$accountNumber",
      "Username": "${customerType == 'ORG' ? username : null}",
      "PhoneNumber": "$phoneNumber",
      "VirtualAccountType": "${virtualAccountNumber.isNotEmpty ? "LOCPHAT" : null}",
      "VirtualAccountNumber": "${virtualAccountNumber.isNotEmpty ? virtualAccountNumber : null}",
      "CreditOrDebitRealTime": "$realTimeNotification",
      "CreditOrDebitPastDay": "${customerType == 'ORG' ? pastDayNotification : 'NONE'}"
    }
  ''');
    debugPrint('======================================');
  }

  Future<bool> verifyOtp(String otp, String requestId, String authorizationId, int autoId, int branchId) async {
    isLoading = true;
    update();

    try {
      debugPrint('''
    === OTP VERIFICATION REQUEST ===
    RequestId: $requestId
    AuthorizationId: $authorizationId
    AutoId: $autoId (type: ${autoId.runtimeType})
    BranchId: $branchId (type: ${branchId.runtimeType})
    OTP: $otp
    ''');

      // Tạo URL với query parameters
      final String endpoint = '${SmeUrl.acbRegisterVerify}?id=$autoId&branchId=$branchId';

      // Tạo request body với kiểu dữ liệu chính xác
      final Map<String, dynamic> requestBody = {
        "RequestId": requestId,
        "AuthorizationId": authorizationId,
        "Code": otp,
      };

      debugPrint('Endpoint: $endpoint');
      debugPrint('Request Body: $requestBody');

      final response = await dioClient.post(
        endpoint,
        data: requestBody,
      );

      debugPrint('Response: ${response.data}');

      dynamic data = response.data;

      if (data['Success'] == true) {
        AppFunction.showSuccess('Xác thực OTP thành công');
        await fetchExistingAccount();
        return true;
      } else {
        final errorMsg = data['Message'] ?? 'Xác thực OTP thất bại';
        AppFunction.showError(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('Error in verifyOtp: $e');
      debugPrint('Stack trace: $stackTrace');
      AppFunction.showError('Lỗi xác thực: ${e.toString()}');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  // Thêm hàm cập nhật tài khoản LOCPHAT
  Future<bool> updateVirtualAccount() async {
    // if (virtualAccountController.text.isEmpty) {
    //   AppFunction.showError('Vui lòng nhập số tài khoản LOCPHAT');
    //   return false;
    // }

    if (bankAccountAutoId == 0) {
      AppFunction.showError('Không tìm thấy thông tin tài khoản');
      return false;
    }

    isLoading = true;
    update();

    try {
      final response = await dioClient.post(
        SmeUrl.acbUpdateVirtualAccount,
        data: {
          "AutoId": bankAccountAutoId,
          "VirtualAccountType": virtualAccountController.text.isNotEmpty ? "ACB-LOCPHAT" : "",
          "VirtualAccountNumber": virtualAccountController.text,
        },
      );

      dynamic data = response.data;

      if (data['Success'] == true) {
        AppFunction.showSuccess('Cập nhật tài khoản LOCPHAT thành công');
        virtualAccountNumber = virtualAccountController.text;
        await fetchExistingAccount();
        return true;
      } else {
        AppFunction.showError(data['Message'] ?? 'Có lỗi xảy ra khi cập nhật');
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi xảy ra: ${e.toString()}');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<bool> updateAccountUsage() async {
    if (bankAccountAutoId == 0) {
      AppFunction.showError('Không tìm thấy thông tin tài khoản');
      return false;
    }

    isLoading = true;
    update();

    try {
      final response = await dioClient.post(
        SmeUrl.acbUseAccount,
        data: {
          "AutoId": bankAccountAutoId,
          "UseMainAccount": useMainAccount,
          "UseVirtualAccount": useVirtualAccount,
        },
      );

      dynamic data = response.data;

      if (data['Success'] == true) {
        AppFunction.showSuccess('Cập nhật cách sử dụng tài khoản thành công');
        // Gọi lại API để lấy dữ liệu mới nhất
        await fetchExistingAccount();
        return true;
      } else {
        AppFunction.showError(data['Message'] ?? 'Có lỗi xảy ra khi cập nhật');
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi xảy ra: ${e.toString()}');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  void resetForm() {
    customerType = 'PERS';
    accountNumber = '';
    username = '';
    phoneNumber = '';
    virtualAccountNumber = '';
    realTimeNotification = 'ALL';
    pastDayNotification = 'NONE';
    bankAccountAutoId = 0;
    isLoading = false;
    hasExistingAccount = false;
    acceptedTerms = false;
    useMainAccount = true;
    useVirtualAccount = false;
    virtualAccountController.clear();
    update();
  }

  Future<bool> requestCancelAccount() async {
    if (bankAccountAutoId == 0) {
      AppFunction.showError('Không tìm thấy thông tin tài khoản');
      return false;
    }

    isLoading = true;
    update();

    try {
      // Đảm bảo truyền đúng kiểu int cho id và branchId
      final response = await dioClient.post(
        '${SmeUrl.acbCancel}?id=$bankAccountAutoId&branchId=${UserInfo.branchId}',
      );

      dynamic data = response.data;

      if (data['Success'] == true) {
        final requestId = data['Result']['ResponseData']['RequestId'].toString();
        final authId = data['Result']['ResponseData']['AuthorizationId'].toString();

        debugPrint('''
      Cancel Account Response:
      RequestId: $requestId (${requestId.runtimeType})
      AuthorizationId: $authId (${authId.runtimeType})
      AutoId: $bankAccountAutoId (${bankAccountAutoId.runtimeType})
      BranchId: ${UserInfo.branchId} (${UserInfo.branchId.runtimeType})
      ''');

        Get.to(() => CancelOtpVerificationView(
          requestId: requestId,
          authorizationId: authId,
          autoId: bankAccountAutoId,
          branchId: UserInfo.branchId,
        ));
        return true;
      } else {
        AppFunction.showError(data['Message'] ?? 'Có lỗi xảy ra khi yêu cầu hủy tài khoản');
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi xảy ra: ${e.toString()}');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<bool> verifyCancelOtp(String otp, String requestId, String authorizationId) async {
    isLoading = true;
    update();

    try {
      debugPrint('''
    === CANCEL OTP VERIFICATION REQUEST ===
    RequestId: $requestId (${requestId.runtimeType})
    AuthorizationId: $authorizationId (${authorizationId.runtimeType})
    AutoId: $bankAccountAutoId (${bankAccountAutoId.runtimeType})
    BranchId: ${UserInfo.branchId} (${UserInfo.branchId.runtimeType})
    OTP: $otp (${otp.runtimeType})
    ''');

      // Tạo URL với query parameters - đảm bảo truyền đúng kiểu int
      final String endpoint = '${SmeUrl.acbCancelVerify}?id=$bankAccountAutoId&branchId=${UserInfo.branchId}';

      // Tạo request body
      final Map<String, dynamic> requestBody = {
        "RequestId": requestId,
        "AuthorizationId": authorizationId,
        "Code": otp,
      };

      debugPrint('Endpoint: $endpoint');
      debugPrint('Request Body: $requestBody');

      final response = await dioClient.post(
        endpoint,
        data: requestBody,
      );

      debugPrint('Response: ${response.data}');

      dynamic data = response.data;

      if (data['Success'] == true) {
        AppFunction.showSuccess('Hủy liên kết tài khoản thành công');
        resetForm();
        await fetchExistingAccount();
        return true;
      } else {
        AppFunction.showError(data['Message'] ?? 'Xác thực OTP thất bại');
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('Error in verifyCancelOtp: $e');
      debugPrint('Stack trace: $stackTrace');
      AppFunction.showError('Có lỗi xảy ra: ${e.toString()}');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

}