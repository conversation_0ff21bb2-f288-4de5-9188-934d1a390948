import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/widgets/text_title.dart';
import '../../domain/report/entities/report.dart';
import '../bloc/home_cubit.dart';
import '../bloc/home_state.dart';

Widget overviewRevenue(BuildContext context, double width, List<OverviewRevenueEntity> reviewRevenue, String title, String titleDate) {
  final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0);
  final PageController _pageController = PageController();
  final int totalPages = 2;
  final state = context.watch<HomeCubit>().state;
  int selectedBranchCount = 0;
  int totalBranchCount = 0;

  if (state is HomeLoaded) {
    selectedBranchCount = state.selectedBrands.length;
    totalBranchCount = state.branches.length;
  }


  if (reviewRevenue.isEmpty) {
    return Text(
      "Không có dữ liệu doanh thu",
      style: PrimaryFont.bold.copyWith(fontSize: width * 0.04),
    );
  }
  final totalRevenue =
  reviewRevenue.fold(0.0, (sum, e) => sum + (e.paymentAmount ?? 0));
  final totalItemAmount =
  reviewRevenue.fold(0.0, (sum, e) => sum + (e.itemAmount ?? 0));
  final totalVAT =
  reviewRevenue.fold(0.0, (sum, e) => sum + (e.vatAmount ?? 0));
  final totalDiscount =
  reviewRevenue.fold(0.0, (sum, e) => sum + (e.discountAmount ?? 0));
  final totalBill =
  reviewRevenue.fold(0.0, (sum, e) => sum + (e.totalBill ?? 0));
  final totalInvoice =
  reviewRevenue.fold(0.0, (sum, e) => sum + (e.totalInvoice ?? 0));

  //
  final revenueSharing = reviewRevenue.fold(
      0.0,
          (sum, e) => sum + ((e.itemAmount ?? 0) - (e.vatAmount ?? 0)) * 0.15
  );
  return Column(
    children: [
      Align(
        alignment: Alignment.center,
        child: Padding(
          padding: EdgeInsets.only(left: width * 0.015, bottom: width * 0.015),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              TextTitle(
                title: title,
                fontSize: width * 0.04,
              ),
              TextTitleRegular(
                title: titleDate,
                fontSize: width * 0.035,
              ),
            ],
          ),
        ),
      ),
      SizedBox(
        height: width * 0.42,
        child: PageView(
          controller: _pageController,
          children: [
            buildSummaryGrid(width, [
              buildCard(
                  width,
                  "total_amount".tr,
                  currencyFormat.format(totalRevenue),
                  Colors.amber[100]!,
                  Icons.monetization_on),
              buildCard(
                  width,
                  "item_amount".tr,
                  currencyFormat.format(totalItemAmount),
                  Colors.teal[100]!,
                  Icons.bar_chart),
              buildCard(
                  width,
                  "bills".tr,
                  "$totalBill",
                  Colors.blue[100]!,
                  Icons.receipt_long),

              buildCard(
                  width,
                 "discount".tr,
                  currencyFormat.format(totalDiscount),
                  Colors.green[100]!,
                  Icons.local_activity),
            ]),
            buildSummaryGrid(width, [
              buildCard(
                  width,
                  "tax".tr,
                  currencyFormat.format(totalVAT),
                  Colors.pink[100]!,
                  Icons.account_balance_sharp),
              buildCard(
                  width,
                  "Hóa đơn điện tử",
                  "$totalInvoice",
                  Colors.orange[100]!,
                  Icons.payment),
            ]),

          ],
        ),
      ),
      SmoothPageIndicator(
        controller: _pageController,
        count: totalPages,
        effect: ExpandingDotsEffect(
          dotHeight: width * 0.015,
          dotWidth: width * 0.015,
          activeDotColor: AppColors.primary,
          dotColor: AppColors.shadow,
        ),
      ),
    ],
  );
}
Widget buildSummaryGrid(double width, List<Widget> children) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: width * 0.015),
    child: GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: children.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: width * 0.03,
        crossAxisSpacing: width * 0.03,
        childAspectRatio: 2.5,
      ),
      itemBuilder: (context, index) => children[index],
    ),
  );
}
Widget buildCard(double width, String title, String value, Color color, IconData icon) {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: width * 0.012, vertical: width * 0.035),
    decoration: cardDecoration(width * 0.03, AppColors.background),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: width * 0.05,
          backgroundColor: color,
          child: Icon(icon, color: AppColors.background, size: width * 0.06),
        ),
        SizedBox(width: width * 0.012),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.035,
                ),
              ),
              Text(
                value,
                style: PrimaryFont.bold.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.035,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
