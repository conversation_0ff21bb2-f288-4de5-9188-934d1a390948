import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class CustomMoneyField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool showLabel;
  final String hint;
  final bool readOnly;
  final bool disabled;
  final VoidCallback? onTap;
  final int maxLines;
  final bool space;
  final int? maxLength;
  final bool required;
  final int decimal;
  final bool taxPercent; // Thêm tham số mới
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;

  const CustomMoneyField({
    super.key,
    required this.controller,
    this.label = '',
    this.showLabel = true,
    this.hint = '',
    this.readOnly = false,
    this.disabled = false,
    this.onTap,
    this.maxLines = 1,
    this.space = true,
    this.maxLength,
    this.required = false,
    this.decimal = 0,
    this.taxPercent = false, // Mặc định là false
    this.inputFormatters,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel)
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: label,
                size: 16,
              ),
              if (required)
                CustomText(
                  text: '*',
                  color: AppColors.danger,
                  size: 16,
                )
            ],
          ),
        Container(
          margin: EdgeInsets.fromLTRB(0, 2.5, 0, space ? 10 : 0),
          child: TextFormField(
            controller: controller,
            validator: validator,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 10),
              filled: true,
              fillColor: disabled
                  ? Colors.grey.withValues(alpha: 0.2)
                  : Colors.white,
              enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                      width: 0.5, color: Colors.black.withValues(alpha: 0.2)),
                  borderRadius: BorderRadius.circular(7.5)),
              focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                      width: 0.5,
                      color: Colors.blueAccent.withValues(alpha: 0.8)),
                  borderRadius: BorderRadius.circular(7.5)),
              hintText: hint,
              suffixText: taxPercent ? '%' : null, // Hiển thị % nếu taxPercent = true
              isDense: true,
              counterText: '',
            ),
            readOnly: readOnly,
            onTap: onTap,
            keyboardType: TextInputType.number,
            maxLines: maxLines,
            maxLength: maxLength,
            inputFormatters: inputFormatters,
            autovalidateMode: AutovalidateMode.onUserInteraction,
          ),
        ),
      ],
    );
  }
}