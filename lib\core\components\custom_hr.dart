import 'package:flutter/material.dart';

class CustomHr extends StatelessWidget {
  final double height;
  const CustomHr({super.key, this.height = 1});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: height,
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  width: height,
                  color: Colors.grey.withValues(alpha: 0.5)
              )
          )
      ),
    );
  }
}
