import 'package:get/get.dart';

import '../../../data/datasources/sale_common/tables_remote_data_source.dart';
import '../../../data/repositories/sale_common/tables_repository_impl.dart';
import '../../../domain/repositories/sale_common/tables_repository.dart';
import '../../../domain/usecases/sale_common/tables_usecase.dart';
import 'tables_controller.dart';

class TablesBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<TablesRemoteDataSource>(
          () => TablesRemoteDataSourceImpl(dioClient: Get.find()),
    );

    Get.lazyPut<TablesRepository>(
          () => TablesRepositoryImpl(remoteDataSource: Get.find()),
    );

    Get.lazyPut<GetTablesUseCase>(
          () => GetTablesUseCase(repository: Get.find()),
    );

    Get.lazyPut<TablesController>(
          () => TablesController(getTablesUseCase: Get.find()),
    );
  }
}