import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_style.dart';

class ConfirmLogoutDialog extends StatelessWidget {
  final VoidCallback onConfirmed;
  final double width;
  final String savedPassword;

  const ConfirmLogoutDialog({
    super.key,
    required this.onConfirmed,
    required this.width,
    required this.savedPassword,
  });

  @override
  Widget build(BuildContext context) {
    final TextEditingController passwordController = TextEditingController();
    String? errorText;

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          backgroundColor: AppColors.background,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(width * 0.03)),
          title: Center(
            child: Text(
              'Đăng xuất',
              style: PrimaryFont.bold.copyWith(
                fontSize: width * 0.05,
                color: AppColors.text,
              ),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vui lòng nhập lại mật khẩu để xác nhận đăng xuất.',
                style: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.text,
                ),
              ),
              SizedBox(height: width * 0.015),
              TextField(
                controller: passwordController,
                obscureText: true,
                decoration: InputDecoration(
                  labelText: 'password'.tr,
                  labelStyle: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                  prefixIcon: Icon(Icons.lock,
                      color: AppColors.text, size: width * 0.05),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.text),
                    borderRadius: BorderRadius.circular(width * 0.02),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary, width: 1),
                    borderRadius: BorderRadius.circular(width * 0.02),
                  ),
                  errorText: errorText,
                  errorStyle: TextStyle(
                    color: Colors.red,
                    fontSize: width * 0.028,
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Colors.red),
                    borderRadius: BorderRadius.circular(width * 0.02),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Colors.red, width: 1),
                    borderRadius: BorderRadius.circular(width * 0.02),
                  ),
                ),

                style: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.text,
                ),
                onChanged: (_) {
                  if (errorText != null) {
                    setState(() {
                      errorText = null;
                    });
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'cancel'.tr,
                style: PrimaryFont.bold.copyWith(
                  fontSize: width * 0.03,
                  color: Colors.red,
                ),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                final inputPassword = passwordController.text.trim();
                if (inputPassword == savedPassword) {
                  Navigator.of(context).pop();
                  onConfirmed();
                } else {
                  setState(() {
                    errorText = 'Mật khẩu không đúng';
                  });
                }
              },
              icon: Icon(Icons.logout, color: AppColors.background, size: width * 0.05),
              label: Text(
                'confirm'.tr,
                style: PrimaryFont.bold.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.background,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.background,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(width * 0.02)),
              ),
            ),
          ],
        );
      },
    );
  }
}
