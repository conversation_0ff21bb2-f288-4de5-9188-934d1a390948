import 'package:dartz/dartz.dart';
import '../../../common/helper/mapper/report_mapper.dart';
import '../../../domain/report/entities/report.dart';
import '../../../domain/report/repositories/report.dart';
import '../sources/report.dart';

// Revenue View
class OverviewRevenueRepositoryImpl implements OverviewRevenueRepository {
  final OverviewRevenueService overviewRevenueService;

  OverviewRevenueRepositoryImpl({required this.overviewRevenueService});

  @override
  Future<Either<String, OverviewRevenueEntity>> getOverviewRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await overviewRevenueService.getOverviewRevenues(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (data) => Right(OverviewRevenueMapper.toEntity(data)),
    );
  }
}

class TopBottomBranchesRevenueRepositoryImpl implements TopBottomBranchesRevenueRepository {
  final TopBottomBranchesRevenueService topBottomBranchesRevenueService;

  TopBottomBranchesRevenueRepositoryImpl({required this.topBottomBranchesRevenueService});

  @override
  Future<Either<String, TopBottomBranchesRevenueEntity>> getTopBottomBranchesRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await topBottomBranchesRevenueService.getTopBottomBranchesRevenues(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (data) => Right(TopBottomBranchesRevenueMapper.toEntity(data)),
    );
  }
}

class Revenue7DaysRepositoryImpl implements Revenue7DaysRepository {
  final Revenue7DaysService revenue7DaysService;

  Revenue7DaysRepositoryImpl({required this.revenue7DaysService});

  @override
  Future<Either<String, List<Revenue7DaysEntity>>> getRevenue7Days({
    required List<int> listBrandId,
  }) async {
    final result = await revenue7DaysService.getRevenue7Days(listBrandId: listBrandId);

    return result.fold(
          (failure) => Left(failure),
          (data) => Right(Revenue7DaysMapper.toEntityList(data)),
    );
  }
}

class TopBranchesRevenueByDateRepositoryImpl implements TopBranchesRevenueByDateRepository {
  final TopBranchesRevenueByDateService topBranchesRevenueByDateService;

  TopBranchesRevenueByDateRepositoryImpl({required this.topBranchesRevenueByDateService});

  @override
  Future<Either<String, List<TopBranchesRevenueByDateEntity>>> getTopBranchesRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await topBranchesRevenueByDateService.getTopBranchesRevenueByDates(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (dataList) => Right(dataList.map((e) => TopBranchesRevenueByDateMapper.toEntity(e)).toList()),
    );
  }
}

// By Item
class TopAndBottomItemsRevenueRepositoryImpl implements TopAndBottomItemsRevenueRepository {
  final TopAndBottomItemsRevenueService topAndBottomItemsRevenueService;

  TopAndBottomItemsRevenueRepositoryImpl({required this.topAndBottomItemsRevenueService});

  @override
  Future<Either<String, TopAndBottomItemsRevenueEntity>> getTopAndBottomItemsRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await topAndBottomItemsRevenueService.getTopAndBottomItemsRevenues(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (data) => Right(TopAndBottomItemsRevenueMapper.toEntity(data)),
    );
  }
}

class TopItemsRevenueByDateRepositoryImpl implements TopItemsRevenueByDateRepository {
  final TopItemsRevenueByDateService topItemsRevenueByDateService;

  TopItemsRevenueByDateRepositoryImpl({required this.topItemsRevenueByDateService});

  @override
  Future<Either<String, List<TopItemsRevenueByDateEntity>>> getTopItemsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await topItemsRevenueByDateService.getTopItemsRevenueByDates(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (dataList) => Right(dataList.map((e) => TopItemsRevenueByDateMapper.toEntity(e)).toList()),
    );
  }
}

// By Category
class TopAndBottomCategoriesByAmountRepositoryImpl implements TopAndBottomCategoriesByAmountRepository {
  final TopAndBottomCategoriesByAmountService topAndBottomCategoriesByAmountService;

  TopAndBottomCategoriesByAmountRepositoryImpl({required this.topAndBottomCategoriesByAmountService});

  @override
  Future<Either<String, TopAndBottomCategoriesByAmountEntity>> getTopAndBottomCategoriesByAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await topAndBottomCategoriesByAmountService.getTopAndBottomCategoriesByAmounts(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (data) => Right(TopAndBottomCategoriesByAmountMapper.toEntity(data)),
    );
  }
}

class TopCatsRevenueByDateRepositoryImpl implements TopCatsRevenueByDateRepository {
  final TopCatsRevenueByDateService topCatsRevenueByDateService;

  TopCatsRevenueByDateRepositoryImpl({required this.topCatsRevenueByDateService});

  @override
  Future<Either<String, List<TopCatsRevenueByDateEntity>>> getTopCatsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await topCatsRevenueByDateService.getTopCatsRevenueByDates(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (dataList) => Right(dataList.map((e) => TopCatsRevenueByDateMapper.toEntity(e)).toList()),
    );
  }
}

// Payment
class TotalPaymentAmountRepositoryImpl implements TotalPaymentAmountRepository {
  final TotalPaymentAmountService totalPaymentAmountService;

  TotalPaymentAmountRepositoryImpl({required this.totalPaymentAmountService});

  @override
  Future<Either<String, List<TotalPaymentAmountEntity>>> getTotalPaymentAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await totalPaymentAmountService.getTotalPaymentAmounts(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (data) => Right(TotalPaymentAmountMapper.toEntityList(data)),
    );
  }
}

class TotalPaymentAmountByDateRepositoryImpl implements TotalPaymentAmountByDateRepository {
  final TotalPaymentAmountByDateService totalPaymentAmountByDateService;

  TotalPaymentAmountByDateRepositoryImpl({required this.totalPaymentAmountByDateService});

  @override
  Future<Either<String, List<TotalPaymentAmountByDateEntity>>> getTotalPaymentAmountByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    final result = await totalPaymentAmountByDateService.getTotalPaymentAmountByDates(
      listBrandId: listBrandId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );

    return result.fold(
          (failure) => Left(failure),
          (dataList) => Right(dataList.map((e) => TotalPaymentAmountByDateMapper.toEntity(e)).toList()),
    );
  }
}
