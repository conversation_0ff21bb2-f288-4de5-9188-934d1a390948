import 'package:get/get.dart';

import '../../../../core/network/dio_client.dart';
import '../../../data/datasources/auth_remote_data_source.dart';
import '../../../data/repositories/auth_repository_impl.dart';
import '../../../domain/usecases/login_usecase.dart';
import 'auth_controller.dart';

class AuthSOBinding implements Bindings {
  @override
  void dependencies() {
    final dioClient = Get.find<DioClient>();

    Get.lazyPut<AuthRemoteDataSource>(
          () => AuthRemoteDataSourceImpl(dioClient: dioClient),
    );

    Get.lazyPut<AuthRepositoryImpl>(
          () => AuthRepositoryImpl(
        remoteDataSource: Get.find<AuthRemoteDataSource>(),
      ),
    );

    Get.lazyPut<LoginUseCase>(
          () => LoginUseCase(repository: Get.find<AuthRepositoryImpl>()),
    );

    Get.lazyPut<AuthSOController>(
          () => AuthSOController(loginUseCase: Get.find<LoginUseCase>()),
    );
  }
}