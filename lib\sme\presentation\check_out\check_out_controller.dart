import 'dart:async';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/constants/api_url.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/controllers/bluetooth_printer_controller.dart';
import 'package:gls_self_order/core/controllers/usb_printer_controller.dart';
import 'package:gls_self_order/core/network/dio_client.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:image/image.dart' as img;
import '../../../general/controllers/customer_controller.dart';
import '../../data/datasources/order/order_remote_data_source.dart';
import '../../data/repositories/order/order_repository_impl.dart';
import '../../domain/entities/order/order_reponse_entity.dart';
import '../../domain/entities/order/order_request_entity.dart';
import '../../domain/entities/order/status_request_entity.dart';
import '../../domain/usecases/order/generate_acb_usecase.dart';
import '../../domain/usecases/order/generate_zalo_usecase.dart';
import '../../domain/usecases/order/order_usecase.dart';
import '../../domain/usecases/order/status_usecase.dart';
import '../../routes/sme_app_routes.dart';
import '../e_menu/cart/cart_controller.dart';
import '../e_menu/e_menu_controller.dart';

class CheckoutController extends GetxController {
  final CreateOrderUseCase createOrderUseCase;
  final GenerateZALOQRUseCase generateZALOQRUseCase;
  final UpdateStatusUseCase updateStatusUseCase;
  final BluetoothPrinterController bluetoothPrinterController = Get.find();
  final UsbPrinterController usbPrinterController = Get.find();
  late IO.Socket socket;

  var branchName = ''.obs;
  var branchId = 0.obs;
  var showAllItems = false.obs;
  final RxString paymentMethod = 'CASH'.obs;
  final RxString tempPaymentMethod = 'CASH'.obs;
  bool isFirstTime = true;
  var isLoading = false.obs;
  var orderSuccess = false.obs;

  //final RxInt paymentTimeout = 10.obs;
  final RxString zaloQRCodeData = ''.obs;
  final RxString acbQRCodeData = ''.obs;
  final RxString orderCode = ''.obs;
  final RxDouble paymentAmount = 0.0.obs;
  final RxBool paymentSuccess = false.obs;
  final RxString orderNote = ''.obs;
  final Rx<OrderResponseEntity?> orderResponse = Rx<OrderResponseEntity?>(null);
  final RxString qrError = RxString('');

  // Customer
  final CustomerController customerController = Get.find();
  var selectedCustomer = Rx<Map<String, dynamic>?>(null);
  final RxString cashPaymentNote = ''.obs;

  void updateOrderNote(String note) {
    orderNote.value = note;
  }

  //Timer? timer;

  CheckoutController({
    required this.createOrderUseCase,
    required this.generateZALOQRUseCase,
    required this.updateStatusUseCase,
  });

  Future<void> loadSavedBranchName() async {
    final eMenuController = Get.find<EMenuController>();
    final menuEntity = eMenuController.menuEntity.value;
    branchName.value = menuEntity?.fullJson['BranchName'] ?? '';
    branchId.value = menuEntity?.fullJson['BranchId'] ?? 0;
  }

  @override
  void onInit() {
    super.onInit();
    checkCartAndNavigate();
    connectSocket();
    loadSavedBranchName();
    if (isFirstTime) {
      paymentMethod.value = 'CASH';
      tempPaymentMethod.value = 'CASH';
      isFirstTime = false;
    }
    bluetoothPrinterController.checkAndReconnectPrinter();
    loadPaymentMethod();
  }

  @override
  void onClose() {
    socket.dispose();
    //timer?.cancel();
    super.onClose();
  }

  void connectSocket() {
    socket = IO.io(ApiUrl.socketUrl, <String, dynamic>{
      'transports': ['websocket'],
      'autoConnect': false,
    });

    socket.connect();

    socket.onConnect((_) {
      socket.emit('order-group', UserInfo.orgId);
    });

    socket.on('PaymentSuccess', (data) async {
      String code = data['orderCode'] ?? '';

      print("VINHMIN :${data}");

      if (code == orderCode.value) {
        paymentSuccess.value = true;
        dynamic decoded = await getImageBill(orderCode.value, UserInfo.branchId);
        if (decoded != null) {
          if (GlobalVar.typePrinter == 'bluetooth') {
            bluetoothPrinterController.printBill(decoded);
          }
          else if (GlobalVar.typePrinter == 'usb') {
            usbPrinterController.printBill(decoded);
          }
          else {
            // AppFunction.showError('Máy in chưa được kết nối');
          }
        }
      }
    });

    socket.onDisconnect((_) {
      print('Socket disconnected');
    });
  }

  // startCountdown() {
  //   timer?.cancel();
  //
  //   paymentTimeout.value = 180;
  //
  //   timer = Timer.periodic(Duration(seconds: 1), (_timer) {
  //     if (paymentTimeout.value > 0) {
  //       paymentTimeout.value--;
  //     } else {
  //       _timer.cancel();
  //     }
  //   });
  // }
  //
  // stopCountdown() {
  //   timer?.cancel();
  // }

  void setTempPaymentMethod(String method) {
    tempPaymentMethod.value = method;
  }

  // Choose Payment Method
  Future<void> loadPaymentMethod() async {
    final prefs = await SharedPreferences.getInstance();
    final savedMethod = prefs.getString('paymentMethod');
    if (savedMethod != null) {
      paymentMethod.value = savedMethod;
      tempPaymentMethod.value = savedMethod;
    }
  }

  Future<void> savePaymentMethod(String method) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('paymentMethod', method);
  }
  void confirmPaymentMethod() {
    paymentMethod.value = tempPaymentMethod.value;
    savePaymentMethod(tempPaymentMethod.value);
  }
  String getCurrentPaymentMethod() {
    return paymentMethod.value;
  }

  void toggleExpandCollapse() {
    showAllItems.value = !showAllItems.value;
  }

  Future<void> createOrder() async {
    try {
      isLoading.value = true;
      orderResponse.value = null;

      final cartController = Get.find<CartSMEController>();
      final prefs = await SharedPreferences.getInstance();
      final orderType = prefs.getString('order_type') ?? 'DineIn';
      // Lấy thông tin menu từ EMenuController
      final eMenuController = Get.find<EMenuController>();
      final menuEntity = eMenuController.menuEntity.value;

      // Lấy các giá trị từ menuEntity
      final menuId = menuEntity?.fullJson['MenuAutoId'] ?? 0;
      final branchId = menuEntity?.fullJson['BranchId'] ?? 0;
      final timeSaleMenuId = menuEntity?.fullJson['TimeSaleMenus']?[0]['TimeSaleAutoId'] ?? 0;

      // Chuyển đổi từ giỏ hàng sang OrderRequestEntity
      final orderRequest = OrderRequestEntity(
        menuId: menuId,
        timeSaleMenuId: timeSaleMenuId,
        branchId: branchId,
        customerPhone: selectedCustomer.value?['phone'] ?? "",
        customerNo: selectedCustomer.value?['customerNo'] ?? "",
        customerName: selectedCustomer.value?['name'] ?? "Khách vãng lai",
        orderNote: orderNote.value,
        orderType: orderType,
        orderDetails: cartController.cartItems.map((item) {
          final product = item['product'];

          final defaultItems = product['DefaultItems'] as List<dynamic>? ?? [];
          final selectedChoices = product['SelectedChoices'] as Map<String, dynamic>? ?? {};

          // Convert DefaultItems
          final defaultChildren = defaultItems.map<ChildrenEntity>((defaultItem) {
            return ChildrenEntity(
              itemNo: defaultItem['ItemNo'] ?? '',
              itemName: defaultItem['ItemName'] ?? '',
              itemQuantity: defaultItem['Qty'] ?? 1,
              itemPrice: (defaultItem['Price'] as num?)?.toDouble() ?? 0.0,
              discountAmount: 0,
            );
          }).toList();

          final choiceChildren = <ChildrenEntity>[];
          selectedChoices.forEach((choiceId, items) {
            if (items is List) {
              for (var choiceItem in items) {
                choiceChildren.add(ChildrenEntity(
                  itemNo: choiceItem['ItemNo'] ?? '',
                  itemName: choiceItem['ItemName'] ?? '',
                  itemQuantity: 1,
                  itemPrice: (choiceItem['Price'] as num?)?.toDouble() ?? 0.0,
                  discountAmount: 0,
                ));
              }
            }
          });

          return OrderDetailEntity(
            itemNo: product['ItemNo'] ?? '',
            itemName: product['ItemName'] ?? '',
            itemQuantity: item['quantity'] ?? 1,
            itemPrice: (product['Price'] as num?)?.toDouble() ?? 0.0,
            discountAmount: 0,
            itemNote: item['note'] ?? '',
            children: [...defaultChildren, ...choiceChildren],
          );
        }).toList(),

      );

      final response = await createOrderUseCase(orderRequest);
      orderResponse.value = response;

      if (response.success) {
        orderSuccess.value = true;
        orderCode.value = response.result?.orderResponse.orderCode ?? '';
        paymentAmount.value = response.result?.orderResponse.orderTotalView ?? cartController.totalPrice.value;
      } else {
        Get.snackbar('Lỗi', response.message);
      }
    } catch (e) {
      Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi tạo đơn hàng: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }
  // Thêm hàm generateQRCode
  Future<void> generateQRCode() async {
    if (orderCode.isEmpty) {
      Get.snackbar('Lỗi', 'Chưa có mã đơn hàng');
      return;
    }

    try {
      final response = await generateZALOQRUseCase(orderCode.value);

      if (response.result != null && response.result!.qrCode.isNotEmpty) {
        zaloQRCodeData.value = response.result!.qrCode;
        print('QR Code Data: ${zaloQRCodeData.value}');
      } else {
        zaloQRCodeData.value = '';
        Get.snackbar('Lỗi', 'Không thể tạo mã QR');
      }
    } catch (e) {
      zaloQRCodeData.value = '';
      Get.snackbar('Lỗi', 'Lỗi khi tạo mã QR: ${e.toString()}');
    }
  }

  // Payment ACB
  Future<void> generateACBQRCode() async {
    if (orderCode.isEmpty) {
      Get.snackbar('Lỗi', 'Chưa có mã đơn hàng');
      return;
    }

    try {
      isLoading.value = true;
      qrError.value = '';
      acbQRCodeData.value = '';

      final response = await GenerateACBQRUseCase(repository: OrderRepositoryImpl(
        remoteDataSource: OrderRemoteDataSourceImpl(dioClient: Get.find()),
      )).call(orderCode.value);

      if (response.result?.qrPay != null) {
        acbQRCodeData.value = response.result!.qrPay;
        print('ACB QR Code Data: ${acbQRCodeData.value}');
      } else {
        qrError.value = response.message ?? 'Không thể tạo mã QR ACB';
      }
    } catch (e) {
      qrError.value = 'Lỗi khi tạo mã QR ACB: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> cancelACBOrder() async {
    try {
      if (orderCode.value.isEmpty) {
        Get.snackbar('Lỗi', 'Không tìm thấy mã đơn hàng');
        return;
      }

      // Cập nhật trạng thái hủy đơn
      final response = await updateStatusUseCase(
        StatusRequestEntity(
          key: 'ACB',
          orderCode: orderCode.value,
          msg: 'Hủy thanh toán',
          statusCode: 'CANCEL',
        ),
      );

      if (response.success ?? true) {
        Get.snackbar('Thành công', 'Đã hủy đơn hàng thành công');
        //stopCountdown();
        // Clear các trạng thái thanh toán
        orderCode.value = '';
        paymentAmount.value = 0.0;
        acbQRCodeData.value = '';
      } else {
        Get.snackbar('Lỗi', response.message ?? 'Đã hủy đơn hàng thất bại');
      }
    } catch (e) {
      Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi hủy đơn hàng: ${e.toString()}');
    }
  }

  Future<void> processCashPayment() async {
    try {
      isLoading.value = true;

      // 1. Cập nhật trạng thái thanh toán tiền mặt mà không cần note
      await updateStatusUseCase(
        StatusRequestEntity(
          key: 'CASH',
          orderCode: orderCode.value,
          msg: '',
          statusCode: 'SUCCESS',
        ),
      );

      // 2. In bill
      dynamic decoded = await getImageBill(orderCode.value, UserInfo.branchId);
      if (decoded != null) {

        if (GlobalVar.typePrinter == 'bluetooth') {
          bluetoothPrinterController.printBill(decoded);
        }
        else if (GlobalVar.typePrinter == 'usb') {
          usbPrinterController.printBill(decoded);
        }
      }

      // 3. Đánh dấu thanh toán thành công
      paymentSuccess.value = true;
    } catch (e) {
      Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi thanh toán: ${e.toString()}');
      throw e;
    } finally {
      isLoading.value = false;
    }
  }

  void printOrderSummary() {
    final cartController = Get.find<CartSMEController>();

    print('--- THÔNG TIN ĐƠN HÀNG ---');
    print('Mã đơn hàng: ${orderCode.value}');
    // print('Số điện thoại: 0901234567');
    // print('Khách hàng: Nguyễn Văn A');
    print('Chi nhánh: ${branchName.value}');
    print('Tổng tiền: ${paymentAmount.value.toStringAsFixed(0)}đ');
    print('Phương thức thanh toán: ${paymentMethod.value}');
    print('Danh sách sản phẩm:');

    for (var item in cartController.cartItems) {
      final product = item['product'];
      final quantity = item['quantity'] ?? 1;
      final price = (product['Price'] as num?)?.toDouble() ?? 0.0;
      final total = quantity * price;
      final note = item['note']?.toString().trim() ?? '';

      print(' - ${product['ItemName']} x$quantity = ${total.toStringAsFixed(0)}đ');

      if (note.isNotEmpty) {
        print('    Ghi chú: $note');
      }

      final defaultItems = product['DefaultItems'] as List<dynamic>? ?? [];
      for (var defaultItem in defaultItems) {
        print('    + ${defaultItem['ItemName']} (${defaultItem['Price']}đ)');
      }

      final selectedChoices = product['SelectedChoices'] as Map<String, dynamic>? ?? {};
      selectedChoices.forEach((choiceId, items) {
        if (items is List) {
          for (var choiceItem in items) {
            print('    * ${choiceItem['ItemName']} (${choiceItem['Price']}đ)');
          }
        }
      });
    }
    print('--------------------------');
  }

  void manualCheckTransaction() async {
    paymentSuccess.value = true;
    dynamic decoded = await getImageBill(orderCode.value, UserInfo.branchId);
    if (decoded != null) {
      if (GlobalVar.typePrinter == 'bluetooth') {
        bluetoothPrinterController.printBill(decoded);
      }
      else if (GlobalVar.typePrinter == 'usb') {
        usbPrinterController.printBill(decoded);
      }
      else {
        // AppFunction.showError('Máy in chưa được kết nối');
      }
    }

    //else set false
  }

  // Future<void> refreshQRCode() async {
  //   try {
  //     isLoading.value = true;
  //
  //     await createOrder();
  //
  //     if (orderSuccess.value) {
  //       if (paymentMethod.value == 'ZALOPAY') {
  //         await generateQRCode();
  //       } else if (paymentMethod.value == 'ACB') {
  //         await generateACBQRCode();
  //       }
  //       //startCountdown();
  //     }
  //   } catch (e) {
  //     Get.snackbar('Lỗi', 'Không thể tạo lại mã QR: ${e.toString()}');
  //   } finally {
  //     isLoading.value = false;
  //   }
  // }

  getImageBill(orderId, brandId) async {
    DioClientSME dioClientSME = DioClientSME();
    try {
      final response = await dioClientSME.post(
          SmeUrl.genBillUrl,
          data: {
            "orderId": orderId,
            "branchId": brandId,
          },
        options: Options(
          responseType: ResponseType.bytes,
          headers: {
            'Accept': 'image/png',
          },
        ),
      );
      Uint8List imageBytes = response.data!;
      final decoded = img.decodeImage(imageBytes);
      return decoded;
    } catch (e) {
      return null;
    }
  }

  // Add customer
  Future<void> createNewCustomer(String name, String phone) async {
    try {
      isLoading.value = true;

      final customerId = await customerController.postCreate(
        name,
        phone,
        null, // dob
        null, // gender
        null, // address
      );

      if (customerId > 0) {
        // Nếu tạo thành công, cập nhật selectedCustomer
        selectedCustomer.value = {
          'name': name,
          'phone': phone,
          'customerNo': customerId.toString(),
        };

        Get.back();
        Get.back();
      }
    } catch (e) {
      Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi tạo khách hàng mới');
    } finally {
      isLoading.value = false;
    }
  }

  void checkCartAndNavigate() {
    final cartController = Get.find<CartSMEController>();
    if (cartController.cartItems.isEmpty) {
      Get.offAllNamed(SmeAppRoutes.menu);
    }
  }
}
