import 'package:flutter/material.dart';

import '../../../../core/components/custom_back_button.dart';
import '../../../../core/components/custom_text.dart';
import '../../../../core/components/custom_title_app_bar.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_images.dart';
import 'bank_account_form_view.dart';

class PaymentMethodPage extends StatelessWidget {
  const PaymentMethodPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: '<PERSON><PERSON>u hình thanh toán'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: const CustomBackButton(),
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 10),
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: CustomText(
              text: "<PERSON><PERSON><PERSON> khoản",
              size: 18,
              bold: true,
            ),
          ),
          const SizedBox(height: 8),
          BankSwitchTile(
            bankName: "Ngân hàng thương mại cổ phấn á châu (ACB)",
            logoPath: AppImages.acb,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => BankAccountFormView()),
              );
            },
          ),
        ],
      ),
    );
  }
}

class BankSwitchTile extends StatelessWidget {
  final String bankName;
  final String logoPath;
  final VoidCallback? onTap;

  const BankSwitchTile({
    super.key,
    required this.bankName,
    required this.logoPath,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: Container(
        width: 40,
        height: 40,
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.shadow.withOpacity(0.2), width: 1.5),
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.asset(logoPath, fit: BoxFit.contain),
        ),
      ),
      title: CustomText(text: bankName, maxLines: 2),
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
    );
  }
}
