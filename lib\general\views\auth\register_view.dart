import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/validators/auth_validator.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';

class RegisterView extends StatefulWidget {
  final String type;
  const RegisterView({super.key, required this.type});

  @override
  State<RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<RegisterView> {
  //variable
  GeneralController generalController = Get.find();
  AuthController authController = Get.find();
  TextEditingController orgNameController = TextEditingController();
  TextEditingController branchNameController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController fullNameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController licenseKeyController = TextEditingController();
  TextEditingController businessTypeController = TextEditingController();
  TextEditingController isEnterpriseController = TextEditingController();
  TextEditingController businessType1Controller = TextEditingController();
  final GlobalKey<FormState> _step1FormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _step2FormKey = GlobalKey<FormState>();
  List businessTypes = [];
  int businessTypeSelected = 0;
  String isEnterpriseSelected = "";
  List<Map<String, String>> isEnterprise = [
    {"id": "HKD", "name": "Hộ kinh doanh"},
    {"id": "DN", "name": "Doanh nghiệp"},
  ];

  int currentStep = 0;
  PageController pageController = PageController();

  //function
  @override
  void initState() {
    super.initState();
    isEnterpriseSelected = isEnterprise[0]['id'] ?? '';
    isEnterpriseController.text = isEnterprise[0]['name'] ?? '';


    getData();
  }

  getData() async {
    businessTypes = await generalController.getBusinessTypeList();
    setState(() {});
  }

  pickIsEnterprise() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            height: 200,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for (dynamic item in isEnterprise)
                  InkWell(
                    onTap: () {
                      isEnterpriseSelected = item['id'];
                      isEnterpriseController.text = item['name'];
                      setState(() {});
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color:
                                      AppColors.shadow.withValues(alpha: 0.1))),
                          color: item['id'] == isEnterpriseSelected
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : Colors.white),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['name']),
                    ),
                  )
              ],
            ),
          );
        });
  }

  pickBusinessType() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for (dynamic item in businessTypes)
                  InkWell(
                    onTap: () {
                      businessTypeSelected = item['Id'];
                      businessTypeController.text = item['Name'];
                      setState(() {});
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color:
                                      AppColors.shadow.withValues(alpha: 0.1))),
                          color: item['Id'] == businessTypeSelected
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : Colors.white),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['Name']),
                    ),
                  )
              ],
            ),
          );
        });
  }

  // Navigate to next step
  void nextStep() {
    if (currentStep == 0) {
      if (_step1FormKey.currentState!.validate()) {
        setState(() {
          currentStep = 1;
        });
        pageController.nextPage(
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else if (currentStep == 1) {
      register();
    }
  }

  // Navigate to previous step
  void previousStep() {
    if (currentStep > 0) {
      setState(() {
        currentStep = currentStep - 1;
      });
      pageController.previousPage(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  register() async {
    if (!_step2FormKey.currentState!.validate()) {
      return;
    }
    AppFunction.showLoading();
    dynamic result = await authController.register(
        licenseKeyController.text,
        orgNameController.text,
        branchNameController.text,
        userNameController.text,
        passwordController.text,
        confirmPasswordController.text,
        fullNameController.text,
        phoneController.text,
        widget.type,
        businessTypeSelected,
        isEnterpriseSelected,
        businessType1Controller.text);
    AppFunction.hideLoading();

    if (result['success']) {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
      sharedPreferences.setString(
          'account',
          jsonEncode({
            'username': userNameController.text, // Lưu username người dùng nhập
            'password': passwordController.text, // Lưu password người dùng nhập
            'from_register': true, // Đánh dấu đây là từ đăng ký mới
          }));

      // Move to step 3 (completion)
      setState(() {
        currentStep = 2;
      });
      pageController.nextPage(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Đăng ký tài khoản'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            // Step indicator at top
            _buildStepIndicator(),
            SizedBox(height: 20),

            // Page view for steps
            Expanded(
              child: PageView(
                controller: pageController,
                physics: NeverScrollableScrollPhysics(), // Disable swipe
                onPageChanged: (index) {
                  setState(() {
                    currentStep = index;
                  });
                },
                children: [
                  _buildStep1(),
                  _buildStep2(),
                  _buildStep3(),
                ],
              ),
            ),

            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  // Step indicator widget
  Widget _buildStepIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStepDot(0, 'Thông tin\ncơ bản'),
          Container(
            width: 60,
            height: 2,
            decoration: BoxDecoration(
              color: currentStep >= 1 ? AppColors.primary : Colors.grey[300],
              borderRadius: BorderRadius.circular(1),
            ),
          ),
          _buildStepDot(1, 'Đăng ký\ntài khoản'),
          Container(
            width: 60,
            height: 2,
            decoration: BoxDecoration(
              color: currentStep >= 2 ? AppColors.primary : Colors.grey[300],
              borderRadius: BorderRadius.circular(1),
            ),
          ),
          _buildStepDot(2, 'Hoàn thành'),
        ],
      ),
    );
  }

  Widget _buildStepDot(int step, String title) {
    bool isActive = currentStep >= step;
    bool isCurrent = currentStep == step;
    bool isCompleted = currentStep > step;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? AppColors.primary : Colors.grey[300],
            border: isCurrent
                ? Border.all(
                    color: AppColors.primary,
                    width: 3,
                  )
                : null,
          ),
          child: Center(
            child: isCompleted
                ? Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 18,
                  )
                : Text(
                    '${step + 1}',
                    style: TextStyle(
                      color: isActive ? Colors.white : Colors.grey[600],
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
          ),
        ),
        SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isCurrent ? FontWeight.w600 : FontWeight.w500,
            color: isActive ? AppColors.primary : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // Step 1: Basic information
  Widget _buildStep1() {
    return Form(
      key: _step1FormKey,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          if (widget.type == 'acb')
            CustomTextField(
              controller: licenseKeyController,
              label: 'License Key',
              required: true,
            ),
          SizedBox(
            height: 10,
          ),
          CustomTextField(
            controller: isEnterpriseController,
            label: 'Loại hình doanh nghiệp',
            hint: "Chọn loại hình doanh nghiệp",
            readOnly: true,
            required: true,
            isSelect: true,
            onTap: () {
              pickIsEnterprise();
            },
            validator: AuthValidator.validatorIsEnterprise,
          ),
          SizedBox(
            height: 10,
          ),
          if (isEnterpriseSelected == "DN") ...[
            CustomTextField(
              controller: orgNameController,
              label: 'Tên thương hiệu/đơn vị',
              required: true,
              validator: AuthValidator.validatorBrandName,
              hint: "Nhập tên thương hiệu/đơn vị",
            ),
            SizedBox(
              height: 10,
            ),
          ],
          CustomTextField(
            controller: branchNameController,
            label: 'Tên cửa hàng/chi nhánh',
            required: true,
            hint: "Nhập tên cửa hàng/chi nhánh",
            validator: AuthValidator.validatorBranchName,
          ),
          SizedBox(
            height: 10,
          ),
          CustomTextField(
            controller: businessTypeController,
            label: 'Loại hình kinh doanh',
            hint: "Chọn loại hình kinh doanh",
            readOnly: true,
            required: true,
            isSelect: true,
            onTap: () {
              pickBusinessType();
            },
            validator: AuthValidator.validatorBusinessType,
          ),
          if (businessTypeSelected == 8)
            CustomTextField(
              controller: businessType1Controller,
              label: 'Loại hình kinh doanh khác',
              hint: "Nhập loại hình kinh doanh khác",
              required: true,
            ),
        ],
      ),
    );
  }

  // Step 2: Account information
  Widget _buildStep2() {
    return Form(
      key: _step2FormKey,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          CustomTextField(
            controller: userNameController,
            label: 'Tên đăng nhập',
            hint: "Nhập tên đăng nhập",
            required: true,
            validator: AuthValidator.validatorUsername,
          ),
          SizedBox(
            height: 10,
          ),
          CustomTextField(
            controller: fullNameController,
            label: 'Tên của bạn',
            hint: "Nhập tên của bạn",
            required: true,
            validator: AuthValidator.validatorName,
          ),
          SizedBox(
            height: 10,
          ),
          CustomTextField(
            controller: phoneController,
            label: 'Số điện thoại',
            hint: "Nhập số điện thoại",
            keyboard: TextInputType.phone,
            required: true,
            validator: AuthValidator.validatorPhone,
          ),
          SizedBox(
            height: 10,
          ),
          CustomTextField(
            controller: passwordController,
            label: 'Mật khẩu',
            required: true,
            hint: "Nhập mật khẩu",
            secure: true,
            validator: AuthValidator.validatorPassword,
          ),
          SizedBox(
            height: 10,
          ),
          CustomTextField(
            controller: confirmPasswordController,
            label: 'Nhập lại mật khẩu',
            required: true,
            hint: "Nhập lại mật khẩu",
            secure: true,
            validator: (value) {
              return AuthValidator.validatorConfirmPassword(
                  value, passwordController.text);
            },
          ),
        ],
      ),
    );
  }

  // Step 3: Completion
  Widget _buildStep3() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.green,
            ),
            child: Icon(
              Icons.check,
              color: Colors.white,
              size: 60,
            ),
          ),
          SizedBox(height: 32),
          Text(
            'Đăng ký thành công!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'Tài khoản của bạn đã được tạo thành công.\nBạn có thể bắt đầu sử dụng ứng dụng ngay bây giờ.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  // Navigation buttons
  Widget _buildNavigationButtons() {
    if (currentStep == 2) {
      // Step 3: Only show "Hoàn thành" button
      return Container(
        padding: EdgeInsets.symmetric(vertical: 16),
        child: CustomButton(
          text: 'Hoàn thành',
          onTap: () {
            // Quay về màn hình đăng nhập với thông tin đã được điền sẵn
            Get.back(); // Đóng màn hình đăng ký
            Get.back(); // Quay về màn hình trước đó (login)
          },
          color: Colors.green,
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          if (currentStep > 0)
            Expanded(
              child: Container(
                margin: EdgeInsets.only(right: 8),
                child: CustomButton(
                  text: 'Quay lại',
                  onTap: previousStep,
                  color: Colors.grey[600]!,
                ),
              ),
            ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: currentStep > 0 ? 8 : 0),
              child: CustomButton(
                text: currentStep == 1 ? 'Đăng ký' : 'Tiếp theo',
                onTap: nextStep,
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
