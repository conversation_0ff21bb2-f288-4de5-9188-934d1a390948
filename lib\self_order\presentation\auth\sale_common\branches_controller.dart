import 'package:get/get.dart';
import '../../../domain/entities/sale_common/branches_response_entity.dart';
import '../../../domain/usecases/sale_common/branches_usecase.dart';

class BranchesController extends GetxController {
  final BranchesUseCase branchesUseCase;

  BranchesController({required this.branchesUseCase});

  var branchList = <BranchesResponseEntity>[].obs;
  var selectedBranch = Rxn<BranchesResponseEntity>();
  var isLoading = false.obs;
  var error = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchBranches();
  }

  Future<void> fetchBranches() async {
    try {
      isLoading(true);
      final response = await branchesUseCase();
      branchList.value = response;
      selectedBranch.value = null;
    } catch (e) {
      error(e.toString());
      print('Error loading branches: $e');
    } finally {
      isLoading(false);
    }
  }

}
