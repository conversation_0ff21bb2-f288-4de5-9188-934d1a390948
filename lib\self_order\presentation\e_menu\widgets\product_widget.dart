import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/constants/api_url.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_images.dart';
import '../../../../core/theme/app_style.dart';
import '../../../../core/widgets/card_decoration.dart';
import '../../../routes/app_routes.dart';

Widget productItem(
    double width,
    NumberFormat currencyFormat,
    String itemName,
    double price,
    String imageUrl,
    dynamic productData,
    {VoidCallback? onTap}
    ) {
  return GestureDetector(
    onTap: onTap ?? () {
      Get.toNamed(
        AppRoutes.productDetail,
        arguments: productData,
      );
    },
    child: Container(
      decoration: cardDecoration(width * 0.02, AppColors.background),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Product image
          ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(width * 0.02),
              topRight: Radius.circular(width * 0.02),
            ),
            child: Image.network(
              getValidImageUrl(imageUrl),
              height: width * 0.22,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) => Container(
                color: Colors.grey[200],
                height: width * 0.22,
                child: Image.asset(
                  AppImages.product_1,
                  fit: BoxFit.cover,
                ),
                //child: Icon(Icons.fastfood, size: width * 0.1, color: Colors.grey),
              ),
            ),
          ),

          // Product info
          Padding(
            padding: EdgeInsets.all(width * 0.01),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product name
                SizedBox(
                  height: width * 0.08,
                  child: Text(
                    itemName,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: PrimaryFont.bold.copyWith(
                      fontSize: width * 0.03,
                      color: AppColors.text,
                    ),
                  ),
                ),
                SizedBox(height: width * 0.015),
                // Price and add button
                SizedBox(
                  height: width * 0.05,
                  child: Row(
                    children: [
                      Text(
                        currencyFormat.format(price),
                        style: PrimaryFont.bold.copyWith(
                          fontSize: width * 0.03,
                          color: AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: EdgeInsets.all(width * 0.005),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.add,
                          size: width * 0.03,
                          color: AppColors.background,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

String getValidImageUrl(String url) {
  if (url.isEmpty) return ''; // hoặc trả về URL ảnh mặc định

  try {
    final uri = Uri.parse(url);

    // Nếu URL là tuyệt đối (có http/https)
    if (uri.isAbsolute) {
      return url;
    }
    // Nếu URL là tương đối (bắt đầu bằng /)
    else if (url.startsWith('/')) {
      return '${ApiUrl.baseImageUrl}$url';
    }
    // Các trường hợp khác
    else {
      return '${ApiUrl.baseImageUrl}$url';
    }
  } catch (e) {
    return '';
  }
}