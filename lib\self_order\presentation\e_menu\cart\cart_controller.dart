// cart_controller.dart
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class CartController extends GetxController {
  var cartItems = <Map<String, dynamic>>[].obs;
  var totalPrice = 0.0.obs;
  var selectedChoices = <int, Map<String, dynamic>>{}.obs;

  int get totalItemCount {
    return cartItems.fold(0, (sum, item) => sum + (item['quantity'] as int));
  }

  void addToCart(Map<String, dynamic> product, int quantity,
      {Map<String, dynamic>? selectedChoices, String? note}) {
    final itemId = product['ItemId'];
    if (itemId == null) {
      throw Exception('Product does not have ItemId');
    }

    final productCopy = Map<String, dynamic>.from(product);
    if (product['DefaultItems'] != null) {
      productCopy['DefaultItems'] = List.from(product['DefaultItems']);
    }

    if (selectedChoices != null) {
      productCopy['SelectedChoices'] = selectedChoices;
    }

    final existingIndex = cartItems.indexWhere((item) {
      final existingProduct = item['product'];
      final sameId = existingProduct['ItemId'] == itemId;

      final existingChoices = existingProduct['SelectedChoices'];
      if (selectedChoices == null && existingChoices == null) return sameId;

      if (selectedChoices != null && existingChoices != null) {
        return sameId && _mapEquals(selectedChoices, existingChoices);
      }

      return false;
    });

    if (existingIndex >= 0) {
      cartItems[existingIndex]['quantity'] += quantity;
      if (note != null && note.isNotEmpty) {
        cartItems[existingIndex]['note'] = note;
      }
    } else {
      cartItems.add({
        'product': productCopy,
        'quantity': quantity,
        'note': note ?? '',
      });
    }

    calculateTotal();
  }

  void updateNote(int index, String note) {
    if (index >= 0 && index < cartItems.length) {
      cartItems[index]['note'] = note;
      update();
    }
  }

  bool _mapEquals(Map<String, dynamic> a, Map<String, dynamic> b) {
    if (a.length != b.length) return false;
    for (final key in a.keys) {
      final aVal = a[key];
      final bVal = b[key];
      if (aVal is List && bVal is List) {
        if (!_listEquals(aVal, bVal)) return false;
      } else if (aVal != bVal) {
        return false;
      }
    }
    return true;
  }

  bool _listEquals(List a, List b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] is Map && b[i] is Map) {
        if (!_mapEquals(a[i] as Map<String, dynamic>, b[i] as Map<String, dynamic>)) return false;
      } else if (a[i] != b[i]) {
        return false;
      }
    }
    return true;
  }

  void removeFromCart(int index) {
    cartItems.removeAt(index);
    calculateTotal();
  }

  void updateQuantity(int index, int newQuantity) {
    if (newQuantity > 0) {
      cartItems[index]['quantity'] = newQuantity;
      calculateTotal();
    }
  }

  void updateItemQuantity(int index, int newQuantity) {
    if (index >= 0 && index < cartItems.length && newQuantity > 0) {
      cartItems[index]['quantity'] = newQuantity;
      calculateTotal();
      update(); // Thêm dòng này để cập nhật UI
    }
  }

  void calculateTotal() {
    totalPrice.value = cartItems.fold(0.0, (sum, item) {
      final product = item['product'];
      final quantity = item['quantity'];

      // Sửa lại cách lấy giá, thêm kiểm tra null
      final basePrice = (product['Price'] as num?)?.toDouble() ?? 0.0;

      double extraChoicesPrice = 0.0;

      final selectedChoices = product['SelectedChoices'];
      if (selectedChoices != null && selectedChoices is Map<String, dynamic>) {
        for (var choiceItems in selectedChoices.values) {
          if (choiceItems is List) {
            for (var choiceItem in choiceItems) {
              if (choiceItem is Map<String, dynamic>) {
                // Thêm kiểm tra null cho Price của choiceItem
                final extraPrice = (choiceItem['Price'] as num?)?.toDouble() ?? 0.0;
                extraChoicesPrice += extraPrice;
              }
            }
          }
        }
      }

      final totalItemPrice = (basePrice + extraChoicesPrice) * quantity;
      return sum + totalItemPrice;
    });
  }

  void clearCart() {
    cartItems.clear();
    totalPrice.value = 0.0;
  }

  Map<String, dynamic>? getCartItem(int index) {
    if (index >= 0 && index < cartItems.length) {
      final item = cartItems[index];
      debugPrint('Getting cart item at index $index: $item');

      // Đảm bảo product có SelectedChoices nếu nó tồn tại trong item gốc
      final product = Map<String, dynamic>.from(item['product'] ?? {});
      if (item['product']?['SelectedChoices'] != null) {
        product['SelectedChoices'] = Map<String, dynamic>.from(item['product']['SelectedChoices']);
      }

      return {
        'product': product,
        'quantity': item['quantity'] ?? 1,
        'note': item['note'] ?? '',
      };
    }
    return null;
  }
}
