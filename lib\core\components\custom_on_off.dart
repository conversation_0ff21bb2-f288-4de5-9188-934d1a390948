import 'package:flutter/material.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class CustomOnOff extends StatelessWidget {
  final bool controller;
  final VoidCallback onTap;
  const CustomOnOff({super.key, required this.controller, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: onTap,
        child: Container(
          width: 45,
          height: 25,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(180),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.05),
                  spreadRadius: 0,
                  blurRadius: 1,
                  offset: Offset(0, 3)
              ),
            ],
          ),
          padding: EdgeInsets.all(2.5),
          child: Row(
            mainAxisAlignment: controller ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(180),
                    color: controller ? AppColors.primary : AppColors.shadow
                ),
              )
            ],
          ),
        )
    );
  }
}
