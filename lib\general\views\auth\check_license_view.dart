import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';
import 'package:gls_self_order/general/views/auth/login_view.dart';
import 'package:gls_self_order/general/views/auth/verify_license_view.dart';

class CheckLicenseView extends StatefulWidget {
  const CheckLicenseView({super.key});

  @override
  State<CheckLicenseView> createState() => _CheckLicenseViewState();
}

class _CheckLicenseViewState extends State<CheckLicenseView> {
  //variable
  final PageStorageBucket bucket = PageStorageBucket();
  Widget currentView = const VerifyLicenseView();
  AuthController authController = Get.find();

  //function
  @override
  void initState() {
    super.initState();
    checkLicense();
  }

  checkLicense() async {
    String? code = await authController.checkLicense();
    if (code != null) {
      GlobalVar.licenseCode = code;
      authController.verified.value = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageStorage(
        bucket: bucket,
        child: Obx(() {
          if (authController.verified.value) {
            return LoginView();
          }
          return VerifyLicenseView();
        }),
      ),
    );
  }
}
