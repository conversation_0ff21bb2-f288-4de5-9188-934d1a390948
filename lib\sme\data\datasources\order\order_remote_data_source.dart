import '../../../../core/constants/api_url.dart';
import '../../../../core/constants/sme_url.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../general/classes/user_info.dart';
import '../../models/order/acb_qr_response_model.dart';
import '../../models/order/order_request_model.dart';
import '../../models/order/order_response_model.dart';
import '../../models/order/status_request_model.dart';
import '../../models/order/status_response_model.dart';
import '../../models/order/zalo_qr_response_model.dart';

abstract class OrderRemoteDataSource {
  Future<OrderResponseModel> createOrder(OrderRequestModel request);
  Future<ZALOQRResponseModel> generateZALOQR(String orderCode);
  Future<ACBQRResponseModel> generateACBQR(String orderCode);
  Future<StatusResponseModel> updateStatus(StatusRequestModel request);
}

class OrderRemoteDataSourceImpl implements OrderRemoteDataSource {
  final DioClientSME dioClient;

  OrderRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<OrderResponseModel> createOrder(OrderRequestModel request) async {
    try {
      final response = await dioClient.post(
        SmeUrl.order,
        data: request.toJson(),
      );
      return OrderResponseModel.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ZALOQRResponseModel> generateZALOQR(String orderCode) async {
    try {
      final response = await dioClient.post(
        '${SmeUrl.generateZALOQRSME}/$orderCode',
      );
      return ZALOQRResponseModel.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ACBQRResponseModel> generateACBQR(String orderCode) async {
    try {
      final response = await dioClient.post(
        '${SmeUrl.generateACBQRSME}/$orderCode?branchId=${UserInfo.branchId}&deviceId=${UserInfo.deviceId}',
      );
      return ACBQRResponseModel.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<StatusResponseModel> updateStatus(StatusRequestModel request) async {
    try {
      final response = await dioClient.post(
        SmeUrl.updateOrderStatusSME,
        data: request.toJson(),
      );
      return StatusResponseModel.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }
}