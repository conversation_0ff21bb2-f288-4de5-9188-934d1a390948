class AreasListResponseModel {
  final bool success;
  final int code;
  final String message;
  final List<AreasModel> areas;
  final dynamic errorDetail;

  AreasListResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.areas,
    required this.errorDetail,
  });

  factory AreasListResponseModel.fromJson(Map<String, dynamic> json) {
    return AreasListResponseModel(
      success: json["Success"],
      code: json["Code"],
      message: json["Message"],
      areas: json["Result"] == null
          ? []
          : List<AreasModel>.from(
          json["Result"].map((x) => AreasModel.fromJson(x))),
      errorDetail: json["ErrorDetail"],
    );
  }
}


class AreasModel {
  final int? areaId;
  final String? areaName;

  AreasModel({
    required this.areaId,
    required this.areaName,
  });

  factory AreasModel.fromJson(Map<String, dynamic> json) {
    return AreasModel(
      areaId: json["AreaId"],
      areaName: json["AreaName"],
    );
  }
}
