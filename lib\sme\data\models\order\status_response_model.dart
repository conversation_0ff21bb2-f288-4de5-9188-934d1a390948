class StatusResponseModel {
  StatusResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.result,
    required this.errorDetail,
  });

  final bool? success;
  final int? code;
  final String? message;
  final dynamic result;
  final dynamic errorDetail;

  factory StatusResponseModel.fromJson(Map<String, dynamic> json){
    return StatusResponseModel(
      success: json["Success"],
      code: json["Code"],
      message: json["Message"],
      result: json["Result"],
      errorDetail: json["ErrorDetail"],
    );
  }

}