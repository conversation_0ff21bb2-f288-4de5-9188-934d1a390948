import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:intl/intl.dart';

class CustomerCreateOrUpdateView extends StatefulWidget {
  final dynamic item;
  const CustomerCreateOrUpdateView({super.key, this.item});

  @override
  State<CustomerCreateOrUpdateView> createState() => _CustomerCreateOrUpdateViewState();
}

class _CustomerCreateOrUpdateViewState extends State<CustomerCreateOrUpdateView> {
  //variable
  CustomerController customerController = Get.find();
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController dobController = TextEditingController();
  TextEditingController genderController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  DateTime? dateOfBirth;
  DateTime today = DateTime.now();
  List genders = [
    {'id': 1, 'name': 'Nam'},
    {'id': 2, 'name': 'Nữ'},
  ];
  int gender = 1;
  
  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    if (widget.item != null) {
      dynamic item = await customerController.getDetail(widget.item['CustomerNo']);
      if (item != null) {
        nameController.text = item['FullName'] ?? '';
        phoneController.text = item['PhoneNumber'] ?? '';
        if (item['Birthday'] != null) {
          dateOfBirth = DateTime.parse(item['Birthday']);
          dobController.text = AppFunction.formatDateNoTime(item['Birthday']);
        }
        if (item['Gender'] != null && item['Gender'] != 0) {
          gender = item['Gender'];
        }
        addressController.text = item['CustomerAdress'] ?? '';
        setState(() {

        });
      }
    }
  }

  Future<void> selectDate(BuildContext context) async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: today,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (date != null) {
      dateOfBirth = DateTime(
        date.year,
        date.month,
        date.day,
      );
      dobController.text = AppFunction.formatDateNoTime(dateOfBirth);
    }
  }
  
  save() async {
    AppFunction.showLoading();
    if (widget.item != null) {
      bool success = await customerController.postUpdate(
          widget.item['CustomerId'],
          nameController.text,
          phoneController.text,
          dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth!) : null,
          gender,
          addressController.text
      );
      if (success) {
        Get.back(result: success);
      }
    }
    else {
      int customerId = await customerController.postCreate(
          nameController.text,
          phoneController.text,
          dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth!) : null,
          gender,
          addressController.text
      );
      if (customerId != 0) {
        Get.back(result: customerId);
      }
    }
    AppFunction.hideLoading();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: widget.item != null ? 'Chỉnh sửa khách hàng' : 'Thêm khách hàng'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            CustomTextField(
              controller: nameController,
              label: 'Họ tên',
              required: true,
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Số điện thoại',
            ),
            CustomTextField(
              controller: dobController,
              label: 'Ngày sinh',
              readOnly: true,
              onTap: () {
                selectDate(context);
              },
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(0, 0, 0, 2.5),
              child: CustomText(text: 'Giới tính',),
            ),
            DecoratedBox(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(7.5),
                border: Border.all(
                  width: 0.5,
                  color: Colors.black.withValues(alpha: 0.2)
                )
              ),
              child: DropdownButton(
                value: gender,
                items: [
                  for (dynamic item in genders)
                    DropdownMenuItem(
                      value: item['id'],
                      child: CustomText(text: item['name']),
                    )
                ],
                onChanged: (value) {
                  gender = int.parse(value.toString());
                  setState(() {

                  });
                },
                underline: Container(),
                isExpanded: true,
                padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
              ),
            ),
            SizedBox(height: 10,),
            CustomTextField(
              controller: addressController,
              label: 'Địa chỉ',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }
}
