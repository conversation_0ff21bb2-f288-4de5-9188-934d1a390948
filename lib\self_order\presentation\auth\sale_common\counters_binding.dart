import 'package:get/get.dart';

import '../../../data/datasources/sale_common/counters_remote_data_source.dart';
import '../../../data/repositories/sale_common/couters_repository_impl.dart';
import '../../../domain/repositories/sale_common/couters_repository.dart';
import '../../../domain/usecases/sale_common/couters_usecase.dart';
import 'counters_controller.dart';

class CounterBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<CounterRemoteDataSource>(
          () => CounterRemoteDataSourceImpl(dioClient: Get.find()),
    );

    Get.lazyPut<CounterRepository>(
          () => CounterRepositoryImpl(remoteDataSource: Get.find()),
    );

    Get.lazyPut<GetCountersUseCase>(
          () => GetCountersUseCase(repository: Get.find()),
    );

    Get.lazyPut<CounterController>(
          () => CounterController(getCountersUseCase: Get.find()),
    );
  }
}
