class ProductCreateOrUpdateValidator {
  static String? validatorProductName(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập tên sản phẩm';
    }
    return null;
  }

  static String? validatorOriginalPrice(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập giá vốn sản phẩm';
    }
    return null;
  }
  static String? validatorPrice(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập giá bán sản phẩm';
    }
    return null;
  }
  static String? validatorCategory(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng chọn danh mục';
    }
    return null;
  }
  static String? validatorUnit(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng chọn đơn vị tính';
    }
    return null;
  }
  static String? validatorVatPercent(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập phần trăm VAT';
    }
    // Kiểm tra xem có phải là số hợp lệ không
    final numericValue = double.tryParse(value.replaceAll('.', '').replaceAll(',', '.'));
    if (numericValue == null) {
      return 'Phần trăm VAT không hợp lệ';
    }
    if (numericValue < 0 || numericValue > 100) {
      return 'Phần trăm VAT phải từ 0 đến 100';
    }
    return null;
  }

  static String? validatorCategoryName(String? value){
    if (value == null || value.isEmpty) {
      return 'Vui lòng chọn tên danh mục';
    }
    return null;
  }
}