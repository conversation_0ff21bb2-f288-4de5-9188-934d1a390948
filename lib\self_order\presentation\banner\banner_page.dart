import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/api_url.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_style.dart';
import '../auth/login/auth_controller.dart';
import 'banner_controller.dart';

class BannerPage extends StatefulWidget {
  const BannerPage({super.key});

  @override
  State<BannerPage> createState() => _BannerPageState();
}

class _BannerPageState extends State<BannerPage> with TickerProviderStateMixin {
  late BannerController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<BannerController>();
    controller.initAnimations(this);
  }

  @override
  void dispose() {
    controller.rippleController.dispose();
    controller.handController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    return Stack(
      children: [
        Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                // Banner
                Expanded(
                  flex: 12,
                  child: Obx(() {
                    if (controller.isLoading.value) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    return CarouselSlider(
                      options: CarouselOptions(
                        autoPlay: true,
                        viewportFraction: 1.0,
                        height: double.infinity,
                        enlargeCenterPage: false,
                        autoPlayInterval: const Duration(seconds: 3),
                        autoPlayAnimationDuration:
                        const Duration(milliseconds: 800),
                        enableInfiniteScroll: true,
                      ),
                      items: controller.bannerImages.map((imageUrl) {
                        return Builder(
                          builder: (BuildContext context) {
                            return Image.network(
                              ApiUrl.baseImageUrl + imageUrl,
                              fit: BoxFit.cover,
                            );
                          },
                        );
                      }).toList(),
                    );
                  }),
                ),

                // Button
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: () => Get.toNamed('/start-order-page'),
                    child: Container(
                      width: double.infinity,
                      color: AppColors.primary,
                      child: Stack(
                        alignment: Alignment.centerLeft,
                        children: [
                          Center(
                            child: Text(
                              "order_here".tr,
                              style: PrimaryFont.bold.copyWith(
                                color: AppColors.background,
                                fontSize: width * 0.05,
                              ),
                            ),
                          ),
                          Positioned(
                            right: width * 0.13,
                            bottom: width * 0.05,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                AnimatedBuilder(
                                  animation: controller.rippleController,
                                  builder: (context, child) {
                                    double scale = 1 +
                                        controller.rippleController.value * 2;
                                    return Transform.scale(
                                      scale: scale,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.white.withOpacity(
                                              1 -
                                                  controller
                                                      .rippleController.value),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                                SlideTransition(
                                  position: controller.handOffsetAnimation,
                                  child: Transform.translate(
                                    offset: const Offset(8, 10),
                                    child: Transform.rotate(
                                      angle: -0.65,
                                      child: const Icon(
                                        Icons.touch_app,
                                        size: 50,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.lock_open, color: Colors.grey),
              onPressed: () {
                Get.find<AuthSOController>().logout(context, width);
              },
            ),
          ),
        ),
      ],
    );
  }
}