import '../../../domain/entities/order/acb_qr_response_entity.dart';
import '../../../domain/entities/order/order_reponse_entity.dart';
import '../../../domain/entities/order/order_request_entity.dart';
import '../../../domain/entities/order/status_request_entity.dart';
import '../../../domain/entities/order/status_response_entity.dart';
import '../../../domain/entities/order/zalo_qr_response_entity.dart';
import '../../../domain/repositories/order/order_repository.dart';
import '../../datasources/order/order_remote_data_source.dart';
import '../../models/order/order_request_model.dart';
import '../../models/order/status_request_model.dart';

class OrderRepositoryImpl implements OrderRepository {
  final OrderRemoteDataSource remoteDataSource;

  OrderRepositoryImpl({required this.remoteDataSource});

  @override
  Future<OrderResponseEntity> createOrder(OrderRequestEntity request) async {
    final requestModel = OrderRequestModel(
      menuId: request.menuId,
      timeSaleMenuId: request.timeSaleMenuId,
      branchId: request.branchId,
      customerPhone: request.customerPhone,
      customerNo: request.customerNo,
      customerName: request.customerName,
      orderNote: request.orderNote,
      orderType: request.orderType,
      orderDetails: request.orderDetails.map((detail) => OrderDetailModel(
        itemNo: detail.itemNo,
        itemName: detail.itemName,
        itemQuantity: detail.itemQuantity,
        itemPrice: detail.itemPrice,
        discountAmount: detail.discountAmount,
        itemNote: detail.itemNote,
        children: detail.children?.map((child) => ChildrenModel(
            itemNo: child.itemNo,
            itemName: child.itemName,
            itemQuantity: child.itemQuantity,
            itemPrice: child.itemPrice,
            discountAmount: child.discountAmount,
        )).toList(),
      )).toList(),
    );

    final response = await remoteDataSource.createOrder(requestModel);

    return OrderResponseEntity(
      success: response.success,
      code: response.code,
      message: response.message,
      result: response.result != null
          ? OrderResultEntity(
        orderResponse: OrderResponse(
          orderId: response.result!.orderResponse.orderId,
          branchId: response.result!.orderResponse.branchId,
          branchName: response.result!.orderResponse.branchName,
          orderCode: response.result!.orderResponse.orderCode,
          ticketCode: response.result!.orderResponse.ticketCode,
          orderType: response.result!.orderResponse.orderType,
          statusCode: response.result!.orderResponse.statusCode,
          paymentMethod: response.result!.orderResponse.paymentMethod,
          paymentKey: response.result!.orderResponse.paymentKey,
          paymentMsg: response.result!.orderResponse.paymentMsg,
          customerPhone: response.result!.orderResponse.customerPhone,
          customerNo: response.result!.orderResponse.customerNo,
          customerName: response.result!.orderResponse.customerName,
          orderSubTotalVAT: response.result!.orderResponse.orderSubTotalVAT,
          orderDiscountAmountVAT: response.result!.orderResponse.orderDiscountAmountVAT,
          orderVATAmount: response.result!.orderResponse.orderVATAmount,
          orderTotalVAT: response.result!.orderResponse.orderTotalVAT,
          orderSubTotalView: response.result!.orderResponse.orderSubTotalView,
          orderDiscountAmountView: response.result!.orderResponse.orderDiscountAmountView,
          orderTotalView: response.result!.orderResponse.orderTotalView,
          orderNote: response.result!.orderResponse.orderNote,
          createdAt: response.result!.orderResponse.createdAt,
          createdBy: response.result!.orderResponse.createdBy,
          rowVersion: response.result!.orderResponse.rowVersion,
        ),
        orderDetails: response.result!.orderDetails.map((detail) => OrderDetailResponse(
          detailId: detail.detailId,
          isParent: detail.isParent,
          detailCode: detail.detailCode,
          orderId: detail.orderId,
          orderCode: detail.orderCode,
          ticketItemId: detail.ticketItemId,
          itemNo: detail.itemNo,
          itemName: detail.itemName,
          itemNote: detail.itemNote,
          itemVATPer: detail.itemVATPer,
          includeVAT: detail.includeVAT,
          itemQty: detail.itemQty,
          itemPriceVAT: detail.itemPriceVAT,
          subTotalVAT: detail.subTotalVAT,
          discountAmountVAT: detail.discountAmountVAT,
          vatAmount: detail.vatAmount,
          totalAmountVAT: detail.totalAmountVAT,
          itemPrice: detail.itemPrice,
          subTotal: detail.subTotal,
          discountAmount: detail.discountAmount,
          totalAmount: detail.totalAmount,
          createdDate: detail.createdDate,
          children: detail.children?.map((child) => OrderDetailResponse(
            detailId: child.detailId,
            isParent: child.isParent,
            detailCode: child.detailCode,
            orderId: child.orderId,
            orderCode: child.orderCode,
            ticketItemId: child.ticketItemId,
            itemNo: child.itemNo,
            itemName: child.itemName,
            itemNote: child.itemNote,
            itemVATPer: child.itemVATPer,
            includeVAT: child.includeVAT,
            itemQty: child.itemQty,
            itemPriceVAT: child.itemPriceVAT,
            subTotalVAT: child.subTotalVAT,
            discountAmountVAT: child.discountAmountVAT,
            vatAmount: child.vatAmount,
            totalAmountVAT: child.totalAmountVAT,
            itemPrice: child.itemPrice,
            subTotal: child.subTotal,
            discountAmount: child.discountAmount,
            totalAmount: child.totalAmount,
            createdDate: child.createdDate,
            children: null,
          )).toList(),
        )).toList(),
      )
          : null,
      errorDetail: response.errorDetail,
    );
  }

  @override
  Future<ZALOQRResponseEntity> generateZALOQR(String orderCode) async {
    final response = await remoteDataSource.generateZALOQR(orderCode);

    return ZALOQRResponseEntity(
      success: response.success,
      code: response.code,
      message: response.message,
      result: response.result != null
          ? ZALOQRResultEntity(
        returnCode: response.result!.returnCode,
        returnMessage: response.result!.returnMessage,
        subReturnCode: response.result!.subReturnCode,
        subReturnMessage: response.result!.subReturnMessage,
        zpTransToken: response.result!.zpTransToken,
        orderUrl: response.result!.orderUrl,
        orderToken: response.result!.orderToken,
        qrCode: response.result!.qrCode,
      )
          : null,
    );
  }

  @override
  Future<ACBQRResponseEntity> generateACBQR(String orderCode) async {
    final response = await remoteDataSource.generateACBQR(orderCode);

    return ACBQRResponseEntity(
      success: response.success,
      code: response.code,
      message: response.message,
      result: response.result != null
          ? ACBQRResultEntity(
        date: response.result!.date,
        qrPay: response.result!.qrPay,
      ) : null,
    );
  }

  @override
  Future<StatusResponseEntity> updateStatus(StatusRequestEntity request) async {
    final requestModel = StatusRequestModel(
      key: request.key,
      orderCode: request.orderCode,
      msg: request.msg,
      statusCode: request.statusCode,
    );

    final response = await remoteDataSource.updateStatus(requestModel);

    return StatusResponseEntity(
      success: response.success,
      code: response.code,
      message: response.message,
      result: response.result,
      errorDetail: response.errorDetail,
    );
  }
}