import '../../../../core/constants/api_url.dart';
import '../../../../core/network/dio_client.dart';
import '../../models/order/order_request_model.dart';
import '../../models/order/order_response_model.dart';
import '../../models/order/zalo_qr_response_model.dart';

abstract class OrderRemoteDataSource {
  Future<OrderResponseModel> createOrder(OrderRequestModel request);
  Future<ZALOQRResponseModel> generateZALOQR(String orderCode);
}

class OrderRemoteDataSourceImpl implements OrderRemoteDataSource {
  final DioClient dioClient;

  OrderRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<OrderResponseModel> createOrder(OrderRequestModel request) async {
    try {
      final response = await dioClient.post(
        ApiUrl.order,
        data: request.toJson(),
      );
      return OrderResponseModel.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ZALOQRResponseModel> generateZALOQR(String orderCode) async {
    try {
      final response = await dioClient.post(
        '${ApiUrl.generateZALOQR}/$orderCode',
      );
      return ZALOQRResponseModel.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }
}