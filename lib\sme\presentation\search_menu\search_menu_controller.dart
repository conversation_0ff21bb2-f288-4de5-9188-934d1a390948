import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../routes/sme_app_routes.dart';
import '../e_menu/cart/cart_controller.dart';
import '../e_menu/e_menu_controller.dart';

class SearchMenuController extends GetxController {
  final EMenuController menuController = Get.find<EMenuController>();
  final CartSMEController cartController = Get.find();
  late TextEditingController searchController;

  var searchQuery = ''.obs;
  var recentlyViewedProducts = <Map<String, dynamic>>[].obs;
  var searchResults = <Map<String, dynamic>>[].obs;
  var shouldShowKeyboard = false.obs;

  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController(text: searchQuery.value);
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  void search(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      searchResults.assignAll(menuController.allProducts);
    } else {
      final results = menuController.allProducts.where((product) {
        final name = (product['ItemName'] ?? '').toString().toLowerCase();
        return name.contains(query.toLowerCase());
      }).toList();
      searchResults.assignAll(results);
    }
  }

  void addToRecentlyViewed(Map<String, dynamic> product) {
    if (!recentlyViewedProducts.any((p) => p['ItemId'] == product['ItemId'])) {
      recentlyViewedProducts.insert(0, product);
      if (recentlyViewedProducts.length > 10) {
        recentlyViewedProducts.removeLast();
      }
    }
  }

  void clearSearch() {
    searchQuery.value = '';
    searchController.clear();
    searchResults.assignAll(menuController.allProducts);
  }

  void viewProductDetail(Map<String, dynamic> product) {
    addToRecentlyViewed(product);
    Get.toNamed(
      SmeAppRoutes.productDetail,
      arguments: product,
    );
  }

  void resetSearchState() {
    searchQuery.value = '';
    searchResults.assignAll(menuController.allProducts);
    shouldShowKeyboard.value = false;
  }

  void prepareForNewSearch() {
    shouldShowKeyboard.value = true;
  }

}