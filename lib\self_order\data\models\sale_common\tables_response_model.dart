class TablesListResponseModel {
  final bool success;
  final int code;
  final String message;
  final List<TablesModel> tables;
  final dynamic errorDetail;

  TablesListResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.tables,
    required this.errorDetail,
  });

  factory TablesListResponseModel.fromJson(Map<String, dynamic> json) {
    return TablesListResponseModel(
      success: json["Success"],
      code: json["Code"],
      message: json["Message"],
      tables: json["Result"] == null
          ? []
          : List<TablesModel>.from(
          json["Result"].map((x) => TablesModel.fromJson(x))),
      errorDetail: json["ErrorDetail"],
    );
  }
}


class TablesModel {
  final int? tableId;
  final String? tableNo;

  TablesModel({
    required this.tableId,
    required this.tableNo,
  });

  factory TablesModel.fromJson(Map<String, dynamic> json) {
    return TablesModel(
      tableId: json["TableId"],
      tableNo: json["TableNo"],
    );
  }
}
