import 'package:hive/hive.dart';

import '../../models/e_menu/menu_tree_sme_model.dart';

abstract class MenuSMELocalDataSource {
  Future<void> cacheMenuTreeSME(MenuTreeSMEModel model);
  Future<MenuTreeSMEModel?> getCachedMenuTreeSME();
}

class MenuSMELocalDataSourceImpl implements MenuSMELocalDataSource {
  final String boxName = 'menuBoxSME';

  @override
  Future<void> cacheMenuTreeSME(MenuTreeSMEModel model) async {
    final box = await Hive.openBox<MenuTreeSMEModel>(boxName);
    await box.put('menu_tree_sme', model);
  }

  @override
  Future<MenuTreeSMEModel?> getCachedMenuTreeSME() async {
    final box = await Hive.openBox<MenuTreeSMEModel>(boxName);
    return box.get('menu_tree_sme');
  }
}
