class StatusRequestModel {
  StatusRequestModel({
    required this.key,
    required this.orderCode,
    required this.msg,
    required this.statusCode,
  });

  final String? key;
  final String? orderCode;
  final String? msg;
  final String? statusCode;

  Map<String, dynamic> toJson() => {
    "Key": key,
    "OrderCode": orderCode,
    "Msg": msg,
    "StatusCode": statusCode,
  };

  factory StatusRequestModel.fromJson(Map<String, dynamic> json){
    return StatusRequestModel(
      key: json["Key"],
      orderCode: json["OrderCode"],
      msg: json["Msg"],
      statusCode: json["StatusCode"],
    );
  }

}