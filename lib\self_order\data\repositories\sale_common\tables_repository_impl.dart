import '../../../domain/entities/sale_common/tables_reponse_entity.dart';
import '../../../domain/repositories/sale_common/tables_repository.dart';
import '../../datasources/sale_common/tables_remote_data_source.dart';

class TablesRepositoryImpl implements TablesRepository {
  final TablesRemoteDataSource remoteDataSource;

  TablesRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<TablesResponseEntity>> getTables(int branchId, int areaId) async {
    final models = await remoteDataSource.getTables(branchId, areaId);
    return models
        .map((e) =>
        TablesResponseEntity(
          tableId: e.tableId,
          tableNo: e.tableNo,
        ))
        .toList();
  }
}
