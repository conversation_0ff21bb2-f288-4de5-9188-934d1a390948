import '../../../../core/constants/api_url.dart';
import '../../../../core/network/dio_client.dart';

abstract class MenuRemoteDataSource {
  Future<Map<String, dynamic>> fetchMenuInfo();
  Future<Map<String, dynamic>> fetchMenuTree();
}

class MenuRemoteDataSourceImpl implements MenuRemoteDataSource {
  final DioClient dioClient;

  MenuRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<Map<String, dynamic>> fetchMenuInfo() async {
    final response = await dioClient.get(ApiUrl.menuInfo);
    return response.data['Result'];
  }

  @override
  Future<Map<String, dynamic>> fetchMenuTree() async {
    final response = await dioClient.get(ApiUrl.menuTree);
    return response.data['Result'];
  }
}
