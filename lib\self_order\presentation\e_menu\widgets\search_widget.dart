import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_style.dart';
import '../search_menu/search_menu_controller.dart';
import '../search_menu/search_menu_page.dart';

Widget searchBar(double width, [bool isSearchPage = false]) {
  final searchController = Get.find<SearchMenuController>();
  final focusNode = FocusNode();

  return TextFormField(
    focusNode: isSearchPage ? focusNode : null,
    readOnly: !isSearchPage,
    onTap: () {
      if (!isSearchPage) {
        searchController.prepareForNewSearch();
        Get.to(() => SearchMenuPage());
      }
    },
    onChanged: (value) {
      if (isSearchPage) {
        searchController.search(value);
      }
    },
    controller: isSearchPage
        ? TextEditingController(text: searchController.searchQuery.value)
        : null,
    decoration: InputDecoration(
      hintText: 'start_order'.tr,
      hintStyle: PrimaryFont.regular.copyWith(
        color: AppColors.shadow,
        fontSize: width * 0.03,
      ),
      filled: true,
      fillColor: Colors.white,
      prefixIcon: Padding(
        padding: EdgeInsets.all(width * 0.01),
        child: Icon(Icons.search, size: width * 0.05, color: AppColors.text),
      ),
      suffixIcon: isSearchPage && searchController.searchQuery.isNotEmpty
          ? IconButton(
        icon: Icon(Icons.close, size: width * 0.05),
        onPressed: () {
          searchController.clearSearch();
        },
      )
          : null,
      contentPadding: EdgeInsets.symmetric(vertical: width * 0.015),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(width * 0.03),
        borderSide: BorderSide(
          color: AppColors.text.withOpacity(0.2),
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(width * 0.03),
        borderSide: BorderSide(
          color: AppColors.text.withOpacity(0.5),
          width: 2.0,
        ),
      ),
    ),
  );
}
