import '../../../domain/entities/banner/banner_response_entity.dart';
import '../../../domain/repositories/banner/banner_repository.dart';
import '../../datasources/banner/banner_remote_data_source.dart';

class BannerRepositoryImpl implements BannerRepository {
  final BannerRemoteDataSource remoteDataSource;

  BannerRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<BannerResponseEntity>> getBanner() async {
    final models = await remoteDataSource.getBanner();
    return models.map((e) => BannerResponseEntity(
      id: e.id,
      fileUrl: e.fileUrl,
      contentId: e.contentId,
      isActive: e.isActive,
      lastUpdatedAt: e.lastUpdatedAt,
      branchId: e.branchId,
    )).toList();
  }
}
