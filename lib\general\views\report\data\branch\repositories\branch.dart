import 'package:dartz/dartz.dart';
import '../../../common/helper/mapper/branch_mapper.dart';
import '../../../domain/branch/repositories/branch.dart';
import '../../../service_locator.dart';
import '../models/branch.dart';
import '../sources/branch.dart';

class BranchRepositoryImpl extends BranchRepository {
  @override
  Future<Either> getBranch() async {
    var data = await sl<BranchService>().getBranch();
    return data.fold(
      (error) {
        return Left(error);
      },
      (data) async {
        // Lấy thông tin brand từ BranchLogin
        final branchLogin = data['Result']['OrgInfoLogin'];
        final mainBrand = BranchModel(
          branchId: branchLogin['OrgId'],
          branchName: branchLogin['OrgName'],
          branchCode: branchLogin['OrgCode'],
          branchParentId: -1, // <PERSON><PERSON>h dấu là brand chính
        );

        // <PERSON><PERSON><PERSON> danh sách branches từ BranchesAssgined
        final branchesAssigned = List.from(data['Result']['BranchesAssgined'])
            .map((item) => BranchModel.fromJson(item))
            .toList();

        // Kết hợp brand chính và các branches
        final allBranches = [mainBrand]..addAll(branchesAssigned);

        // Chuyển đổi sang entity
        final entities = allBranches.map((e) => BranchMapper.toEntity(e)).toList();
        return Right(entities);
      },
    );
  }
}
