import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/helper/format_helper.dart';
import '../../domain/report/entities/report.dart';

class TopCatsRevenueByDate extends StatelessWidget {
  final double width;
  final String title;
  final List<TopCatsRevenueByDateEntity> topCatsRevenueByDate;

  const TopCatsRevenueByDate({
    super.key,
    required this.width,
    required this.title,
    required this.topCatsRevenueByDate,
  });

  @override
  Widget build(BuildContext context) {
    double maxRevenue = _getMaxRevenue();
    double maxY = _calculateMaxY(maxRevenue);
    final List<DateTime> allDates = topCatsRevenueByDate.map((e) => e.revenueDate ?? DateTime.now()).toList();
    final double bottomInterval = allDates.length <= 7 ? 1 : (allDates.length / 6).floorToDouble().clamp(1, 10);


    return Container(
      margin: EdgeInsets.symmetric(horizontal: width * 0.015),
      padding: EdgeInsets.all(width * 0.015),
      decoration: cardDecoration(width * 0.03, AppColors.background),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(width * 0.015),
            child: Text(
              title.toUpperCase(),
              style: PrimaryFont.bold.copyWith(
                color: AppColors.text,
                fontSize: width * 0.04,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: width * 0.05),
          SizedBox(
            height: width * 0.8,
            child: LineChart(
              LineChartData(
                minX: topCatsRevenueByDate.length == 1 ? -0.5 : 0,
                maxX: topCatsRevenueByDate.length == 1 ? 0.5 : (topCatsRevenueByDate.length - 1).toDouble(),
                minY: 0,
                maxY: maxY,
                gridData: FlGridData(
                  drawVerticalLine: false,
                  drawHorizontalLine: true,
                  horizontalInterval: maxY / 5,
                  getDrawingHorizontalLine: (value) => FlLine(
                    color: Colors.grey.shade300,
                    strokeWidth: 1,
                    dashArray: [5, 5],
                  ),
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          FormatHelper.formatCurrency(value),
                          style: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      interval: bottomInterval,
                      getTitlesWidget: (value, meta) {
                        int index = value.toInt();
                        if (index < 0 || index >= allDates.length) return const SizedBox();

                        return Padding(
                          padding: EdgeInsets.only(top: width * 0.015),
                          child: Text(
                            DateFormat('dd/MM').format(allDates[index]),
                            style: PrimaryFont.regular.copyWith(
                              fontSize: width * 0.03,
                              color: AppColors.text,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          FormatHelper.formatCurrency(value),
                          style: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border(
                    left: BorderSide(color: Colors.black, width: 1),
                    bottom: BorderSide(color: Colors.black, width: 1),
                    right: BorderSide(color: Colors.black, width: 1),
                  ),
                ),
                lineBarsData: _buildLineBars(),
              ),
            ),
          ),
          _buildLegend(),
        ],
      ),
    );
  }


  double _getMaxRevenue() {
    double maxRevenue = 0;
    for (var entry in topCatsRevenueByDate) {
      for (var revenue in entry.revenues.values) {
        if (revenue > maxRevenue) {
          maxRevenue = revenue;
        }
      }
    }
    return maxRevenue;
  }

  double _calculateMaxY(double maxRevenue) {
    if (maxRevenue <= 0) return 1.0;

    final double exponent = (log(maxRevenue) / ln10).floorToDouble();
    final double fraction = maxRevenue / pow(10, exponent).toDouble();

    double niceFraction;
    if (fraction <= 1.5) {
      niceFraction = 1.5;
    } else if (fraction <= 3) {
      niceFraction = 3;
    } else if (fraction <= 7) {
      niceFraction = 7;
    } else if (fraction <= 10) {
      niceFraction = 10;
    } else {
      niceFraction = 15;
    }

    double niceMax = niceFraction * pow(10, exponent).toDouble();

    final double minGap = maxRevenue * 0.15;
    if (niceMax - maxRevenue < minGap) {
      niceMax = maxRevenue + minGap;

      final double newExponent = (log(niceMax) / ln10).floorToDouble();
      final double newFraction = niceMax / pow(10, newExponent).toDouble();
      niceMax = (newFraction.ceilToDouble() * pow(10, newExponent)).toDouble();
    }

    return niceMax;
  }

  List<LineChartBarData> _buildLineBars() {
    Map<String, List<FlSpot>> categoryData = {};
    final colors = _generateColors();

    for (int i = 0; i < topCatsRevenueByDate.length; i++) {
      var entry = topCatsRevenueByDate[i];
      entry.revenues.forEach((category, revenue) {
        categoryData.putIfAbsent(category, () => []).add(FlSpot(i.toDouble(), revenue));
      });
    }

    return categoryData.entries.map((entry) {
      return LineChartBarData(
        spots: entry.value,
        barWidth: 1.5,
        isStrokeCapRound: true,
        color: colors[entry.key]!,
        belowBarData: BarAreaData(show: false),
      );
    }).toList();
  }

  Widget _buildLegend() {
    final colors = _generateColors();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 10,
        runSpacing: 5,
        children: colors.entries.map((entry) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: entry.value,
                ),
              ),
              const SizedBox(width: 5),
              Text(entry.key, style: const TextStyle(fontSize: 12)),
            ],
          );
        }).toList(),
      ),
    );
  }

  Map<String, Color> _generateColors() {
    final baseColors = [Colors.blue, Colors.green, Colors.red, Colors.orange, Colors.purple,
      Colors.teal, Colors.yellow, Colors.pink, Colors.cyan, Colors.brown];
    final Map<String, Color> colorMap = {};
    int index = 0;

    for (var entry in topCatsRevenueByDate) {
      for (var category in entry.revenues.keys) {
        colorMap.putIfAbsent(category, () => baseColors[index % baseColors.length]);
        index++;
      }
    }
    return colorMap;
  }
}
