import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_style.dart';
import '../e_menu/e_menu_controller.dart';
import '../e_menu/e_menu_page.dart';
import '../e_menu/widgets/product_widget.dart';
import 'search_menu_controller.dart';

class SearchMenuPage extends StatelessWidget {
  final SearchMenuController searchController = Get.find();
  final EMenuController menuController = Get.find();
  final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '');
  final FocusNode _focusNode = FocusNode();

  SearchMenuPage({super.key});
  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    searchController.searchController.text = searchController.searchQuery.value;
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Obx(() {
          if (menuController.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (searchController.shouldShowKeyboard.value) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _focusNode.requestFocus();
              searchController.shouldShowKeyboard.value = false;
            });
          }

          return Padding(
            padding: EdgeInsets.symmetric(horizontal: width * 0.015, vertical: width * 0.015),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        searchController.resetSearchState();
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.all(width * 0.015),
                        decoration: BoxDecoration(
                          color: AppColors.background,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.arrow_back_ios_new_rounded,
                          size: width * 0.045,
                          color: AppColors.text,
                        ),
                      ),
                    ),
                    SizedBox(width: width * 0.03),
                    Expanded(
                      child: TextFormField(
                        focusNode: _focusNode,
                        autofocus: true,
                        onChanged: (value) {
                          searchController.search(value);
                        },
                        controller: searchController.searchController,
                        decoration: InputDecoration(
                          hintText: "search_product".tr,
                          hintStyle: PrimaryFont.regular.copyWith(
                            color: AppColors.shadow,
                            fontSize: width * 0.03,
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(width * 0.01),
                            child: Icon(Icons.search, size: width * 0.05, color: AppColors.text),
                          ),
                          suffixIcon: searchController.searchQuery.isNotEmpty
                              ? IconButton(
                            icon: Icon(Icons.close, size: width * 0.05),
                            onPressed: () {
                              searchController.clearSearch();
                            },
                          )
                              : null,
                          contentPadding: EdgeInsets.symmetric(vertical: width * 0.015),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(width * 0.03),
                            borderSide: BorderSide(
                              color: AppColors.text.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(width * 0.03),
                            borderSide: BorderSide(
                              color: AppColors.text.withOpacity(0.5),
                              width: 2.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: width * 0.015),

                if (searchController.searchQuery.isEmpty &&
                    searchController.recentlyViewedProducts.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: width * 0.015, vertical: width * 0.015),
                        child: Text(
                          'recent_searches'.tr,
                          style: PrimaryFont.bold.copyWith(
                            fontSize: width * 0.04,
                            color: AppColors.text,
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: width * 0.015),
                        child: SizedBox(
                          height: width * 0.4,
                          child: GridView.builder(
                            scrollDirection: Axis.horizontal,
                            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 1,
                              mainAxisSpacing: width * 0.03,
                              childAspectRatio: 1.3,
                            ),
                            itemCount: searchController.recentlyViewedProducts.length,
                            itemBuilder: (context, index) {
                              final product = searchController.recentlyViewedProducts[index];
                              final itemName = product['ItemName'] ?? '';
                              final price = (product['Price'] as num?)?.toDouble() ?? 0.0;
                              final images = product['Images'] as List? ?? [];
                              final imageUrl = images.isNotEmpty ? images.first['MediaUrl'] ?? '' : '';

                              return productItem(
                                width,
                                currencyFormat,
                                itemName,
                                price,
                                imageUrl,
                                product,
                              );
                            },
                          ),
                        ),
                      ),
                      SizedBox(height: width * 0.03),
                    ],
                  ),

                if (searchController.searchQuery.isEmpty)
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.015),
                    child: Text(
                      'all_products'.tr,
                      style: PrimaryFont.bold.copyWith(
                        fontSize: width * 0.04,
                        color: AppColors.text,
                      ),
                    ),
                  ),
                if (searchController.searchQuery.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: width * 0.02),
                    child: Text(
                      '${'results_for'.tr} "${searchController.searchQuery.value}"',
                      style: PrimaryFont.medium.copyWith(
                        fontSize: width * 0.035,
                        color: AppColors.text,
                      ),
                    ),
                  ),

                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.015, vertical: width * 0.015),
                    child: GridView.builder(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        mainAxisSpacing: width * 0.03,
                        crossAxisSpacing: width * 0.03,
                        childAspectRatio: 0.7,
                      ),
                      itemCount: searchController.searchQuery.isEmpty
                          ? menuController.allProducts.length
                          : searchController.searchResults.length,
                      itemBuilder: (context, index) {
                        final product = searchController.searchQuery.isEmpty
                            ? menuController.allProducts[index]
                            : searchController.searchResults[index];
                        final itemName = product['ItemName'] ?? '';
                        final price = (product['Price'] as num?)?.toDouble() ?? 0.0;
                        final images = product['Images'] as List? ?? [];
                        final imageUrl = images.isNotEmpty ? images.first['MediaUrl'] ?? '' : '';

                        return productItem(
                          width,
                          currencyFormat,
                          itemName,
                          price,
                          imageUrl,
                          product,
                          onTap: () => searchController.viewProductDetail(product),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
      bottomNavigationBar: buildBottomCartBar(width, currencyFormat),
    );
  }
}