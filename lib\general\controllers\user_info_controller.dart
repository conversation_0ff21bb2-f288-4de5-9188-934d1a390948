import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';

class UserInfoController extends GetxController {
  final DioClientSME dioClient = DioClientSME();

  RxBool isLoading = true.obs;
  RxBool isEditing = false.obs;

  RxMap<String, dynamic> orgInfo = <String, dynamic>{}.obs;
  RxMap<String, dynamic> branchInfo = <String, dynamic>{}.obs;
  RxMap<String, dynamic> userLoginInfo = <String, dynamic>{}.obs;

  Future<void> fetchUserInfo() async {
    try {
      isLoading.value = true;
      final response = await dioClient.get(SmeUrl.orgInfo);

      if (response.data['Success'] && response.data['Result'] != null) {
        orgInfo.value = response.data['Result']['OrgInfo'] ?? {};
        branchInfo.value = response.data['Result']['BranchInfo'] ?? {};
        userLoginInfo.value = response.data['Result']['UserLoginInfo'] ?? {};
      } else {
        AppFunction.showError(response.data['Message'] ?? 'Failed to load user info');
      }
    } catch (e) {
      AppFunction.showError('Error loading user information');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateUserInfo(Map<String, dynamic> data) async {
    try {
      isLoading.value = true;
      final response = await dioClient.post(
        SmeUrl.orgUpdate,
        data: data,
      );
 
      if (response.data['Success']) {
        // Cập nhật lại dữ liệu từ response của server
        orgInfo.value = response.data['Result']['OrgInfo'] ?? {};
        branchInfo.value = response.data['Result']['BranchInfo'] ?? {};
        userLoginInfo.value = response.data['Result']['UserLoginInfo'] ?? {};

        isEditing.value = false;
        AppFunction.showSuccess('Cập nhật thông tin thành công');
      } else {
        AppFunction.showError(response.data['Message'] ?? 'Cập nhật thất bại');
      }
    } catch (e) {
      AppFunction.showError('Lỗi khi cập nhật thông tin: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onInit() {
    super.onInit();
    fetchUserInfo();
  }
}