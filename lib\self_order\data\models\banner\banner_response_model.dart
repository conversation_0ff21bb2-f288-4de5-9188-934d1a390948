class BannerListResponseModel {
  BannerListResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.result,
    required this.errorDetail,
  });

  final bool? success;
  final int? code;
  final String? message;
  final List<BannerResponseModel> result;
  final dynamic errorDetail;

  factory BannerListResponseModel.fromJson(Map<String, dynamic> json){
    return BannerListResponseModel(
      success: json["Success"],
      code: json["Code"],
      message: json["Message"],
      result: json["Result"] == null ? [] : List<BannerResponseModel>.from(json["Result"]!.map((x) => BannerResponseModel.fromJson(x))),
      errorDetail: json["ErrorDetail"],
    );
  }

}

class BannerResponseModel {
  BannerResponseModel({
    required this.id,
    required this.fileUrl,
    required this.contentId,
    required this.isActive,
    required this.lastUpdatedAt,
    required this.branchId,
  });

  final int? id;
  final String? fileUrl;
  final dynamic contentId;
  final bool? isActive;
  final DateTime? lastUpdatedAt;
  final int? branchId;

  factory BannerResponseModel.fromJson(Map<String, dynamic> json){
    return BannerResponseModel(
      id: json["Id"],
      fileUrl: json["FileUrl"],
      contentId: json["ContentId"],
      isActive: json["IsActive"],
      lastUpdatedAt: DateTime.tryParse(json["LastUpdatedAt"] ?? ""),
      branchId: json["BranchId"],
    );
  }

}
