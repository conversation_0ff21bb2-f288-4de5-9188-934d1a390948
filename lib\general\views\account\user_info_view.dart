import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/validators/user_info_view_validator.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/user_info_controller.dart';

class UserInfoView extends StatefulWidget {
  const UserInfoView({super.key});

  @override
  State<UserInfoView> createState() => _UserInfoViewState();
}

class _UserInfoViewState extends State<UserInfoView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final UserInfoController _controller = Get.put(UserInfoController());
  int? _selectedGender;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
  final GlobalKey<FormState> _formKey3 = GlobalKey<FormState>();

  final Map<String, TextEditingController> _controllers = {
    // User Info
    'ObjectNo': TextEditingController(),
    'ObjectName': TextEditingController(),
    'ObjectPhone': TextEditingController(),
    'Email': TextEditingController(),
    'ObjectAddress': TextEditingController(),
    'GroupName': TextEditingController(),
    'CountryName': TextEditingController(),
    'ProvinceName': TextEditingController(),
    'DistrictName': TextEditingController(),

    // Org Info
    'OrgName': TextEditingController(),
    'OrgCode': TextEditingController(),
    'TaxCode': TextEditingController(),
    'OrgEmail': TextEditingController(),
    'PhoneNumber': TextEditingController(),
    'Address': TextEditingController(),
    'OrgCountryName': TextEditingController(),
    'OrgProvinceName': TextEditingController(),
    'OrgDistrictName': TextEditingController(),

    // Branch Info
    'BranchName': TextEditingController(),
    'BranchCode': TextEditingController(),
    'BranchEmail': TextEditingController(),
    'BranchPhone': TextEditingController(),
    'BranchAddress': TextEditingController(),
    'BranchCountryName': TextEditingController(),
    'BranchProvinceName': TextEditingController(),
    'BranchDistrictName': TextEditingController(),
  };

  @override
  void initState() {
    super.initState();
    _tabController =
        TabController(length: UserInfo.orgType == 'DN' ? 3 : 2, vsync: this);
    _setupControllers();
  }

  void _setupControllers() {
    ever(_controller.userLoginInfo, (user) {
      _controllers['ObjectNo']!.text = user['ObjectNo'] ?? '';
      _controllers['ObjectName']!.text = user['ObjectName'] ?? '';
      _controllers['ObjectPhone']!.text = user['ObjectPhone'] ?? '';
      _controllers['Email']!.text = user['Email'] ?? '';
      _controllers['ObjectAddress']!.text = user['ObjectAddress'] ?? '';
      _controllers['GroupName']!.text = user['GroupName'] ?? '';
      _selectedGender = user['GenderId'];
      _controllers['CountryName']!.text = user['CountryName'] ?? '';
      _controllers['ProvinceName']!.text = user['ProvinceName'] ?? '';
      _controllers['DistrictName']!.text = user['DistrictName'] ?? '';
    });

    ever(_controller.orgInfo, (org) {
      _controllers['OrgName']!.text = org['OrgName'] ?? '';
      _controllers['OrgCode']!.text = org['OrgCode'] ?? '';
      _controllers['TaxCode']!.text = org['TaxCode'] ?? '';
      _controllers['OrgEmail']!.text = org['Email'] ?? '';
      _controllers['PhoneNumber']!.text = org['PhoneNumber'] ?? '';
      _controllers['Address']!.text = org['Address'] ?? '';
      _controllers['OrgCountryName']!.text = org['CountryName'] ?? '';
      _controllers['OrgProvinceName']!.text = org['ProvinceName'] ?? '';
      _controllers['OrgDistrictName']!.text = org['DistrictName'] ?? '';
    });

    ever(_controller.branchInfo, (branch) {
      _controllers['BranchName']!.text = branch['BranchName'] ?? '';
      _controllers['BranchCode']!.text = branch['BranchCode'] ?? '';
      _controllers['BranchEmail']!.text = branch['BranchEmail'] ?? '';
      _controllers['BranchPhone']!.text = branch['BranchPhone'] ?? '';
      _controllers['BranchAddress']!.text = branch['BranchAddress'] ?? '';
      _controllers['BranchCountryName']!.text =
          branch['BranchCountryName'] ?? '';
      _controllers['BranchProvinceName']!.text =
          branch['BranchProvinceName'] ?? '';
      _controllers['BranchDistrictName']!.text =
          branch['BranchDistrictName'] ?? '';
    });
  }

  Future<void> _updateUserInfo() async {
    if (!_formKey.currentState!.validate()) return;

    final Map<String, dynamic> requestBody = {
      "BranchId": _controller.branchInfo['BranchId'] ?? 0,
      "OrgName": _controllers['OrgName']!.text,
      "OrgTaxID": _controllers['TaxCode']!.text,
      "OrgEmail": _controllers['OrgEmail']!.text,
      "OrgPhone": _controllers['PhoneNumber']!.text,
      "OrgDescription": "",
      "OrgCountryId": _controller.orgInfo['CountryId'] ?? 0,
      "OrgProvinceId": _controller.orgInfo['ProvinceId'] ?? 0,
      "OrgDistrictId": _controller.orgInfo['DistrictId'] ?? 0,
      "OrgPlaceId": _controller.orgInfo['PlaceId'] ?? 0,
      "OrgAddress": _controllers['Address']!.text,
      "BranchName": _controllers['BranchName']!.text,
      "BranchEmail": _controllers['BranchEmail']!.text,
      "BranchPhone": _controllers['BranchPhone']!.text,
      "BranchDescription": "",
      "BranchCountryId": _controller.branchInfo['BranchCountryId'] ?? 0,
      "BranchProvinceId": _controller.branchInfo['BranchProvinceId'] ?? 0,
      "BranchDistrictId": _controller.branchInfo['BranchDistrictId'] ?? 0,
      "BranchAddress": _controllers['BranchAddress']!.text,
      "FullName": _controllers['ObjectName']!.text,
      "ObjectEmail": _controllers['Email']!.text,
      "ObjectPhone": _controllers['ObjectPhone']?.text ?? "",
      "ObjectDescription": "",
      "ObjectCountryId": _controller.userLoginInfo['CountryId'] ?? 0,
      "ObjectProvinceId": _controller.userLoginInfo['ProvinceId'] ?? 0,
      "ObjectDistrictId": _controller.userLoginInfo['DistrictId'] ?? 0,
      "ObjectAddress": _controllers['ObjectAddress']!.text,
      "ObjGender": _selectedGender,
      //"GroupName": _controllers['GroupName']!.text,
    };

    await _controller.updateUserInfo(requestBody);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _controllers.forEach((key, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const CustomTitleAppBar(title: 'Thông tin tài khoản'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: const CustomBackButton(),
        actions: [
          Obx(() => IconButton(
                icon:
                    Icon(_controller.isEditing.value ? Icons.save : Icons.edit),
                color: AppColors.background,
                onPressed: () {
                  if (_controller.isEditing.value) {
                    _updateUserInfo();
                  } else {
                    _controller.isEditing.value = true;
                  }
                },
              )),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'Cá nhân'),
            if (UserInfo.orgType == 'DN') ...[
              Tab(text: 'Thương hiệu'),
            ],
            Tab(text: 'Chi nhánh'),
          ],
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          indicatorColor: Colors.white,
        ),
      ),
      body: Obx(() => _controller.isLoading.value
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildUserInfoTab(),
                if (UserInfo.orgType == 'DN') ...[
                  _buildOrgInfoTab(),
                ],
                _buildBranchInfoTab(),
              ],
            )),
      bottomNavigationBar: Obx(() {
        if (!_controller.isEditing.value) return const SizedBox.shrink();
        return Container(
          padding: const EdgeInsets.all(16),
          child: CustomButton(
            text: 'Lưu thay đổi',
            onTap: _updateUserInfo,
            color: AppColors.primary,
          ),
        );
      }),
    );
  }

  Widget _buildUserInfoTab() {
    return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // CustomTextField(
              //   controller: _controllers['ObjectNo']!,
              //   label: "Mã tài khoản",
              //   readOnly: true,
              // ),
              // const SizedBox(height: 8),
              CustomTextField(
                controller: _controllers['ObjectName']!,
                label: "Tên tài khoản",
                readOnly: !_controller.isEditing.value,
                validator: UserInfoViewValidator.validatorObjectName,
              ),
              const SizedBox(height: 8),
              CustomTextField(
                controller: _controllers['ObjectPhone']!,
                label: "Số điện thoại",
                readOnly: !_controller.isEditing.value,
              ),
              const SizedBox(height: 8),
              CustomTextField(
                controller: _controllers['Email']!,
                label: "Email",
                readOnly: !_controller.isEditing.value,
              ),
              const SizedBox(height: 8),
              CustomTextField(
                controller: _controllers['ObjectAddress']!,
                label: "Địa chỉ",
                readOnly: !_controller.isEditing.value,
              ),
              const SizedBox(height: 8),
              // CustomTextField(
              //   controller: _controllers['GroupName']!,
              //   label: "Nhóm người dùng",
              //   readOnly: true,
              // ),
              // const SizedBox(height: 8),
              _buildGenderDropdown(),
            ],
          ),
        ));
  }

  Widget _buildOrgInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey2,
        child: Column(
          children: [
            // CustomTextField(
            //   controller: _controllers['OrgCode']!,
            //   label: "Mã thương hiệu",
            //   readOnly: true,
            // ),
            // const SizedBox(height: 8),
            CustomTextField(
              controller: _controllers['OrgName']!,
              label: "Tên thương hiệu",
              readOnly: !_controller.isEditing.value,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập tên thương hiệu';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),
            CustomTextField(
              controller: _controllers['TaxCode']!,
              label: "Mã số thuế",
              readOnly: !_controller.isEditing.value,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập mã số thuế';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),
            CustomTextField(
              controller: _controllers['OrgEmail']!,
              label: "Email",
              readOnly: !_controller.isEditing.value,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập email';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),
            CustomTextField(
              controller: _controllers['PhoneNumber']!,
              label: "Số điện thoại",
              readOnly: !_controller.isEditing.value,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập số điện thoại';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),
            CustomTextField(
                controller: _controllers['Address']!,
                label: "Địa chỉ",
                readOnly: !_controller.isEditing.value,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập địa chỉ';
                  }
                  return null;
                }),
            const SizedBox(height: 8),
            // if (UserInfo.licenseKey.isNotEmpty) ...[
            //   _buildReadOnlyField("Mã License", UserInfo.licenseKey),
            //   const SizedBox(height: 8),
            //   _buildReadOnlyField("Còn lại", "${UserInfo.daysLeft} ngày"),
            //   const SizedBox(height: 8),
            //   _buildReadOnlyField("Ngày bắt đầu", _formatDate(UserInfo.startDate)),
            //   const SizedBox(height: 8),
            //   _buildReadOnlyField("Ngày hết hạn", _formatDate(UserInfo.expiryDate)),
            //
            // ],
          ],
        ),
      ),
    );
  }

  Widget _buildBranchInfoTab() {
    return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey3,
          child: Column(
            children: [
              // CustomTextField(
              //   controller: _controllers['BranchCode']!,
              //   label: "Mã chi nhánh",
              //   readOnly: true,
              // ),
              // const SizedBox(height: 8),
              CustomTextField(
                  controller: _controllers['BranchName']!,
                  label: "Tên chi nhánh",
                  readOnly: !_controller.isEditing.value,
                  validator: UserInfoViewValidator.validatorEmail),
              const SizedBox(height: 8),
              CustomTextField(
                controller: _controllers['BranchEmail']!,
                label: "Email",
                readOnly: !_controller.isEditing.value,
                validator: UserInfoViewValidator.validatorEmail,
              ),
              const SizedBox(height: 8),
              CustomTextField(
                controller: _controllers['BranchPhone']!,
                label: "Số điện thoại",
                readOnly: !_controller.isEditing.value,
                validator: UserInfoViewValidator.validatorPhone,
              ),
              const SizedBox(height: 8),
              CustomTextField(
                controller: _controllers['BranchAddress']!,
                label: "Địa chỉ",
                readOnly: !_controller.isEditing.value,
                validator: UserInfoViewValidator.validatorAddress,
              ),
              const SizedBox(height: 8),
            ],
          ),
        ));
  }

  Widget _buildGenderDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Giới tính",
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<int>(
          value: _selectedGender,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          items: const [
            DropdownMenuItem(
              value: 1,
              child: Text("Nữ"),
            ),
            DropdownMenuItem(
              value: 0,
              child: Text("Nam"),
            ),
          ],
          onChanged: _controller.isEditing.value
              ? (value) {
                  setState(() {
                    _selectedGender = value;
                  });
                }
              : null,
          isExpanded: true,
        ),
      ],
    );
  }
}
