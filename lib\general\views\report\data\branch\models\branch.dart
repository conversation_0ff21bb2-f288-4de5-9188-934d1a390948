class BranchModel {
  final int? branchId;
  final String? branchName;
  final String? branchCode;
  final int? branchParentId;

  BranchModel({
    required this.branchId,
    required this.branchName,
    required this.branchCode,
    required this.branchParentId,
  });

  factory BranchModel.fromJson(Map<String, dynamic> json) {
    return BranchModel(
      branchId: json["BranchId"],
      branchName: json["BranchName"],
      branchCode: json["BranchCode"], // Đ<PERSON>i từ BranchNo sang BranchCode để phù hợp JSON
      branchParentId: json["ParentId"], // Thêm parentId
    );
  }
}