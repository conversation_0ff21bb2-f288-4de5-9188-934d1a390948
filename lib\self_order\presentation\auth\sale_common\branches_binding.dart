import 'package:get/get.dart';
import '../../../../core/network/dio_client.dart';
import '../../../data/datasources/sale_common/branches_remote_data_source.dart';
import '../../../data/repositories/sale_common/branches_repository_impl.dart';
import '../../../domain/repositories/sale_common/branches_repository.dart';
import '../../../domain/usecases/sale_common/branches_usecase.dart';
import 'branches_controller.dart';

class BranchesBinding extends Bindings {
  @override
  void dependencies() {
    final dioClient = Get.find<DioClient>();

    Get.lazyPut<BranchesRemoteDataSource>(
          () => BranchesRemoteDataSourceImpl(dioClient: dioClient),
    );

    Get.lazyPut<BranchesRepository>(
          () => BranchesRepositoryImpl(remoteDataSource: Get.find()),
    );

    Get.lazyPut<BranchesUseCase>(
          () => BranchesUseCase(repository: Get.find()),
    );

    Get.lazyPut<BranchesController>(
          () => BranchesController(branchesUseCase: Get.find()),
    );
  }
}
