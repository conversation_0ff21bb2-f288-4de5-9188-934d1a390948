import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import '../../../../../../core/network/dio_client.dart';
import '../../../core/constants/api_url.dart';
import '../../../core/network/dio_client.dart';
import '../../../service_locator.dart';
import '../models/report.dart';

// DashBoard
abstract class OverviewRevenueService {
  Future<Either<String, OverviewRevenueModel>> getOverviewRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class OverviewRevenueApiServiceImpl extends OverviewRevenueService {
  @override
  Future<Either<String, OverviewRevenueModel>> getOverviewRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.overviewRevenue,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      return Right(OverviewRevenueModel.fromJson(response.data["Result"]));
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }
}

abstract class TopBottomBranchesRevenueService {
  Future<Either<String, TopBottomBranchesRevenueModel>> getTopBottomBranchesRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TopBottomBranchesRevenueApiServiceImpl extends TopBottomBranchesRevenueService {
  @override
  Future<Either<String, TopBottomBranchesRevenueModel>> getTopBottomBranchesRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.topBottomBranchesRevenue,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      return Right(TopBottomBranchesRevenueModel.fromJson(response.data["Result"]));
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }
}

abstract class Revenue7DaysService {
  Future<Either<String, List<Revenue7DaysModel>>> getRevenue7Days({
    required List<int> listBrandId,
  });
}
class Revenue7DaysApiServiceImpl extends Revenue7DaysService {
  @override
  Future<Either<String, List<Revenue7DaysModel>>> getRevenue7Days({
    required List<int> listBrandId,
  }) async {
    try {
      var response = await sl<DioClientReport>().post(
        ApiUrl.revenue7days,
        data: listBrandId,
      );

      // ✅ Kiểm tra nếu `response.data["Result"]` là null hoặc không phải danh sách
      if (response.data["Result"] == null || response.data["Result"] is! List) {
        return Left("Lỗi: Dữ liệu không hợp lệ từ API");
      }

      List<dynamic> jsonData = response.data["Result"];
      List<Revenue7DaysModel> reports = jsonData.map((e) => Revenue7DaysModel.fromJson(e)).toList();

      return Right(reports);
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }
}

abstract class TopBranchesRevenueByDateService {
  Future<Either<String, List<TopBranchesRevenueByDateModel>>> getTopBranchesRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TopBranchesRevenueByDateApiServiceImpl extends TopBranchesRevenueByDateService {
  @override
  Future<Either<String, List<TopBranchesRevenueByDateModel>>> getTopBranchesRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.topBranchesRevenueByDate,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      // Kiểm tra nếu "Result" không tồn tại hoặc không có "TopItemsRevenueByDate"
      if (response.data == null ||
          response.data["Result"] == null ||
          response.data["Result"]["TopBranchesRevenueByDate"] == null) {
        return Left("No data available");
      }

      // Lấy danh sách từ API
      final List<dynamic> rawData = response.data["Result"]["TopBranchesRevenueByDate"];

      // Chuyển đổi danh sách JSON thành danh sách đối tượng model
      final List<TopBranchesRevenueByDateModel> dataList =
      rawData.map((e) => TopBranchesRevenueByDateModel.fromJson(e)).toList();

      return Right(dataList);
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }

}

// By Item
abstract class TopAndBottomItemsRevenueService {
  Future<Either<String, TopAndBottomItemsRevenueModel>> getTopAndBottomItemsRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TopAndBottomItemsRevenueApiServiceImpl extends TopAndBottomItemsRevenueService {
  @override
  Future<Either<String, TopAndBottomItemsRevenueModel>> getTopAndBottomItemsRevenues({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.topAndBottomItemsRevenue,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      return Right(TopAndBottomItemsRevenueModel.fromJson(response.data["Result"]));
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }
}

abstract class TopItemsRevenueByDateService {
  Future<Either<String, List<TopItemsRevenueByDateModel>>> getTopItemsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TopItemsRevenueByDateApiServiceImpl extends TopItemsRevenueByDateService {
  @override
  Future<Either<String, List<TopItemsRevenueByDateModel>>> getTopItemsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.topItemsRevenueByDate,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      // Kiểm tra nếu "Result" không tồn tại hoặc không có "TopItemsRevenueByDate"
      if (response.data == null ||
          response.data["Result"] == null ||
          response.data["Result"]["TopItemsRevenueByDate"] == null) {
        return Left("No data available");
      }

      // Lấy danh sách từ API
      final List<dynamic> rawData = response.data["Result"]["TopItemsRevenueByDate"];

      // Chuyển đổi danh sách JSON thành danh sách đối tượng model
      final List<TopItemsRevenueByDateModel> dataList =
      rawData.map((e) => TopItemsRevenueByDateModel.fromJson(e)).toList();

      return Right(dataList);
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }

}

// By Category
abstract class TopAndBottomCategoriesByAmountService {
  Future<Either<String, TopAndBottomCategoriesByAmountModel>> getTopAndBottomCategoriesByAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TopAndBottomCategoriesByAmountApiServiceImpl extends TopAndBottomCategoriesByAmountService {
  @override
  Future<Either<String, TopAndBottomCategoriesByAmountModel>> getTopAndBottomCategoriesByAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.topAndBottomCategoriesByAmount,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      return Right(TopAndBottomCategoriesByAmountModel.fromJson(response.data["Result"]));
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }
}

abstract class TopCatsRevenueByDateService {
  Future<Either<String, List<TopCatsRevenueByDateModel>>> getTopCatsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TopCatsRevenueByDateApiServiceImpl extends TopCatsRevenueByDateService {
  @override
  Future<Either<String, List<TopCatsRevenueByDateModel>>> getTopCatsRevenueByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.topCatsRevenueByDate,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      if (response.data == null ||
          response.data["Result"] == null ||
          response.data["Result"]["TopItemGroupsRevenueByDate"] == null) {
        return Left("No data available");
      }

      final List<dynamic> rawData = response.data["Result"]["TopItemGroupsRevenueByDate"];

      // Chuyển đổi danh sách JSON thành danh sách đối tượng model
      final List<TopCatsRevenueByDateModel> dataList =
      rawData.map((e) => TopCatsRevenueByDateModel.fromJson(e)).toList();

      return Right(dataList);
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }

}

// By Payment
abstract class TotalPaymentAmountService {
  Future<Either<String, List<TotalPaymentAmountModel>>> getTotalPaymentAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TotalPaymentAmountApiServiceImpl extends TotalPaymentAmountService {
  @override
  Future<Either<String, List<TotalPaymentAmountModel>>> getTotalPaymentAmounts({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.totalPaymentAmount,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      // Sửa lại phần parse dữ liệu
      if (response.data["Result"] == null ||
          response.data["Result"]["TotalPaymentRevenue"] == null) {
        return Left("No data available");
      }

      final List<dynamic> rawData = response.data["Result"]["TotalPaymentRevenue"];
      List<TotalPaymentAmountModel> models = rawData
          .map((item) => TotalPaymentAmountModel.fromJson(item))
          .toList();

      return Right(models);
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi kết nối API");
    }
  }
}

abstract class TotalPaymentAmountByDateService {
  Future<Either<String, List<TotalPaymentAmountByDateModel>>> getTotalPaymentAmountByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  });
}
class TotalPaymentAmountByDateApiServiceImpl extends TotalPaymentAmountByDateService {
  @override
  Future<Either<String, List<TotalPaymentAmountByDateModel>>> getTotalPaymentAmountByDates({
    required List<int> listBrandId,
    required String dateFrom,
    required String dateTo,
  }) async {
    try {
      var response = await sl<DioClientSME>().post(
        ApiUrl.totalPaymentAmountByDate,
        data: {
          "listBrandId": listBrandId,
          "DateFrom": dateFrom,
          "DateTo": dateTo,
        },
      );

      // Sửa lại phần parse dữ liệu theo đúng cấu trúc API
      if (response.data == null ||
          response.data["Result"] == null ||
          response.data["Result"]["TotalPaymentAmountByDate"] == null) {
        return Left("No data available");
      }

      final List<dynamic> rawData = response.data["Result"]["TotalPaymentAmountByDate"];

      List<TotalPaymentAmountByDateModel> dataList = rawData.map((e) {
        return TotalPaymentAmountByDateModel(
          paymentDate: _parseDate(e["RevenueDate"]),
          payments: (e["Revenues"] as Map<String, dynamic>).map(
                (key, value) => MapEntry(key, (value as num).toDouble()),
          ),
        );
      }).toList();


      return Right(dataList);
    } on DioException catch (e) {
      return Left(e.response?.data?['message'] ?? "Lỗi khi tải dữ liệu");
    }
  }

  static DateTime? _parseDate(dynamic dateStr) {
    if (dateStr == null) return null;
    try {
      return DateFormat("M/d/yyyy hh:mm:ss a").parse(dateStr);
    } catch (e) {
      print("⚠️ Lỗi parse ngày: $dateStr");
      return null;
    }
  }
}
