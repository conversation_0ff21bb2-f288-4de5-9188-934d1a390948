import 'package:get/get.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';

class PaymentController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();

  //function
  getList(from, to, type) async {
    List list = [];
    dynamic body = {
      "RealTransDateFrom": from,
      "RealTransDateTo": to,
      "DebitOrCredit": type
    };
    try {
      final response = await dioClient.post(
          SmeUrl.paymentList,
          data: body
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {

    }
    return list;
  }
}