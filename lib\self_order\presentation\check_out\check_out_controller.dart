// checkout_controller.dart
import 'dart:async';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/api_url.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/controllers/bluetooth_printer_controller.dart';
import 'package:gls_self_order/core/controllers/usb_printer_controller.dart';
import 'package:gls_self_order/core/network/dio_client.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:image/image.dart' as img;
import '../../domain/entities/order/order_reponse_entity.dart';
import '../../domain/entities/order/order_request_entity.dart';
import '../../domain/usecases/order/generate_zalo_usecase.dart';
import '../../domain/usecases/order/order_usecase.dart';
import '../e_menu/cart/cart_controller.dart';
import '../e_menu/e_menu_controller.dart';

class CheckoutController extends GetxController {
  final CreateOrderUseCase createOrderUseCase;
  final GenerateZALOQRUseCase generateZALOQRUseCase;
  final BluetoothPrinterController bluetoothPrinterController = Get.find();
  final UsbPrinterController usbPrinterController = Get.find();
  late IO.Socket socket;

  var branchName = ''.obs;
  var branchId = 0.obs;
  var showAllItems = false.obs;
  var paymentMethod = 'ZALOPAY'.obs;
  var tempPaymentMethod = 'ZALOPAY'.obs;
  var isLoading = false.obs;
  var orderSuccess = false.obs;

  final RxInt paymentTimeout = 10.obs;
  final RxString qrCodeData = ''.obs;
  final RxString orderCode = ''.obs;
  final RxDouble paymentAmount = 0.0.obs;
  final RxBool paymentSuccess = false.obs;
  final RxString orderNote = ''.obs;
  final Rx<OrderResponseEntity?> orderResponse = Rx<OrderResponseEntity?>(null);

  void updateOrderNote(String note) {
    orderNote.value = note;
  }

  Timer? timer;

  CheckoutController({
    required this.createOrderUseCase,
    required this.generateZALOQRUseCase,
  });

  Future<void> loadSavedBranchName() async {
    final prefs = await SharedPreferences.getInstance();
    branchName.value = prefs.getString('branchName') ?? 'Chưa chọn chi nhánh';
    branchId.value = prefs.getInt('branchId') ?? 0;
  }

  @override
  void onInit() {
    super.onInit();
    connectSocket();
    loadSavedBranchName();
  }

  @override
  void onClose() {
    socket.dispose();
    timer?.cancel();
    super.onClose();
  }

  void connectSocket() {
    socket = IO.io(ApiUrl.socketUrl, <String, dynamic>{
      'transports': ['websocket'],
      'autoConnect': false,
    });

    socket.connect();

    socket.onConnect((_) {
      print('Socket connected');
    });

    socket.on('PaymentSuccess', (data) async {
      String code = data['orderCode'] ?? '';
      if (code == orderCode.value) {
        dynamic decoded = await getImageBill(orderCode, UserInfo.branchId);
        if (decoded != null) {
          if (GlobalVar.typePrinter == 'bluetooth') {
            bluetoothPrinterController.printBill(decoded);
          }
          else if (GlobalVar.typePrinter == 'usb') {
            usbPrinterController.printBill(decoded);
          }
          else {
            AppFunction.showError('Máy in chưa được kết nối');
          }
        }
      }
    });

    socket.onDisconnect((_) {
      print('Socket disconnected');
    });
  }

  startCountdown() {
    timer?.cancel();

    paymentTimeout.value = 180;

    timer = Timer.periodic(Duration(seconds: 1), (_timer) {
      if (paymentTimeout.value > 0) {
        paymentTimeout.value--;
      } else {
        _timer.cancel();
      }
    });
  }

  stopCountdown() {
    timer?.cancel();
  }

  void setTempPaymentMethod(String method) {
    tempPaymentMethod.value = method;
  }

  void confirmPaymentMethod() {
    paymentMethod.value = tempPaymentMethod.value;
  }

  void toggleExpandCollapse() {
    showAllItems.value = !showAllItems.value;
  }

  Future<void> createOrder() async {
    try {
      isLoading.value = true;
      orderResponse.value = null;

      final cartController = Get.find<CartController>();
      final prefs = await SharedPreferences.getInstance();
      final orderType = prefs.getString('order_type') ?? 'DineIn';
      // Lấy thông tin menu từ EMenuController
      final eMenuController = Get.find<EMenuController>();
      final menuEntity = eMenuController.menuEntity.value;

      // Lấy các giá trị từ menuEntity
      final menuId = menuEntity?.fullJson['MenuAutoId'] ?? 0;
      final branchId = menuEntity?.fullJson['BranchId'] ?? 0;
      final timeSaleMenuId = menuEntity?.fullJson['TimeSaleMenus']?[0]['TimeSaleAutoId'] ?? 0;


      // Chuyển đổi từ giỏ hàng sang OrderRequestEntity
      final orderRequest = OrderRequestEntity(
        menuId: menuId,
        timeSaleMenuId: timeSaleMenuId,
        branchId: branchId,
        customerPhone: "0901234567",
        customerNo: "CUST001",
        customerName: "Nguyễn Văn A",
        orderNote: orderNote.value,
        orderType: orderType,
        orderDetails: cartController.cartItems.map((item) {
          final product = item['product'];

          final defaultItems = product['DefaultItems'] as List<dynamic>? ?? [];
          final selectedChoices = product['SelectedChoices'] as Map<String, dynamic>? ?? {};

          // Convert DefaultItems
          final defaultChildren = defaultItems.map<ChildrenEntity>((defaultItem) {
            return ChildrenEntity(
              itemNo: defaultItem['ItemNo'] ?? '',
              itemName: defaultItem['ItemName'] ?? '',
              itemQuantity: defaultItem['Qty'] ?? 1,
              itemPrice: (defaultItem['Price'] as num?)?.toDouble() ?? 0.0,
              discountAmount: 0,
            );
          }).toList();

          final choiceChildren = <ChildrenEntity>[];
          selectedChoices.forEach((choiceId, items) {
            if (items is List) {
              for (var choiceItem in items) {
                choiceChildren.add(ChildrenEntity(
                  itemNo: choiceItem['ItemNo'] ?? '',
                  itemName: choiceItem['ItemName'] ?? '',
                  itemQuantity: 1,
                  itemPrice: (choiceItem['Price'] as num?)?.toDouble() ?? 0.0,
                  discountAmount: 0,
                ));
              }
            }
          });

          return OrderDetailEntity(
            itemNo: product['ItemNo'] ?? '',
            itemName: product['ItemName'] ?? '',
            itemQuantity: item['quantity'] ?? 1,
            itemPrice: (product['Price'] as num?)?.toDouble() ?? 0.0,
            discountAmount: 0,
            itemNote: item['note'] ?? '',
            children: [...defaultChildren, ...choiceChildren],
          );
        }).toList(),

      );

      final response = await createOrderUseCase(orderRequest);
      orderResponse.value = response;

      if (response.success) {
        orderSuccess.value = true;
        orderCode.value = response.result?.orderResponse.orderCode ?? '';
        paymentAmount.value = response.result?.orderResponse.orderTotalView ?? cartController.totalPrice.value;
      } else {
        Get.snackbar('Lỗi', response.message);
      }
    } catch (e) {
      Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi tạo đơn hàng: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }
  // Thêm hàm generateQRCode
  Future<void> generateQRCode() async {
    if (orderCode.isEmpty) {
      Get.snackbar('Lỗi', 'Chưa có mã đơn hàng');
      return;
    }

    try {
      final response = await generateZALOQRUseCase(orderCode.value);

      if (response.result != null && response.result!.qrCode.isNotEmpty) {
        qrCodeData.value = response.result!.qrCode;
        print('QR Code Data: ${qrCodeData.value}');
      } else {
        qrCodeData.value = '';
        Get.snackbar('Lỗi', 'Không thể tạo mã QR');
      }
    } catch (e) {
      qrCodeData.value = '';
      Get.snackbar('Lỗi', 'Lỗi khi tạo mã QR: ${e.toString()}');
    }
  }

  void printOrderSummary() {
    final cartController = Get.find<CartController>();

    print('--- THÔNG TIN ĐƠN HÀNG ---');
    print('Mã đơn hàng: ${orderCode.value}');
    print('Số điện thoại: 0901234567');
    print('Khách hàng: Nguyễn Văn A');
    print('Chi nhánh: ${branchName.value}');
    print('Tổng tiền: ${paymentAmount.value.toStringAsFixed(0)}đ');
    print('Phương thức thanh toán: ${paymentMethod.value}');
    print('Danh sách sản phẩm:');

    for (var item in cartController.cartItems) {
      final product = item['product'];
      final quantity = item['quantity'] ?? 1;
      final price = (product['Price'] as num?)?.toDouble() ?? 0.0;
      final total = quantity * price;
      final note = item['note']?.toString().trim() ?? '';

      print(' - ${product['ItemName']} x$quantity = ${total.toStringAsFixed(0)}đ');

      // In ghi chú nếu có
      if (note.isNotEmpty) {
        print('    Ghi chú: $note');
      }

      // In default items
      final defaultItems = product['DefaultItems'] as List<dynamic>? ?? [];
      for (var defaultItem in defaultItems) {
        print('    + ${defaultItem['ItemName']} (${defaultItem['Price']}đ)');
      }

      // In selected choices
      final selectedChoices = product['SelectedChoices'] as Map<String, dynamic>? ?? {};
      selectedChoices.forEach((choiceId, items) {
        if (items is List) {
          for (var choiceItem in items) {
            print('    * ${choiceItem['ItemName']} (${choiceItem['Price']}đ)');
          }
        }
      });
    }
    print('--------------------------');
  }

  void manualCheckTransaction() async{
    //call api
    //if true set
    dynamic decoded = await getImageBill(orderCode, UserInfo.branchId);
    if (decoded != null) {
      if (GlobalVar.typePrinter == 'bluetooth') {
        bluetoothPrinterController.printBill(decoded);
      }
      else if (GlobalVar.typePrinter == 'usb') {
        usbPrinterController.printBill(decoded);
      }
      else {
        AppFunction.showError('Máy in chưa được kết nối');
      }
    }
    //else set false
  }

  Future<void> refreshQRCode() async {
    try {
      isLoading.value = true;

      await createOrder();

      if (orderSuccess.value) {
        await generateQRCode();
        startCountdown();
      }
    } catch (e) {
      Get.snackbar('Lỗi', 'Không thể tạo lại mã QR: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  // Demo tích điểm thành viên
  // Thêm các observable vào controller
  final memberPhone = ''.obs;
  final memberPoints = 0.obs;

  void updateMemberPhone(String phone) {
    memberPhone.value = phone;
    // Gọi API kiểm tra điểm tích lũy ở đây
    // Ví dụ: fetchMemberPoints(phone);
  }

  // Hàm giả lập gọi API
  Future<void> fetchMemberPoints(String phone) async {
    try {
      // Gọi API để lấy thông tin điểm
      // final response = await memberRepository.getPoints(phone);
      // memberPoints.value = response.points;

      // Tạm thời set giá trị mẫu
      memberPoints.value = 150; // Giá trị mẫu
    } catch (e) {
      memberPoints.value = 0;
    }
  }

  getImageBill(orderId, brandId) async {
    DioClientSME dioClientSME = DioClientSME();
    try {
      final response = await dioClientSME.post(
        SmeUrl.genBillUrl,
        data: {
          "orderId": orderId,
          "branchId": brandId,
        },
        options: Options(
          responseType: ResponseType.bytes,
          headers: {
            'Accept': 'image/png',
          },
        ),
      );
      Uint8List imageBytes = response.data!;
      final decoded = img.decodeImage(imageBytes);
      return decoded;
    } catch (e) {
      return null;
    }
  }

}
