import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/news_controller.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class NewsDetailView extends StatefulWidget {
  final int id;
  const NewsDetailView({super.key, required this.id});

  @override
  State<NewsDetailView> createState() => _NewsDetailViewState();
}

class _NewsDetailViewState extends State<NewsDetailView> {
  //variable
  dynamic detail;
  NewsController newsController = Get.find();
  WebViewController webViewController = WebViewController();
  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    dynamic item = await newsController.getNewsDetail(widget.id);
    if (item != null) {
      detail = item;
    }
    webViewController = WebViewController()
      ..enableZoom(true)
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) {
            launchUrl(Uri.parse(request.url));
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadHtmlString("""
      <!DOCTYPE html>
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              a {
               color: black
              }
              img {
                max-width: 100% !important;
                height: auto !important;
              }
            </style>
          </head>
          <body style="margin: 0; padding: 10px; color: #000000!important">
            <h1>${detail != null ? detail['Title'] : ''}</h1>
            ${detail != null ? detail['ContentBody'] : ''}
          </body>
        </html>
      """);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(
            title: detail != null ? (detail['Title'] ?? '') : ''),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: WebViewWidget(
        controller: webViewController
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageFinished: (url) {
                webViewController.runJavaScript('''
                    var meta = document.createElement('meta');
                    meta.name = 'viewport';
                    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                    document.getElementsByTagName('head')[0].appendChild(meta);
                  ''');
              },
            ),
          ),
      ),
    );
  }
}
