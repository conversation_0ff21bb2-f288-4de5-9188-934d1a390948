import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/views/warehouse/export_warehouse_create_or_update_view.dart';
import 'package:gls_self_order/general/views/warehouse/import_warehouse_create_or_update_view.dart';

class ExportWarehouseView extends StatefulWidget {
  const ExportWarehouseView({super.key});

  @override
  State<ExportWarehouseView> createState() => _ExportWarehouseViewState();
}

class _ExportWarehouseViewState extends State<ExportWarehouseView> {
  //variable
  TextEditingController searchController = TextEditingController();

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget renderFilter() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: searchController,
              showLabel: false,
              hint: 'Tìm kiếm số phiếu',
              space: false,
            ),
          ),
          InkWell(
            onTap: () {
              // filter(false);
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)
              ),
              child: Icon(Icons.search, color: Colors.white,),
            ),
          ),
          InkWell(
            onTap: () {
              // showFilter(context);
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)
              ),
              child: Icon(Icons.filter_alt_sharp, color: Colors.white,),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Xuất kho'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              Get.to(() => ExportWarehouseCreateOrUpdateView());
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.add, color: Colors.white, size: 35,),
            ),
          )
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            //for filter
            renderFilter(),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.05),
                            spreadRadius: 0,
                            blurRadius: 1,
                            offset: Offset(0, 3)
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(10),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            CustomText(text: 'Số phiếu: '),
                            CustomText(text: 'PX20250715201353', bold: true,),
                          ],
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Kho:'),
                                  CustomText(text: 'Kho Huỳnh Tấn Phát', bold: true, maxLines: 2,),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Khách hàng'),
                                  CustomText(text: 'Anh Dũng CT', bold: true, maxLines: 2,),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Số tiền:'),
                                  CustomText(text: '5.350.000 đ', bold: true,),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Ngày xuất'),
                                  CustomText(text: '20/07/2025 15:30', bold: true,),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Ghi chú:'),
                                  CustomText(text: 'Ủng hộ', bold: true, maxLines: 4,),
                                ],
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
