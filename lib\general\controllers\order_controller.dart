import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';

class OrderController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();
  int saleMenuId = 1;
  var texCodeError = "".obs;
  var isSearchingCompany = false.obs;

  //function
  getList(from, to, customerId, paymentMethod, status) async {
    List list = [];
    dynamic body = {
      "CreatedAt": null,
      "FromDate": from,
      "ToDate": to,
      "CustomerName": null,
      "CustomerPhone": null,
      "StatusCode": status != '' ? status : null,
      "PaymentMethod": paymentMethod != '' ? paymentMethod : null,
      "CustomerId": customerId != 0 ? customerId : null
    };
    try {
      final response = await dioClient.post(
        SmeUrl.orderList,
        data: body
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {

    }
    return list;
  }

  getListGroupCustomer(from, to, status) async {
    List list = [];
    dynamic body = {
      "FromDate": from,
      "ToDate": to,
      "StatusCode": status != '' ? status : null,
    };
    try {
      final response = await dioClient.post(
          SmeUrl.orderGroupByCustomerList,
          data: body
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {

    }
    return list;
  }

  getStatusList() async {
    List list = [];
    try {
      final response = await dioClient.get(
        SmeUrl.statusList,
      );
     
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {

    }
    return list;
  }

  getDetail(code) async {
    dynamic item;
    List details = [];
    try {
      final response = await dioClient.get(
        '${SmeUrl.orderDetail}/$code',
      );

      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null && data['Result']['OrderResponse'] != null) {
        item = data['Result']['OrderResponse'];
        details = data['Result']['OrderDetails'];
      }
      
    } catch (e) {

    }
    return [item, details];
  }

  getSearchCompany(taxCode) async {
    dynamic item;
    try {
      // Thêm branchId và deviceId vào API call
      final response = await dioClient.get(
        'https://cms-api.goldensme.com/api/tax/lookup?tax=$taxCode&branchId=3&deviceId=BP22.250325.006',
      );
      dynamic data = response.data;
      if(data.containsKey('ma_so_thue')) {
        item = data;
      }
      else {
        // Không hiển thị error ở đây, để view xử lý
        return null;
      }
    } catch (e) {
      return null;
    }
    return item;
  }

  postCreateInvoice(
      orderCode,
      date,
      customerId,
      customerName,
      customerPhone,
      customerAddress,
      taxCode,
      companyName,
      companyAddress,
      note
      ) async {
    dynamic body = {
      "OrderCode": orderCode,
      "InvoiceNumber": AppFunction.generateRandomString(10),
      "InvoiceSeries": "",
      "InvoiceDate": date,
      "CustomerId": customerId,
      "CustomerName": customerName,
      "CustomerPhone": customerPhone,
      "CustomerAddress": customerAddress,
      "TaxCode": taxCode,
      "CompanyName": companyName,
      "CompanyAddress": companyAddress,
      "Notes": note
    };
    try {
      final response = await dioClient.post(
          SmeUrl.orderCreateInvoice,
          data: body
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        AppFunction.showSuccess('Xuất hoá đơn thành công');
        return true;
      }
      else {
        AppFunction.showSuccess(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi trong quá trình xuất hoá đơn');
      return false;
    }
  }


 // GETX CREATE BILL 
  
  searchCompany(String taxCode) async {
    // Reset error trước khi tìm kiếm
    texCodeError.value = '';

    // Kiểm tra nếu mã số thuế trống
    // if (taxCode.trim().isEmpty) {
    //   texCodeError.value = 'Vui lòng nhập mã số thuế';
    //   return null;
    // }

    isSearchingCompany.value = true;
    try {
      dynamic item = await getSearchCompany(taxCode);
      if (item != null &&
          item['ten_cty'] != null &&
          item['ten_cty'].toString().trim().isNotEmpty) {
        // Có tên công ty - là mã số thuế công ty hợp lệ
        texCodeError.value = '';
        return {
          'companyName': item['ten_cty'] ?? '',
          'companyAddress': item['dia_chi'] ?? '',
          'isValid': true
        };
      } else {
        // Không có tên công ty - không phải mã số thuế công ty
        texCodeError.value = 'Đây không phải mã số thuế công ty, kiểm tra lại';
        return {
          'companyName': '',
          'companyAddress': '',
          'isValid': false
        };
      }
    } finally {
      isSearchingCompany.value = false;
    }
  }

  

}



