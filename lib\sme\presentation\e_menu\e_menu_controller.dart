import 'package:get/get.dart';

import '../../domain/entities/menu/menu_entity.dart';
import '../../domain/usecases/menu/menu_usecase.dart';

class EMenuController extends GetxController {
  final MenuUseCase menuUseCase;

  EMenuController({required this.menuUseCase});

  var isLoading = false.obs;
  var error = ''.obs;
  var menuEntity = Rxn<MenuSMEEntity>();
  var selectedCategoryIndex = 0.obs;
  var allCategories = <Map<String, dynamic>>[].obs;
  var allProducts = <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadMenuTree();
  }

  Future<void> loadMenuTree() async {
    try {
      isLoading(true);
      final result = await menuUseCase();
      menuEntity.value = result;

      // Xử lý dữ liệu lồng nhau
      final timeSaleMenus = result.fullJson['TimeSaleMenus'] as List?;
      if (timeSaleMenus != null && timeSaleMenus.isNotEmpty) {
        final itemGroups = timeSaleMenus.first['ItemGroups'] as List? ?? [];

        // Lấy tất cả categories (bao gồm cả parent và children)
        final categories = _extractAllCategories(itemGroups);
        allCategories.assignAll(categories);

        // Lấy tất cả products từ tất cả các cấp
        final products = _extractAllProducts(itemGroups);
        allProducts.assignAll(products);
      }
        } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading(false);
    }
  }

  // Hàm lấy tất cả categories (bao gồm cả parent và children)
  List<Map<String, dynamic>> _extractAllCategories(List<dynamic> itemGroups) {
    final categories = <Map<String, dynamic>>[];

    for (final group in itemGroups) {
      final groupMap = Map<String, dynamic>.from(group);

      // Chỉ thêm nếu có sản phẩm
      if (hasProducts(groupMap)) {
        categories.add(groupMap);

        // Thêm children nếu có và có sản phẩm
        final children = groupMap['Children'] as List? ?? [];
        for (final child in children) {
          final childMap = Map<String, dynamic>.from(child);
          if (hasProducts(childMap)) {
            categories.add(childMap);
          }
        }
      }
    }

    return categories;
  }

  // Hàm trích xuất tất cả products từ tất cả các cấp
  List<Map<String, dynamic>> _extractAllProducts(List<dynamic> itemGroups) {
    final products = <Map<String, dynamic>>[];

    for (final group in itemGroups) {
      final groupMap = Map<String, dynamic>.from(group);

      // Thêm products từ group hiện tại (chỉ lấy sản phẩm AVAILABLE)
      final items = (groupMap['Items'] as List? ?? [])
          .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
          .toList();
      products.addAll(items.map((e) => Map<String, dynamic>.from(e)));

      // Thêm products từ children nếu có (chỉ lấy sản phẩm AVAILABLE)
      final children = groupMap['Children'] as List? ?? [];
      for (final child in children) {
        final childMap = Map<String, dynamic>.from(child);
        final childItems = (childMap['Items'] as List? ?? [])
            .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
            .toList();
        products.addAll(childItems.map((e) => Map<String, dynamic>.from(e)));
      }
    }

    return products;
  }

  // Lấy tất cả products từ category được chọn (bao gồm cả từ các category con)
  List<Map<String, dynamic>> getProductsForSelectedCategory() {
    if (allCategories.isEmpty || selectedCategoryIndex.value >= allCategories.length) {
      return [];
    }

    final selectedCategory = allCategories[selectedCategoryIndex.value];
    final allItems = <Map<String, dynamic>>[];

    // Lấy items trực tiếp từ category (chỉ lấy AVAILABLE)
    final items = (selectedCategory['Items'] as List? ?? [])
        .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
        .toList();
    allItems.addAll(items.map((e) => Map<String, dynamic>.from(e)));

    // Lấy items từ tất cả children (chỉ lấy AVAILABLE)
    final children = selectedCategory['Children'] as List? ?? [];
    for (final child in children) {
      final childMap = Map<String, dynamic>.from(child);
      final childItems = (childMap['Items'] as List? ?? [])
          .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
          .toList();
      allItems.addAll(childItems.map((e) => Map<String, dynamic>.from(e)));
    }

    return allItems;
  }

  // Lấy tất cả children categories của category hiện tại
  List<Map<String, dynamic>> getChildrenCategories() {
    if (allCategories.isEmpty || selectedCategoryIndex.value >= allCategories.length) {
      return [];
    }

    final selectedCategory = allCategories[selectedCategoryIndex.value];
    final children = selectedCategory['Children'] as List? ?? [];
    return children.map((e) => Map<String, dynamic>.from(e)).toList();
  }

  List<Map<String, dynamic>> getChildCategoriesOfSelected() {
    if (allCategories.isEmpty || selectedCategoryIndex.value >= allCategories.length) {
      return [];
    }
    final selectedCategory = allCategories[selectedCategoryIndex.value];
    final children = selectedCategory['Children'] as List? ?? [];
    return children.map((e) => Map<String, dynamic>.from(e)).toList();
  }

  Map<String, List<Map<String, dynamic>>> getGroupedProductsByChildCategory() {
    final children = getChildCategoriesOfSelected();
    final result = <String, List<Map<String, dynamic>>>{};

    for (final child in children) {
      final name = child['ItemGroupName'] ?? 'Danh mục con';
      final items = (child['Items'] as List? ?? [])
          .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
          .map((e) => Map<String, dynamic>.from(e))
          .toList();

      if (items.isNotEmpty) {
        result[name] = items;
      }
    }

    return result;
  }
  bool hasAvailableProducts(Map<String, dynamic> category) {
    // Kiểm tra sản phẩm trực tiếp trong danh mục
    final directItems = (category['Items'] as List? ?? [])
        .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
        .isNotEmpty;

    if (directItems) return true;

    // Kiểm tra sản phẩm trong các danh mục con
    final children = category['Children'] as List? ?? [];
    for (final child in children) {
      final childMap = Map<String, dynamic>.from(child);
      final hasChildItems = (childMap['Items'] as List? ?? [])
          .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
          .isNotEmpty;

      if (hasChildItems) return true;
    }

    return false;
  }

  bool hasProducts(Map<String, dynamic> category) {
    // Kiểm tra có items trực tiếp không (chỉ lấy AVAILABLE)
    final hasDirectItems = (category['Items'] as List? ?? [])
        .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
        .isNotEmpty;

    if (hasDirectItems) return true;

    // Kiểm tra trong children
    final children = category['Children'] as List? ?? [];
    for (final child in children) {
      final childMap = Map<String, dynamic>.from(child);
      final hasChildItems = (childMap['Items'] as List? ?? [])
          .where((item) => (item['AvailableStatus'] as String?) == 'AVAILABLE')
          .isNotEmpty;

      if (hasChildItems) return true;
    }

    return false;
  }
}