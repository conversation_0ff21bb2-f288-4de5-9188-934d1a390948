import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';

class EinvoiceConfigView extends StatefulWidget {
  const EinvoiceConfigView({super.key});

  @override
  State<EinvoiceConfigView> createState() => _EinvoiceConfigViewState();
}

class _EinvoiceConfigViewState extends State<EinvoiceConfigView> {
  //variable
  AuthController authController = Get.find();
  TextEditingController apiController = TextEditingController();
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController serialNoController = TextEditingController();
  TextEditingController patternController = TextEditingController();
  TextEditingController taxCodeController = TextEditingController();
  bool isSign = true;
  bool isActive = true;
  String type = 'insert';

  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    AppFunction.showLoading();
    dynamic item = await authController.getEinvoiceConfig();
    if (item != null) {
      type = 'update';
      apiController.text = item['ApiUrl'];
      serialNoController.text = item['SerialNo'];
      patternController.text = item['Patten'];
      usernameController.text = item['UserName'];
      passwordController.text = item['Password'];
      taxCodeController.text = item['TaxCode'];
      isSign = item['MIN_IsSign'];
      isActive = item['IsActived'];
      setState(() {

      });
    }
    AppFunction.hideLoading();
  }

  save() async {
    AppFunction.showLoading();
    bool success = await authController.postSaveEinvoiceConfig(
        serialNoController.text,
        patternController.text,
        usernameController.text,
        passwordController.text,
        apiController.text,
        taxCodeController.text,
        isSign,
        isActive,
        type
    );
    if (success) {
      Get.back();
    }
    AppFunction.hideLoading();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Cấu hình hoá đơn điện tử'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            // Container(
            //   width: double.infinity,
            //   decoration: BoxDecoration(
            //     color: AppColors.shadow.withValues(alpha: 0.03),
            //     borderRadius: BorderRadius.circular(10),
            //     boxShadow: [
            //       BoxShadow(
            //         color: Colors.grey.withValues(alpha: 0.05),
            //         spreadRadius: 0,
            //         blurRadius: 1,
            //         offset: Offset(0, 3)
            //       ),
            //     ],
            //   ),
            //   padding: EdgeInsets.all(10),
            //   margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
            //   child: Column(
            //     mainAxisAlignment: MainAxisAlignment.start,
            //     crossAxisAlignment: CrossAxisAlignment.start,
            //     children: [
            //       "Thiết lập kết nối M-Invoice"
            //       CustomTextField(
            //         controller: TextEditingController(),
            //         label: 'Link quản trị',
            //       ),
            //       CustomTextField(
            //         controller: TextEditingController(),
            //         label: 'Tên đăng nhập (Portal)',
            //       ),
            //       CustomTextField(
            //         controller: TextEditingController(),
            //         label: 'Mật khẩu (Portal)',
            //         secure: true,
            //       ),
            //     ],
            //   ),
            // ),
            Center(child: CustomText(text: 'Thiết lập kết nối M-Invoice', bold: true, size: 18,)),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.shadow.withValues(alpha: 0.03),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.05),
                      spreadRadius: 0,
                      blurRadius: 1,
                      offset: Offset(0, 3)
                  ),
                ],
              ),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(text: 'Thông tin kết nối API', bold: true, size: 18,),
                  CustomTextField(
                    controller: apiController,
                    label: 'Api Url',
                    required: true,
                  ),
                  CustomTextField(
                    controller: usernameController,
                    label: 'Tên đăng nhập (API)',
                    required: true,
                  ),
                  CustomTextField(
                    controller: passwordController,
                    label: 'Mật khẩu (API)',
                    secure: true,
                    required: true,
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.shadow.withValues(alpha: 0.03),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.05),
                      spreadRadius: 0,
                      blurRadius: 1,
                      offset: Offset(0, 3)
                  ),
                ],
              ),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(text: 'Thông tin hoá đơn', bold: true, size: 18,),
                  CustomTextField(
                    controller: taxCodeController,
                    label: 'Mã số thuế',
                    required: true,
                  ),
                  CustomTextField(
                    controller: patternController,
                    label: 'Mẫu số',
                    hint: 'Mẫu số (VD: 1GTkT0)',
                    required: true,
                  ),
                  CustomTextField(
                    controller: serialNoController,
                    label: 'Ký hiệu',
                    hint: 'Ký hiệu (VD: AA/23E)',
                    required: true,
                  ),
                ],
              ),
            ),
            InkWell(
              onTap: () {
                setState(() {
                  isSign = !isSign;
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 25,
                    height: 25,
                    margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                    decoration: BoxDecoration(
                        border: Border.all(
                            width: 2,
                            color: isSign ? AppColors.primary : AppColors.shadow
                        ),
                        borderRadius: BorderRadius.circular(5),
                        color: isSign ? AppColors.primary : Colors.white
                    ),
                    child: isSign ? Center(child: Icon(Icons.check, color: Colors.white, size: 22,)) : null,
                  ),
                  CustomText(text: 'Ký ngay lập tức', bold: true,)
                ],
              ),
            ),
            SizedBox(height: 20,),
            InkWell(
              onTap: () {
                setState(() {
                  isActive = !isActive;
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 25,
                    height: 25,
                    margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                    decoration: BoxDecoration(
                        border: Border.all(
                            width: 2,
                            color: isActive ? AppColors.primary : AppColors.shadow
                        ),
                        borderRadius: BorderRadius.circular(5),
                        color: isActive ? AppColors.primary : Colors.white
                    ),
                    child: isActive ? Center(child: Icon(Icons.check, color: Colors.white, size: 22,)) : null,
                  ),
                  CustomText(text: 'Kích hoạt', bold: true,)
                ],
              ),
            ),
            SizedBox(height: 50,)
          ],
        ),
      ),
    );
  }
}
