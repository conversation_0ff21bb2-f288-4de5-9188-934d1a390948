import '../../../../../core/constants/sme_url.dart';

class ApiUrl {
  static const baseURL = SmeUrl.baseURL;
  //static const baseURL = 'https://api-sme-center.goldensme.com/api';

  static const branch = '$baseURL/core/auth/info';
  // Dashboard
  static const overviewRevenue = '$baseURL/sme/report/dashboard/overview';
  static const topBottomBranchesRevenue = '$baseURL/sme/report/dashboard/highest-lowest-revenue';
  static const revenue7days = '$baseURL/sme/report/dashboard/revenue-7-days';
  static const topBranchesRevenueByDate = '$baseURL/sme/report/dashboard/highest-branches-revenue-by-day';
  // By Items
  //static const totalAmountItemsRevenue = '$baseURL/sme/report/item/overview';
  static const topAndBottomItemsRevenue = '$baseURL/sme/report/item/highest-lowest-revenue';
  static const topItemsRevenueByDate = '$baseURL/sme/report/item/highest-item-revenue-by-day';
  // By Category
  static const totalAmountCatsRevenue = '$baseURL/CateReport/GetTotalAmountCatsRevenue';
  static const topAndBottomCategoriesByAmount = '$baseURL/sme/report/item-group/highest-lowest-revenue';
  static const topCatsRevenueByDate = '$baseURL/sme/report/item-group/highest-itemgroup-revenue-by-day';
  // By Payment
  static const totalPaymentAmount = '$baseURL/sme/report/payment-method/total-revenue';
  static const totalPaymentAmountByDate = '$baseURL/sme/report/payment-method/highest-payment-revenue-by-day';
}