import '../../../../core/constants/api_url.dart';
import '../../../../core/network/dio_client.dart';
import '../../models/sale_common/tables_response_model.dart';

abstract class TablesRemoteDataSource {
  Future<List<TablesModel>> getTables(int branchId, int areaId);
}

class TablesRemoteDataSourceImpl implements TablesRemoteDataSource {
  final DioClient dioClient;

  TablesRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<TablesModel>> getTables(int branchId, int areaId) async {
    try {
      final response = await dioClient.get(
        ApiUrl.tables,
        queryParameters: {'branchId': branchId, 'areaId': areaId},
      );
      return TablesListResponseModel.fromJson(response.data).tables;
    } catch (e) {
      rethrow;
    }
  }
}
