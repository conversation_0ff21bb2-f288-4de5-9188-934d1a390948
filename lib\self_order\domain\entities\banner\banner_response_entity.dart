class BannerListResponseEntity {
  BannerListResponseEntity({
    required this.success,
    required this.code,
    required this.message,
    required this.result,
    required this.errorDetail,
  });

  final bool? success;
  final int? code;
  final String? message;
  final List<BannerResponseEntity> result;
  final dynamic errorDetail;

}

class BannerResponseEntity {
  BannerResponseEntity({
    required this.id,
    required this.fileUrl,
    required this.contentId,
    required this.isActive,
    required this.lastUpdatedAt,
    required this.branchId,
  });

  final int? id;
  final String? fileUrl;
  final dynamic contentId;
  final bool? isActive;
  final DateTime? lastUpdatedAt;
  final int? branchId;

}
