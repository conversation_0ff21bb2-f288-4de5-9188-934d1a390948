import 'package:flutter/material.dart';

import '../theme/app_colors.dart';
import '../theme/app_style.dart';

class DefaultButton extends StatelessWidget {
  final VoidCallback onPress;
  final String? title;
  final Color color;
  final double widthPercentage;
  final double heightPercentage;
  final Widget? child;
  final double borderRadius;
  final TextStyle? titleStyle;
  final Color? borderColor;

  const DefaultButton({
    super.key,
    required this.onPress,
    this.title,
    required this.color,
    this.widthPercentage = 1,
    this.heightPercentage = 1,
    this.child,
    this.borderRadius = 16,
    this.titleStyle,
    this.borderColor
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final buttonWidth = width * widthPercentage;
    final buttonHeight = width * heightPercentage;

    return InkWell(
      onTap: onPress,
      child: Container(
        width: buttonWidth,
        height: buttonHeight,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(borderRadius),
          border: borderColor != null ? Border.all(
            width: 2,
            color: borderColor!
          ) : null
        ),
        child: child != null
            ? Align(
          alignment: Alignment.centerLeft,
          child: child,
        )
            : Center(
          child: Text(
            title ?? '',
            textAlign: TextAlign.center,
            style: titleStyle ??
                PrimaryFont.bold.copyWith(
                  color: AppColors.primary,
                  fontSize: width * 0.035,
                ),
          ),
        ),
      ),
    );
  }
}
