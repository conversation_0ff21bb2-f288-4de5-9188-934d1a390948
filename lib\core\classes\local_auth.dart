import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:local_auth/local_auth.dart';

class LocalAuth {
  static final localAuth = LocalAuthentication();

  static Future<bool> canAuthenticate() async => await localAuth.canCheckBiometrics || await localAuth.isDeviceSupported();

  static Future<bool> authenticate() async{
    try{
      if(!await canAuthenticate()){
        AppFunction.showError('Thiết bị của bạn không hỗ trợ sinh trắc học');
        return false;
      }
      return await localAuth.authenticate(
          localizedReason: 'User fingerprint to authenticate',
          options: AuthenticationOptions(
              useErrorDialogs: true,
              stickyAuth: true
          )
      );
    }
    catch(ex){
      return false;
    }
  }
}