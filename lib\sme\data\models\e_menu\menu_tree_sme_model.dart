import 'package:hive/hive.dart';

part 'menu_tree_sme_model.g.dart';

@HiveType(typeId: 2)
class MenuTreeSMEModel extends HiveObject {
  @HiveField(0)
  final String lastUpdatedAt;

  @HiveField(1)
  final Map<String, dynamic> fullJson;

  MenuTreeSMEModel({
    required this.lastUpdatedAt,
    required this.fullJson,
  });

  factory MenuTreeSMEModel.fromJson(Map<String, dynamic> json) {
    return MenuTreeSMEModel(
      lastUpdatedAt: json['LastUpdatedAt'] ?? '',
      fullJson: json,
    );
  }

  Map<String, dynamic> toJson() => fullJson;
}
