import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/network/dio_client.dart';

class NewsController extends GetxController {
  //variable
  final DioClient dioClient = DioClient();

  //function
  getNews() async {
    List list = [];
    try {
      final Dio tempDio = Dio(
        BaseOptions(
          baseUrl: 'https://cms-api.goldensme.com/api',
          headers: {
            'x-api-key': GlobalVar.newsApiKey,
          },
          responseType: ResponseType.json,
          sendTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 60),
        ),
      );

      final response = await tempDio.get(
        '/content/public/list',
      );
      dynamic data = response.data;
      if (data['success'] && data['data'] != null && data['data']['items'] != null) {
        list = data['data']['items'];
      }
    }
    catch(e) {

    }
    return list;
  }

  getNewsDetail(id) async {
    dynamic item;
    try {
      final Dio tempDio = Dio(
        BaseOptions(
          baseUrl: 'https://cms-api.goldensme.com/api',
          headers: {
            'x-api-key': GlobalVar.newsApiKey,
          },
          responseType: ResponseType.json,
          sendTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 60),
        ),
      );

      final response = await tempDio.get(
        '/content/public/$id',
      );
      dynamic data = response.data;
      if (data['success'] && data['data'] != null) {
        item = data['data'];
      }
    }
    catch(e) {

    }

    return item;
  }
}