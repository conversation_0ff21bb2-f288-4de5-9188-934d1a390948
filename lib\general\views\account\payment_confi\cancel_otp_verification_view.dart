import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/classes/app_function.dart';
import '../../../../core/components/custom_back_button.dart';
import '../../../../core/components/custom_text.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../controllers/bank_account_controller.dart';

class CancelOtpVerificationView extends StatefulWidget {
  final String requestId;
  final String authorizationId;
  final int autoId;
  final int branchId;

  const CancelOtpVerificationView({
    super.key,
    required this.requestId,
    required this.authorizationId,
    required this.autoId,
    required this.branchId,
  });

  @override
  State<CancelOtpVerificationView> createState() => _CancelOtpVerificationViewState();
}

class _CancelOtpVerificationViewState extends State<CancelOtpVerificationView> {
  final TextEditingController _otpController = TextEditingController();
  final BankAccountController controller = Get.find<BankAccountController>();

  Future<void> _verifyCancelOtp() async {
    if (_otpController.text.isEmpty) {
      AppFunction.showError('Vui lòng nhập mã OTP');
      return;
    }

    final success = await controller.verifyCancelOtp(
      _otpController.text,
      widget.requestId,
      widget.authorizationId,
    );

    if (success) {
      Get.back(); // Quay lại màn hình thông tin tài khoản
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        leading: const CustomBackButton(),
        title: CustomText(
          text: "Xác thực hủy liên kết",
          size: 20,
          bold: true,
          color: Colors.white,
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 32),
            Text(
              "Mã OTP gửi đến ứng dụng ACB ONE của bạn để xác nhận hủy liên kết",
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 24),
            TextField(
              controller: _otpController,
              keyboardType: TextInputType.number,
              maxLength: 10,
              decoration: InputDecoration(
                hintText: "Nhập mã OTP",
                prefixIcon: const Icon(Icons.lock_outline),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              height: 45,
              child: ElevatedButton(
                onPressed: _verifyCancelOtp,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.danger,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: controller.isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : CustomText(
                  text: "Xác nhận hủy liên kết",
                  color: Colors.white,
                  bold: true,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}