import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/views/auth/bluetooth_printer_config_view.dart';
import 'package:gls_self_order/general/views/auth/usb_printer_config_view.dart';

class ConfigPrinterView extends StatefulWidget {
  const ConfigPrinterView({super.key});

  @override
  State<ConfigPrinterView> createState() => _ConfigPrinterViewState();
}

class _ConfigPrinterViewState extends State<ConfigPrinterView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: '<PERSON><PERSON><PERSON> hình máy in'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(10),
        child: Column(
          children: [
            InkWell(
              onTap: () {
                Get.to(() => BluetoothPrinterConfigView());
              },
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: AppColors.primary.withValues(alpha: 0.1)
                ),
                padding: EdgeInsets.all(10),
                margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(180)
                      ),
                      margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: Icon(Icons.bluetooth, size: 30, color: AppColors.primary,),
                    ),
                    Expanded(
                      child: CustomText(text: 'Máy in Bluetooth', size: 20, bold: true,),
                    )
                  ],
                ),
              ),
            ),
            InkWell(
              onTap: () {
                Get.to(() => UsbPrinterConfigView());
              },
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: AppColors.primary.withValues(alpha: 0.1)
                ),
                padding: EdgeInsets.all(10),
                margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(180)
                      ),
                      margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: Icon(Icons.usb, size: 30, color: AppColors.primary,),
                    ),
                    Expanded(
                      child: CustomText(text: 'Máy in USB', size: 20, bold: true,),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
