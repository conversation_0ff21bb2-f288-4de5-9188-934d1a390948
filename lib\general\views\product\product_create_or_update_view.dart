import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_money_field.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_text_vat.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/validators/product_create_or_update_validator.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';
import 'package:gls_self_order/general/views/product/product_import_excel_view.dart';
import 'package:image_picker/image_picker.dart';
import 'package:simple_barcode_scanner/simple_barcode_scanner.dart';

class ProductCreateOrUpdateView extends StatefulWidget {
  final dynamic item;
  const ProductCreateOrUpdateView({super.key, this.item});

  @override
  State<ProductCreateOrUpdateView> createState() =>
      _ProductCreateOrUpdateViewState();
}

class _ProductCreateOrUpdateViewState extends State<ProductCreateOrUpdateView> {
  //variable
  ProductController productController = Get.find();
  File? image;
  String imageOnline = '';
  final ImagePicker imagePicker = ImagePicker();
  List units = [];
  List categories = [];
  TextEditingController productNameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController originalPriceController = TextEditingController();
  TextEditingController categoryController = TextEditingController();
  TextEditingController vatPercentController = TextEditingController();
  TextEditingController unitController = TextEditingController();
  TextEditingController barCodeController = TextEditingController();
  TextEditingController idProductController = TextEditingController();
  TextEditingController priceRealisticController = TextEditingController();
  TextEditingController tncnPercentController = TextEditingController();
  TextEditingController gtgtPercentController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  int categorySelected = 0;
  int unitSelected = 0;
  bool includeVat = true;
  bool isActive = true;
  String availableStatus = 'AVAILABLE';
  String itemNo = '';

  //function
  @override
  void initState() {
    super.initState();
    initData();
    vatPercentController.addListener(updateRealisticPrice);
    priceController.addListener(updateRealisticPrice);
    // Thêm listener cho thuế TNCN và GTGT khi UserInfo.orgType == 'HKD'
    if (UserInfo.orgType == 'HKD') {
      tncnPercentController.addListener(updateRealisticPrice);
      gtgtPercentController.addListener(updateRealisticPrice);
    }
  }

  initData() async {
    await getDetail();
    await getUnits();
    await getCategories();
  }

  getDetail() async {
    if (widget.item != null) {
      AppFunction.showLoading();
      dynamic item =
          await productController.getDetailProduct(widget.item['Id']);
      if (item != null) {
        productNameController.text = item['ItemName'];
        idProductController.text = item['ItemNo'];
        // Format giá bán với CurrencyInputFormatter để đảm bảo tương thích
        double priceValue = item['Price'].toDouble();
        priceController.text = toCurrencyString(
          priceValue.toStringAsFixed(0),
          leadingSymbol: '',
          useSymbolPadding: true,
          thousandSeparator: ThousandSeparator.Period,
          mantissaLength: 0,
        );

        // Format giá vốn với CurrencyInputFormatter để đảm bảo tương thích
        double costPriceValue = item['CostPrice'].toDouble();
        originalPriceController.text = toCurrencyString(
          costPriceValue.toStringAsFixed(0),
          leadingSymbol: '',
          useSymbolPadding: true,
          thousandSeparator: ThousandSeparator.Period,
          mantissaLength: 0,
        );

        double vatValue = item['VatPercent'].toDouble();
        if (vatValue == vatValue.toInt()) {
          // Nếu là số nguyên thì bỏ phần thập phân
          vatPercentController.text = vatValue.toInt().toString();
        } else {
          // Nếu có phần thập phân thì hiển thị 1 chữ số thập phân
          vatPercentController.text = vatValue.toStringAsFixed(1);
        }

        categorySelected = item['ItemGroupId'] ?? 0;
        unitSelected = item['UnitId'] ?? 0;
        unitController.text = item['UnitName'] ?? '';
        includeVat = item['IncludeVat'] ?? true;
        availableStatus = item['AvailableStatus'];
        isActive = item['IsActive'];
        barCodeController.text = item['Barcode'] ?? '';
        if (item['MediaUrls'].isNotEmpty) {
          imageOnline =
              item['MediaUrls'][item['MediaUrls'].length - 1]['MediaUrl'];
        }
        itemNo = item['ItemNo'] ?? '';
        tncnPercentController.text = item['GroupPITaxPer'].toString();
        gtgtPercentController.text = item['GroupVatTaxPer'].toString();
        AppFunction.hideLoading();
        setState(() {});
        // Gọi updateRealisticPrice() sau khi load dữ liệu để tính toán lại giá bán thực tế
        updateRealisticPrice();
      }
    }
  }

  getCategories() async {
    categories = await productController.getCategories();
    if (widget.item != null) {
      for (int i = 0; i < categories.length; i++) {
        if (categories[i]['Id'] == widget.item['ItemGroupId']) {
          categoryController.text = categories[i]['ItemGroupName'];
        }
      }
    }
  }

  getUnits() async {
    units = await productController.getUnits();
    if (widget.item != null) {
      // Sử dụng unitSelected đã được set trong getDetail()
      for (int i = 0; i < units.length; i++) {
        if (units[i]['UOM_AUTOID'] == unitSelected) {
          unitController.text = units[i]['UOM_NAME'];
          break; // Thoát khỏi vòng lặp khi tìm thấy
        }
      }
    }
  }

  Future<void> pickImageFromGallery() async {
    final XFile? pickedFile = await imagePicker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 85,
    );
    if (pickedFile != null) {
      setState(() => image = File(pickedFile.path));
    }
  }

  void updateRealisticPrice() {
    if (UserInfo.orgType == 'DN') {
      double price =
          double.tryParse(toNumericString(priceController.text)) ?? 0;
      double vatPercent =
          double.tryParse(toNumericString(vatPercentController.text)) ?? 0;

      double priceWithVat = includeVat ? price : price * (1 + vatPercent / 100);

      priceRealisticController.text = toCurrencyString(
        priceWithVat.toStringAsFixed(0),
        thousandSeparator: ThousandSeparator.Period,
        mantissaLength: 0,
        leadingSymbol: '',
      );
    } else {
      double price =
          double.tryParse(toNumericString(priceController.text)) ?? 0;
      double tncnPercent =
          double.tryParse(toNumericString(tncnPercentController.text)) ?? 0;
      double gtgtPercent =
          double.tryParse(toNumericString(gtgtPercentController.text)) ?? 0;

      double priceWithTaxes = price * (1 + (tncnPercent + gtgtPercent) / 100);

      priceRealisticController.text = toCurrencyString(
        priceWithTaxes.toStringAsFixed(0),
        thousandSeparator: ThousandSeparator.Period,
        mantissaLength: 0,
        leadingSymbol: '',
      );
    }
  }

  Future<void> pickImageFromCamera() async {
    final XFile? pickedFile = await imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      setState(() => image = File(pickedFile.path));
    }
  }

  pickCategory() {
    if (categories.isEmpty) return;

    _showSearchableBottomSheet(
      title: 'Chọn danh mục',
      titleHint: 'Tìm kiếm danh mục',
      items: categories,
      displayKey: 'ItemGroupName',
      selectedId: categorySelected,
      onItemSelected: (item) {
        setState(() {
          categorySelected = item['Id'];
          categoryController.text = item['ItemGroupName'];
        });
      },
    );
  }

  pickUnit() {
    if (units.isEmpty) return;

    _showSearchableBottomSheet(
      title: 'Chọn đơn vị tính',
      titleHint: 'Tìm kiếm đơn vị tính',
      items: units,
      displayKey: 'UnitName',
      selectedId: unitSelected,
      onItemSelected: (item) {
        setState(() {
          unitSelected = item['UnitId'];
          unitController.text = item['UnitName'];
        });
      },
    );
  }

  save() async {
    if (!_formKey.currentState!.validate()) return;
    AppFunction.showLoading();
    int productId = await productController.postCreateOrUpdateProduct(
        widget.item != null ? widget.item['Id'] : null,
        productNameController.text,
        toNumericString(originalPriceController.text),
        toNumericString(priceController.text),
        categorySelected,
        toNumericString(vatPercentController.text,
            allowPeriod: true, mantissaSeparator: ','),
        availableStatus,
        includeVat,
        unitSelected,
        unitController.text,
        barCodeController.text,
        isActive,
        toNumericString(tncnPercentController.text,
            allowPeriod: true, mantissaSeparator: ','),
        toNumericString(gtgtPercentController.text,
            allowPeriod: true, mantissaSeparator: ','),
        itemNo);
    if (productId != 0) {
      if (image != null) {
        String imagePath =
            await productController.postUploadImageProduct(image);
        if (imagePath != '') {
          await productController.postAddImageForProduct(productId, imagePath);
        }
      }
      Get.back(result: productId);
    }
    AppFunction.hideLoading();
  }

  scanBarcode() async {
    try {
      String? res = await SimpleBarcodeScanner.scanBarcode(
        context,
        barcodeAppBar: const BarcodeAppBar(
          appBarTitle: 'Quét mã vạch',
          centerTitle: false,
          enableBackButton: true,
          backButtonIcon: Icon(Icons.arrow_back_ios),
        ),
        isShowFlashIcon: true,
        delayMillis: 500,
        cameraFace: CameraFace.back,
        scanFormat: ScanFormat.ONLY_BARCODE,
      );

      if (res != null && res.isNotEmpty && res != "-1") {
        setState(() {
          barCodeController.text = res;
        });
      } else {
        AppFunction.showError('Không thể quét mã hoặc mã không hợp lệ');
      }
    } catch (e) {
      AppFunction.showError('Lỗi khi quét mã: ${e.toString()}');
    }
  }

  void _showSearchableBottomSheet({
    required String title,
    required String titleHint,
    required List<dynamic> items,
    required String displayKey,
    required int selectedId,
    required Function(dynamic) onItemSelected,
  }) {
    final searchController = TextEditingController();
    List<dynamic> filteredItems = List.from(items);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return SizedBox(
              height: MediaQuery.of(context).size.height * 0.9,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(text: title, bold: true),
                        IconButton(
                          icon: Icon(Icons.close,
                              color: AppColors.text, size: 20),
                          onPressed: () {
                            Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: CustomTextField(
                      controller: searchController,
                      showLabel: false,
                      hint: titleHint,
                      prefixIcon: Icons.search,
                      onChanged: (value) {
                        setState(() {
                          filteredItems = items.where((item) {
                            return (item[displayKey]?.toString() ?? '')
                                .toLowerCase()
                                .contains(value.toLowerCase());
                          }).toList();
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = filteredItems[index];
                        return InkWell(
                          onTap: () {
                            onItemSelected(item);
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  width: 1,
                                  color:
                                      AppColors.shadow.withValues(alpha: 0.1),
                                ),
                              ),
                              color: item['Id'] == selectedId ||
                                      item['UOM_AUTOID'] == selectedId ||
                                      item['UnitId'] == selectedId
                                  ? AppColors.primary.withValues(alpha: 0.1)
                                  : AppColors.background,
                            ),
                            child: Text(item[displayKey]?.toString() ?? ''),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    priceController.removeListener(updateRealisticPrice);
    vatPercentController.removeListener(updateRealisticPrice);

    priceController.dispose();
    originalPriceController.dispose();
    vatPercentController.dispose();
    productNameController.dispose();
    categoryController.dispose();
    unitController.dispose();
    barCodeController.dispose();
    priceRealisticController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Tạo sản phẩm'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            Container(
              width: double.infinity,
              height: 110,
              color: AppColors.shadow.withValues(alpha: 0.1),
              padding: EdgeInsets.all(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (image != null)
                    Padding(
                      padding: EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: Image.file(
                        image!,
                        fit: BoxFit.cover,
                        width: 80,
                        height: 80,
                      ),
                    ),
                  if (imageOnline != '' && image == null)
                    Padding(
                      padding: EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: Image.network(
                        SmeUrl.baseImageUrl + imageOnline,
                        fit: BoxFit.cover,
                        width: 80,
                        height: 80,
                        errorBuilder: (context, _, __) {
                          return Image.asset(
                            'assets/images/general/no_image.jpg',
                            fit: BoxFit.cover,
                          );
                        },
                        loadingBuilder: (BuildContext context, Widget child,
                            ImageChunkEvent? loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                              child: CircularProgressIndicator(
                            color: AppColors.primary,
                          ));
                        },
                      ),
                    ),
                  if (image != null || imageOnline != '')
                    DottedBorder(
                      options: RectDottedBorderOptions(
                        color: AppColors.shadow,
                      ),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            image = null;
                            imageOnline = '';
                          });
                        },
                        child: SizedBox(
                          width: 80,
                          height: 80,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.image_not_supported,
                                color: AppColors.danger,
                              ),
                              CustomText(text: 'Xoá ảnh')
                            ],
                          ),
                        ),
                      ),
                    ),
                  if (image == null && imageOnline == '')
                    DottedBorder(
                      options: RectDottedBorderOptions(
                        color: AppColors.shadow,
                      ),
                      child: InkWell(
                        onTap: () {
                          pickImageFromGallery();
                        },
                        child: SizedBox(
                          width: 80,
                          height: 80,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.image,
                                color: AppColors.primary,
                              ),
                              CustomText(text: 'Thêm ảnh')
                            ],
                          ),
                        ),
                      ),
                    ),
                  if (image == null && imageOnline == '')
                    Padding(
                      padding: EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: DottedBorder(
                        options:
                            RectDottedBorderOptions(color: AppColors.shadow),
                        child: InkWell(
                          onTap: () {
                            pickImageFromCamera();
                          },
                          child: SizedBox(
                            width: 80,
                            height: 80,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.camera,
                                  color: AppColors.primary,
                                ),
                                CustomText(text: 'Chụp ảnh')
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                ],
              ),
            ),
            Padding(
                padding: EdgeInsets.all(10),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: barCodeController,
                              label: 'Barcode',
                            ),
                          ),
                          SizedBox(width: 10),
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.background,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                            child: IconButton(
                              icon: Icon(Icons.qr_code_scanner,
                                  color: AppColors.text),
                              onPressed: () async {
                                await scanBarcode();
                              },
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 7,
                      ),
                      if (widget.item != null) ...[
                        CustomTextField(
                          controller: idProductController,
                          label: 'Id sản phẩm',
                          readOnly: true,
                        ),
                      ],
                      SizedBox(
                        height: 7,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: categoryController,
                              label: 'Danh mục',
                              readOnly: true,
                              required: true,
                              isSelect: true,
                              validator: ProductCreateOrUpdateValidator
                                  .validatorCategory,
                              onTap: () {
                                pickCategory();
                              },
                            ),
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            child: CustomTextField(
                              controller: productNameController,
                              label: 'Tên sản phẩm',
                              required: true,
                              validator: ProductCreateOrUpdateValidator
                                  .validatorProductName,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: unitController,
                              label: 'Đơn vị tính',
                              readOnly: true,
                              required: true,
                              isSelect: true,
                              validator:
                                  ProductCreateOrUpdateValidator.validatorUnit,
                              onTap: () {
                                pickUnit();
                              },
                            ),
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            child: CustomMoneyField(
                              controller: originalPriceController,
                              label: 'Giá vốn',
                              required: true,
                              validator: ProductCreateOrUpdateValidator
                                  .validatorOriginalPrice,
                              inputFormatters: [
                                CurrencyInputFormatter(
                                  thousandSeparator: ThousandSeparator.Period,
                                  mantissaLength: 0,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: CustomMoneyField(
                              controller: gtgtPercentController,
                              label: '% Thuế GTGT',
                              required: true,
                              maxLength: 4,
                              taxPercent: true,
                              readOnly: UserInfo.orgType == 'DN' ? false : true,
                              validator: ProductCreateOrUpdateValidator
                                  .validatorVatPercent,
                              inputFormatters: [
                                VatPercentFormatter(decimalPlaces: 1),
                              ],
                            ),
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            child: CustomMoneyField(
                              controller: tncnPercentController,
                              label: '% Thuế TNCN',
                              required: true,
                              maxLength: 4,
                              readOnly: UserInfo.orgType == 'DN' ? false : true,
                              taxPercent: true,
                              validator: ProductCreateOrUpdateValidator
                                  .validatorVatPercent,
                              inputFormatters: [
                                VatPercentFormatter(decimalPlaces: 1),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (UserInfo.orgType == 'DN') ...[
                        Row(children: [
                          Expanded(
                            child: CustomMoneyField(
                              controller: vatPercentController,
                              label: '% VAT',
                              required: true,
                              maxLength: 4,
                              taxPercent: true, // Hiển thị %
                              validator: ProductCreateOrUpdateValidator
                                  .validatorVatPercent,
                              inputFormatters: [
                                VatPercentFormatter(decimalPlaces: 1),
                              ],
                            ),
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            child: CustomMoneyField(
                              controller: priceController,
                              label: 'Giá bán',
                              required: true,
                              validator:
                                  ProductCreateOrUpdateValidator.validatorPrice,
                              inputFormatters: [
                                CurrencyInputFormatter(
                                  thousandSeparator: ThousandSeparator.Period,
                                  mantissaLength: 0,
                                ),
                              ],
                            ),
                          ),
                        ])
                      ],
                      SizedBox(
                        height: 7,
                      ),
                      Row(
                        children: [
                          if (UserInfo.orgType == 'HKD') ...[
                            Expanded(
                              child: CustomMoneyField(
                                controller: priceController,
                                label: 'Giá bán',
                                required: true,
                                validator: ProductCreateOrUpdateValidator
                                    .validatorPrice,
                                inputFormatters: [
                                  CurrencyInputFormatter(
                                    thousandSeparator: ThousandSeparator.Period,
                                    mantissaLength: 0,
                                  ),
                                ],
                              ),
                            ),
                          ],
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: CustomMoneyField(
                              controller: priceRealisticController,
                              label: 'Giá bán bao gồm thuế',
                              readOnly: true,
                              required: true,
                            ),
                          ),

                          // Expanded(
                          //   child: CustomMoneyField(
                          //     controller: vatPercentController,
                          //     label: '% VAT',
                          //     required: true,
                          //     maxLength: 4,
                          //     taxPercent: true, // Hiển thị %
                          //     validator: ProductCreateOrUpdateValidator
                          //         .validatorVatPercent,
                          //     inputFormatters: [
                          //       VatPercentFormatter(decimalPlaces: 1),
                          //     ],
                          //   ),
                          // ),
                        ],
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Row(
                        children: [
                          if (UserInfo.orgType == 'DN') ...[
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    includeVat = !includeVat;
                                    updateRealisticPrice();
                                  });
                                },
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 25,
                                      height: 25,
                                      margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          width: 2,
                                          color: includeVat
                                              ? AppColors.primary
                                              : AppColors.shadow,
                                        ),
                                        borderRadius: BorderRadius.circular(5),
                                        color: includeVat
                                            ? AppColors.primary
                                            : Colors.white,
                                      ),
                                      child: includeVat
                                          ? Center(
                                              child: Icon(
                                                Icons.check,
                                                color: Colors.white,
                                                size: 22,
                                              ),
                                            )
                                          : null,
                                    ),
                                    CustomText(
                                      text: 'Đã bao gồm VAT',
                                      bold: true,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      CustomText(
                        text: 'Tình trạng sản phẩm',
                        bold: true,
                        size: 16,
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  availableStatus = 'AVAILABLE';
                                });
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 25,
                                    height: 25,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        width: 2,
                                        color: availableStatus == 'AVAILABLE'
                                            ? AppColors.primary
                                            : AppColors.shadow,
                                      ),
                                      borderRadius: BorderRadius.circular(5),
                                      color: availableStatus == 'AVAILABLE'
                                          ? AppColors.primary
                                          : Colors.white,
                                    ),
                                    child: availableStatus == 'AVAILABLE'
                                        ? Icon(Icons.check,
                                            size: 20, color: Colors.white)
                                        : null,
                                  ),
                                  SizedBox(width: 10),
                                  CustomText(text: 'Còn hàng', bold: true),
                                ],
                              ),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  availableStatus = 'UNAVAILABLE';
                                });
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 25,
                                    height: 25,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        width: 2,
                                        color: availableStatus == 'UNAVAILABLE'
                                            ? AppColors.primary
                                            : AppColors.shadow,
                                      ),
                                      borderRadius: BorderRadius.circular(5),
                                      color: availableStatus == 'UNAVAILABLE'
                                          ? AppColors.primary
                                          : Colors.white,
                                    ),
                                    child: availableStatus == 'UNAVAILABLE'
                                        ? Icon(Icons.check,
                                            size: 20, color: Colors.white)
                                        : null,
                                  ),
                                  SizedBox(width: 10),
                                  CustomText(text: 'Hết hàng', bold: true),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      CustomText(
                        text: 'Thêm danh sách sản phẩm',
                        bold: true,
                        size: 16,
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      SizedBox(
                        width: 120,
                        height: 40,
                        child: CustomButton(
                          text: 'Import Excel',
                          onTap: () {
                            Get.to(() => const ProductImportExcelView());
                          },
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
