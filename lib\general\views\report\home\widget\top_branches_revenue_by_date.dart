import 'dart:math';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/helper/format_helper.dart';
import '../../domain/report/entities/report.dart';

class TopBranchesRevenueByDate extends StatelessWidget {
  final double width;
  final String title;
  final List<TopBranchesRevenueByDateEntity> data;

  const TopBranchesRevenueByDate({super.key, required this.data, required this.width, required this.title});

  @override
  Widget build(BuildContext context) {
    double maxRevenue = _getMaxRevenue();
    double maxY = _calculateMaxY(maxRevenue);
    final List<DateTime> allDates = data.map((e) => e.revenueDate ?? DateTime.now()).toList();
    final double bottomInterval = allDates.length <= 7 ? 1 : (allDates.length / 6).floorToDouble().clamp(1, 10);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: width * 0.015),
      padding: EdgeInsets.all(width * 0.01),
      decoration: cardDecoration(width * 0.03, AppColors.background),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(width * 0.015),
            child: Text(
              title.toUpperCase(),
              style: PrimaryFont.bold.copyWith(
                color: AppColors.text,
                fontSize: width * 0.04,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: width * 0.05),
          SizedBox(
            height: width * 0.8,
            child: LineChart(
              LineChartData(
                minX: data.length == 1 ? -0.5 : 0,
                maxX: data.length == 1 ? 0.5 : (data.length - 1).toDouble(),
                minY: 0,
                maxY: maxY,
                gridData: FlGridData(
                  drawVerticalLine: false,
                  drawHorizontalLine: true,
                  horizontalInterval: maxY / 5,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.shade300,
                      strokeWidth: 1,
                      dashArray: [5, 5],
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          FormatHelper.formatCurrency(value),
                          style: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      interval: bottomInterval,
                      getTitlesWidget: (value, meta) {
                        int index = value.toInt();
                        if (index < 0 || index >= allDates.length) return const SizedBox();

                        return Padding(
                          padding: EdgeInsets.only(top: width * 0.015),
                          child: Text(
                            DateFormat('dd/MM').format(allDates[index]),
                            style: PrimaryFont.regular.copyWith(
                              fontSize: width * 0.03,
                              color: AppColors.text,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                            FormatHelper.formatCurrency(value),
                          style: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border(
                    left: BorderSide(color: Colors.black, width: 1),
                    bottom: BorderSide(color: Colors.black, width: 1),
                    right: BorderSide(color: Colors.black, width: 1),
                  ),
                ),

                lineBarsData: _buildLineBars(),
              ),
            ),
          ),
          _buildLegend(width),
        ],
      ),
    );
  }

  double _getMaxRevenue() {
    double maxRevenue = 0;
    for (var entry in data) {
      for (var revenue in entry.revenues.values) {
        if (revenue > maxRevenue) {
          maxRevenue = revenue;
        }
      }
    }
    return maxRevenue;
  }

  double _calculateMaxY(double maxRevenue) {
    if (maxRevenue <= 0) return 1.0;

    final double exponent = (log(maxRevenue) / ln10).floorToDouble();
    final double fraction = maxRevenue / pow(10, exponent).toDouble();

    double niceFraction;
    if (fraction <= 1.5) {
      niceFraction = 1.5;
    } else if (fraction <= 3) {
      niceFraction = 3;
    } else if (fraction <= 7) {
      niceFraction = 7;
    } else if (fraction <= 10) {
      niceFraction = 10;
    } else {
      niceFraction = 15;
    }

    double niceMax = niceFraction * pow(10, exponent).toDouble();

    final double minGap = maxRevenue * 0.15;
    if (niceMax - maxRevenue < minGap) {
      niceMax = maxRevenue + minGap;

      final double newExponent = (log(niceMax) / ln10).floorToDouble();
      final double newFraction = niceMax / pow(10, newExponent).toDouble();
      niceMax = (newFraction.ceilToDouble() * pow(10, newExponent)).toDouble();
    }

    return niceMax;
  }

  List<LineChartBarData> _buildLineBars() {
    Map<String, List<FlSpot>> branchData = {};
    final colors = _generateColors();

    for (int i = 0; i < data.length; i++) {
      var entry = data[i];
      entry.revenues.forEach((branch, revenue) {
        branchData.putIfAbsent(branch, () => []).add(FlSpot(i.toDouble(), revenue));
      });
    }

    return branchData.entries.map((entry) {
      return LineChartBarData(
        spots: entry.value,
        barWidth: 1.5,
        isStrokeCapRound: true,
        color: colors[entry.key]!,
        belowBarData: BarAreaData(show: false),
      );
    }).toList();
  }

  Widget _buildLegend(double width) {
    final colors = _generateColors();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.015),
      child: Wrap(
        spacing: width * 0.02,
        runSpacing: width * 0.012,
        children: colors.entries.map((entry) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: width * 0.02,
                height: width * 0.02,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: entry.value,
                ),
              ),
              SizedBox(width: width * 0.01),
              Text(entry.key,
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Map<String, Color> _generateColors() {
    final baseColors = [Colors.blue, Colors.red, Colors.green, Colors.orange, Colors.purple, Colors.pink, Colors.teal];
    final Map<String, Color> colorMap = {};
    int index = 0;

    for (var entry in data) {
      for (var branch in entry.revenues.keys) {
        colorMap.putIfAbsent(branch, () => baseColors[index % baseColors.length]);
        index++;
      }
    }
    return colorMap;
  }
}
