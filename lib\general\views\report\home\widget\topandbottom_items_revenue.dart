import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../domain/report/entities/report.dart';

class TopAndBottomItems extends StatelessWidget {
  final double width;
  final List<ItemEntity> items;
  final String title;

  const TopAndBottomItems({
    super.key,
    required this.width,
    required this.items,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '');

    List<ItemEntity> sortedItems = [...items];
    sortedItems.sort((a, b) => (b.totalAmount ?? 0).compareTo(a.totalAmount ?? 0));

    double totalRevenue = sortedItems.fold(0.0, (sum, e) => sum + (e.totalAmount ?? 0));
    bool isEmptyData = totalRevenue == 0;

    List<Color> colors = [
      Colors.blue,
      Colors.orange,
      Colors.greenAccent,
      Colors.green,
      Colors.purpleAccent,
      Colors.red,
      Colors.teal,
      Colors.pink,
      Colors.cyan,
      Colors.brown,
    ];

    List<double> calculatedPercentages = [];

    List<PieChartSectionData> sections = isEmptyData
        ? [
      PieChartSectionData(
        value: 1,
        color: Colors.grey.withOpacity(0.3),
        radius: width * 0.12,
        title: "0%",
        titleStyle: TextStyle(fontSize: width * 0.03, color: Colors.white),
      ),
    ]
        : sortedItems.asMap().entries.map((entry) {
      int index = entry.key;
      var item = entry.value;
      double revenue = item.totalAmount ?? 0;
      double percentage = (revenue / totalRevenue) * 100;
      calculatedPercentages.add(percentage);

      return PieChartSectionData(
        value: revenue,
        title: "${percentage.toStringAsFixed(1)}%",
        color: colors[index % colors.length],
        radius: width * 0.12,
        titleStyle: TextStyle(fontSize: width * 0.03, color: Colors.white),
      );
    }).toList();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Container(
        padding: EdgeInsets.all(width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                title.toUpperCase(),
                style: PrimaryFont.bold.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.04,
                ),
              ),
            ),

            SizedBox(height: width * 0.02),
            SizedBox(
              height: width * 0.5,
              child: PieChart(
                PieChartData(
                  sections: sections,
                  borderData: FlBorderData(show: false),
                  centerSpaceRadius: width * 0.12,
                  sectionsSpace: 2,
                ),
              ),
            ),
            SizedBox(height: width * 0.02),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: isEmptyData
                  ? [
                Text(
                  "Không có dữ liệu",
                  style: PrimaryFont.regular.copyWith(
                    color: AppColors.text,
                    fontSize: width * 0.03,
                  ),
                ),
              ]
                  : sortedItems.asMap().entries.map((entry) {
                int index = entry.key;
                var item = entry.value;
                double percentage = calculatedPercentages[index];

                return Padding(
                  padding: EdgeInsets.symmetric(vertical: width * 0.01),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: width * 0.03,
                          height: width * 0.03,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: colors[index % colors.length],
                          ),
                        ),
                        SizedBox(width: width * 0.015),
                        Text(
                          "${item.itemName} - ${currencyFormat.format(item.totalAmount ?? 0)} - ${percentage.toStringAsFixed(1)}%",
                          style: PrimaryFont.regular.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.03,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}