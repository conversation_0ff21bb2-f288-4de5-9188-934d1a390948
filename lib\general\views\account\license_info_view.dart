import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';

class LicenseInfoView extends StatefulWidget {
  const LicenseInfoView({super.key});

  @override
  State<LicenseInfoView> createState() => _LicenseInfoViewState();
}

class _LicenseInfoViewState extends State<LicenseInfoView> {
  //variable
  AuthController authController = Get.find();

  //function
  @override
  void initState() {
    super.initState();
  }

  removeLicense() async {

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Bản quyền'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.shadow.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.05),
                      spreadRadius: 0,
                      blurRadius: 1,
                      offset: Offset(0, 3)
                  ),
                ],
              ),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CustomText(text: 'Khách hàng: '),
                      CustomText(text: 'Công ty Cổ Phần Café Katinat', bold: true,),
                    ],
                  ),
                  Row(
                    children: [
                      CustomText(text: 'Loại bản quyền: '),
                      CustomText(text: "Tiêu chuẩn", bold: true,),
                    ],
                  ),
                  Row(
                    children: [
                      CustomText(text: 'Mã kích hoạt: '),
                      CustomText(text: 'devtest', bold: true,),
                    ],
                  ),
                  Row(
                    children: [
                      CustomText(text: 'Ngày kích hoạt: '),
                      CustomText(text: AppFunction.formatDateNoTime("2025-06-03T00:00:00"), bold: true,),
                    ],
                  ),
                  Row(
                    children: [
                      CustomText(text: 'Ngày hết hạn: '),
                      CustomText(text: AppFunction.formatDateNoTime("2025-06-30T00:00:00"), bold: true,),
                    ],
                  ),
                  Row(
                    children: [
                      CustomText(text: 'Trạng thái: '),
                      CustomText(text: 'Hoạt động', bold: true,),
                    ],
                  ),
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 210,
                        child: CustomButton(text: 'Gỡ mã kích hoạt trên máy', onTap: () {
                          showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  title: CustomText(text: 'Bạn có chắc muốn gỡ mã kích hoạt trên máy?', maxLines: 2,),
                                  actions: [
                                    Container(
                                      width: 100,
                                      margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                      child: CustomButton(
                                        text: 'cancel'.tr,
                                        color: AppColors.shadow,
                                        onTap: () {
                                          Get.back();
                                        },
                                      ),
                                    ),
                                    Container(
                                      width: 100,
                                      margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                      child: CustomButton(
                                        text: 'confirm'.tr,
                                        color: AppColors.primary,
                                        onTap: () async {
                                          await authController.removeLicense();
                                          Get.back();
                                          Get.back();
                                          Get.back();
                                          Get.back();
                                        },
                                      ),
                                    ),
                                  ],
                                );
                              }
                          );
                        }, color: AppColors.danger),
                      )
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
