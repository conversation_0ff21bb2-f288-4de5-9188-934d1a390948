import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_header_modal.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:intl/intl.dart';

class UserCreateOrUpdateView extends StatefulWidget {
  final dynamic item;
  const UserCreateOrUpdateView({super.key, this.item});

  @override
  State<UserCreateOrUpdateView> createState() => _UserCreateOrUpdateViewState();
}

class _UserCreateOrUpdateViewState extends State<UserCreateOrUpdateView> {
  //variable
  CustomerController customerController = Get.find();
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  
  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    // if (widget.item != null) {
    //   dynamic item = await customerController.getDetail(widget.item['CustomerNo']);
    //   if (item != null) {
    //     nameController.text = item['FullName'] ?? '';
    //     phoneController.text = item['PhoneNumber'] ?? '';
    //     if (item['Birthday'] != null) {
    //       dateOfBirth = DateTime.parse(item['Birthday']);
    //       dobController.text = AppFunction.formatDateNoTime(item['Birthday']);
    //     }
    //     if (item['Gender'] != null && item['Gender'] != 0) {
    //       gender = item['Gender'];
    //     }
    //     addressController.text = item['CustomerAdress'] ?? '';
    //     setState(() {
    //
    //     });
    //   }
    // }
  }

  Widget renderItemUserGroup(item) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)
          ),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(child: CustomText(text: item['Name'] ?? '', bold: true, size: 18,),),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  pickUserGroup() async {
    dynamic customer = {
      'id': null,
      'name': null,
      'phone': null,
      'address': null
    };
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        useSafeArea: true,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, StateSetter setStateIn) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                child: Column(
                  children: [
                    CustomHeaderModal(
                      title: 'Chọn nhóm người dùng',
                      append: InkWell(
                        onTap: () async {

                        },
                        child: Icon(Icons.add, color: Colors.white, size: 30,),
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          for(dynamic item in [{'Name': 'Nhân viên'}])
                            InkWell(
                              onTap: () {

                              },
                              child: renderItemUserGroup(item),
                            )
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          );
        }
    );
    return customer;
  }

  
  save() async {
    AppFunction.showLoading();
    if (widget.item != null) {
      // bool success = await customerController.postUpdate(
      //     widget.item['CustomerId'],
      //     nameController.text,
      //     phoneController.text,
      //     dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth!) : null,
      //     gender,
      //     addressController.text
      // );
      // if (success) {
      //   Get.back(result: success);
      // }
    }
    else {
      // int customerId = await customerController.postCreate(
      //     nameController.text,
      //     phoneController.text,
      //     dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth!) : null,
      //     gender,
      //     addressController.text
      // );
      // if (customerId != 0) {
      //   Get.back(result: customerId);
      // }
    }
    AppFunction.hideLoading();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: widget.item != null ? 'Chỉnh sửa nhân viên' : 'Thêm nhân viên'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            CustomTextField(
              controller: nameController,
              label: 'Tên nhân viên',
              required: true,
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Số điện thoại',
              required: true,
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Email',
            ),
            CustomTextField(
              controller: addressController,
              label: 'Địa chỉ',
              maxLines: 3,
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Nhóm người dùng',
              readOnly: true,
              required: true,
              isSelect: true,
              onTap: () {
                pickUserGroup();
              },
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Tên đăng nhập',
              required: true,
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Mật khẩu',
              required: true,
            ),
          ],
        ),
      ),
    );
  }
}
