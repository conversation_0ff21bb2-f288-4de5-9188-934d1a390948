class ACBQRResponseEntity {
  ACBQRResponseEntity({
    required this.success,
    required this.code,
    required this.message,
    required this.result,
  });

  final bool success;
  final int code;
  final String message;
  final ACBQRResultEntity? result;

}

class ACBQRResultEntity {
  ACBQRResultEntity({
    required this.date,
    required this.qrPay,
  });

  final DateTime? date;
  final String qrPay;

}

