import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:icons_plus/icons_plus.dart';

import '../../../../core/controllers/language_controller.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_images.dart';
import '../../../../core/theme/app_style.dart';
import '../../../../core/utils/toast_info.dart';
import '../../../../general/views/auth/config_printer_view.dart';
import '../../../domain/entities/sale_common/areas_reponse_entity.dart';
import '../../../domain/entities/sale_common/branches_response_entity.dart';
import '../../../domain/entities/sale_common/counters_response_entity.dart';
import '../../../domain/entities/sale_common/tables_reponse_entity.dart';
import '../sale_common/areas_controller.dart';
import '../sale_common/branches_controller.dart';
import '../sale_common/counters_controller.dart';
import '../sale_common/tables_controller.dart';
import 'auth_controller.dart';

class LoginPage extends StatelessWidget {
  LoginPage({super.key});

  final AuthSOController _authController = Get.find<AuthSOController>();
  final LanguageController _languageController = Get.find<LanguageController>();
  final BranchesController _branchesController = Get.find<BranchesController>();
  final CounterController _counterController = Get.find<CounterController>();
  final AreasController _areasController = Get.find<AreasController>();
  final TablesController _tablesController = Get.find<TablesController>();

  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _orgIdController = TextEditingController(text: '3');

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: width * 0.05, vertical: width * 0.015),
                child: Column(
                  children: [
                    // Logo
                    Align(
                      alignment: Alignment.topCenter,
                      child: Container(
                        width: width * 0.45,
                        margin: EdgeInsets.only(
                          top: MediaQuery.of(context).viewPadding.top,
                          bottom: width * 0.015,
                        ),
                        child: Image.asset(AppImages.logo, fit: BoxFit.fitWidth),
                      ),
                    ),
                    _loginText(context, width),
                    _loginMiniText(context, width),
                    SizedBox(height: width * 0.01),
                    _usernameField(context, width),
                    SizedBox(height: width * 0.01),
                    _passwordField(context, width),
                    SizedBox(height: width * 0.01),
                    _branch(width),
                    SizedBox(height: width * 0.01),
                    _counter(width),
                    SizedBox(height: width * 0.01),
                    _area(width),
                    SizedBox(height: width * 0.01),
                    _table(width),
                    SizedBox(height: width * 0.03),
                    _buildRememberMe(context, width),
                    SizedBox(height: width * 0.015),
                    _loginButton(context, width),
                    SizedBox(height: width * 0.015),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () {
                            Get.to(() => ConfigPrinterView());
                          },
                          child: Container(
                            width: 45,
                            height: 45,
                            margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
                            decoration: BoxDecoration(
                                color: AppColors.shadow,
                                borderRadius: BorderRadius.circular(10)
                            ),
                            child: Icon(Icons.settings, color: Colors.white,),
                          ),
                        )
                      ],
                    ),
                    SizedBox(height: width * 0.03),
                    _buildLanguage(width),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: width * 0.01),
              child: _buildVersion(width),
            ),
          ],
        ),
      )
    );
  }

  Widget _loginText(BuildContext context, double width) {
    return Text(
      'login'.tr,
      style: PrimaryFont.bold.copyWith(fontSize: width * 0.05, color: AppColors.text),
    );
  }

  Widget _loginMiniText(BuildContext context, double width) {
    return Text(
      'welcome_message'.tr,
      style: PrimaryFont.regular.copyWith(fontSize: width * 0.03, color: AppColors.text),
    );
  }

  Widget _usernameField(BuildContext context, double width) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'username'.tr,
          style: PrimaryFont.bold.copyWith(color: AppColors.text, fontSize: width * 0.03),
        ),
        SizedBox(height: width * 0.01),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(width * 0.012),
          ),
          child: TextField(
            controller: _usernameController,
            decoration: InputDecoration(
              hintText: 'username'.tr,
              hintStyle: PrimaryFont.regular.copyWith(
                color: AppColors.shadow,
                fontSize: width * 0.03,
              ),
              filled: true,
              fillColor: AppColors.background,
              contentPadding: EdgeInsets.symmetric(
                horizontal: width * 0.03,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(width * 0.015),
                borderSide: BorderSide(color: AppColors.text, width: 2.0),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(width * 0.015),
                borderSide: BorderSide(color: AppColors.text, width: 1.0),
              ),
            ),
            style: PrimaryFont.regular.copyWith(
              color: AppColors.text,
              fontSize: width * 0.03,
            ),
          ),
        ),
      ],
    );
  }

  Widget _passwordField(BuildContext context, double width) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'password'.tr,
            style: PrimaryFont.bold.copyWith(fontSize: width * 0.03, color: AppColors.text),
          ),
          SizedBox(height: width * 0.01),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(width * 0.012),
            ),
            child: TextField(
              controller: _passwordController,
              obscureText: _authController.obscurePassword.value,
              decoration: InputDecoration(
                hintText: 'password'.tr,
                hintStyle: PrimaryFont.regular.copyWith(
                  color: AppColors.shadow,
                  fontSize: width * 0.03,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.012),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppColors.background,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: width * 0.03,
                ),
                suffixIcon: IconButton(
                  icon: Icon(
                    _authController.obscurePassword.value
                        ? Icons.visibility_off
                        : Icons.visibility,
                    color: AppColors.text,
                  ),
                  onPressed: () => _authController.togglePasswordVisibility(),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 2.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 1.0),
                ),

              ),
              style: PrimaryFont.regular.copyWith(
                color: AppColors.text,
                fontSize: width * 0.03,
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _branch(double width) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'branches'.tr,
            style: PrimaryFont.bold.copyWith(
              color: AppColors.text,
              fontSize: width * 0.03,
            ),
          ),
          SizedBox(height: width * 0.01),
          DropdownSearch<BranchesResponseEntity>(
            popupProps: PopupProps.dialog(
              showSearchBox: true,
              searchFieldProps: TextFieldProps(
                decoration: InputDecoration(
                  hintText: 'search'.tr,
                  prefixIcon: Icon(Icons.search, color: AppColors.text),
                  hintStyle: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 1.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 2.0),
                  ),
                ),
                style: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.text,
                ),
              ),
              emptyBuilder: (context, searchEntry) => Center(
                child: Text(
                  'no_data_found'.tr,
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                ),
              ),
              dialogProps: DialogProps(
                backgroundColor: AppColors.background,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                ),
              ),
              itemBuilder: (context, item, isSelected) => Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.015),
                child: Text(
                  item.branchName ?? 'Unnamed Branch',
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.text,
                  ),
                ),
              ),
            ),

            items: _branchesController.branchList,
            dropdownDecoratorProps: DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                hintText: 'select_branch'.tr,
                hintStyle: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.text,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 1.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 2.0),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: width * 0.03,
                ),
              ),
            ),
            itemAsString: (BranchesResponseEntity item) => item.branchName ?? '',
            selectedItem: _branchesController.selectedBranch.value,
            onChanged: (value) {
              _branchesController.selectedBranch.value = value;
            },
          ),
        ],
      );
    });
  }

  Widget _counter(double width) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'counter'.tr,
            style: PrimaryFont.bold.copyWith(
              color: AppColors.text,
              fontSize: width * 0.03,
            ),
          ),
          SizedBox(height: width * 0.01),
          DropdownSearch<CounterResponseEntity>(
            popupProps: PopupProps.dialog(
              showSearchBox: true,
              searchFieldProps: TextFieldProps(
                decoration: InputDecoration(
                  hintText: 'search'.tr,
                  prefixIcon: Icon(Icons.search, color: AppColors.text),
                  hintStyle: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 1.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 2.0),
                  ),
                ),
              ),
              emptyBuilder: (context, searchEntry) => Center(
                child: Text(
                  'no_data_found'.tr,
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                ),
              ),
              dialogProps: DialogProps(
                backgroundColor: AppColors.background,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                ),
              ),
              itemBuilder: (context, item, isSelected) => Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.015),
                child: Text(
                  item.counterName ?? '',
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.text,
                  ),
                ),
              ),
            ),
            items: _counterController.counters,
            selectedItem: _counterController.selectedCounter.value,
            itemAsString: (item) => item.counterName ?? '',
            onChanged: (value) {
              _counterController.selectedCounter.value = value;
            },
            dropdownDecoratorProps: DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                hintText: 'select_counter'.tr,
                hintStyle: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.text,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 1.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 2.0),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: width * 0.03),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _area(double width) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'area'.tr,
            style: PrimaryFont.bold.copyWith(
              color: AppColors.text,
              fontSize: width * 0.03,
            ),
          ),
          SizedBox(height: width * 0.01),
          DropdownSearch<AreasResponseEntity>(
            popupProps: PopupProps.dialog(
              showSearchBox: true,
              searchFieldProps: TextFieldProps(
                decoration: InputDecoration(
                  hintText: 'search'.tr,
                  prefixIcon: Icon(Icons.search, color: AppColors.text),
                  hintStyle: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 1.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 2.0),
                  ),
                ),
              ),
              emptyBuilder: (context, searchEntry) => Center(
                child: Text(
                  'no_data_found'.tr,
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                ),
              ),
              dialogProps: DialogProps(
                backgroundColor: AppColors.background,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                ),
              ),
              itemBuilder: (context, item, isSelected) => Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.015),
                child: Text(
                  item.areaName ?? '',
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.text,
                  ),
                ),
              ),
            ),
            items: _areasController.areas,
            selectedItem: _areasController.selectedArea.value,
            itemAsString: (item) => item.areaName ?? '',
            onChanged: (value) {
              _areasController.selectedArea.value = value;
            },
            dropdownDecoratorProps: DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                hintText: 'select_area'.tr,
                hintStyle: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.text,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 1.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 2.0),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: width * 0.03),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _table(double width) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'table'.tr,
            style: PrimaryFont.bold.copyWith(
              color: AppColors.text,
              fontSize: width * 0.03,
            ),
          ),
          SizedBox(height: width * 0.01),
          DropdownSearch<TablesResponseEntity>(
            popupProps: PopupProps.dialog(
              showSearchBox: true,
              searchFieldProps: TextFieldProps(
                decoration: InputDecoration(
                  hintText: 'search'.tr,
                  prefixIcon: Icon(Icons.search, color: AppColors.text, size: width * 0.05,),
                  hintStyle: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 1.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.015),
                    borderSide: BorderSide(color: AppColors.text, width: 2.0),
                  ),
                ),
              ),
              emptyBuilder: (context, searchEntry) => Center(
                child: Text(
                  'no_data_found'.tr,
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.shadow,
                  ),
                ),
              ),
              dialogProps: DialogProps(
                backgroundColor: AppColors.background,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                ),
              ),
              itemBuilder: (context, item, isSelected) => Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.015),
                child: Text(
                  item.tableNo ?? '',
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.03,
                    color: AppColors.text,
                  ),
                ),
              ),
            ),
            items: _tablesController.tables,
            selectedItem: _tablesController.selectedTable.value,
            itemAsString: (item) => item.tableNo ?? '',
            onChanged: (value) {
              _tablesController.selectedTable.value = value;
            },
            dropdownDecoratorProps: DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                hintText: 'select_table'.tr,
                hintStyle: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.03,
                  color: AppColors.text,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 1.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(width * 0.015),
                  borderSide: BorderSide(color: AppColors.text, width: 2.0),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: width * 0.03),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildRememberMe(BuildContext context, double width) {
    return Obx(() {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              InkWell(
                onTap: () => _authController.toggleRememberMe(),
                child: Container(
                  width: width * 0.05,
                  height: width * 0.05,
                  decoration: BoxDecoration(
                    color: _authController.rememberMe.value
                        ? AppColors.text
                        : Colors.transparent,
                    border: Border.all(width: 2, color: AppColors.text),
                    borderRadius: BorderRadius.circular(width * 0.015),
                  ),
                  child: _authController.rememberMe.value
                      ? Icon(
                    Icons.check,
                    size: width * 0.04,
                    color: AppColors.background,
                  )
                      : null,
                ),
              ),
              SizedBox(width: width * 0.02),
              Text(
                'remember_me'.tr,
                style: PrimaryFont.regular.copyWith(
                  fontSize: width * 0.035,
                  color: AppColors.text,
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  Widget _loginButton(BuildContext context, double width) {
    return SizedBox(
      width: double.infinity,
      height: width * 0.12,
      child: Obx(() {
        return ElevatedButton(
          onPressed: _authController.isLoading.value
              ? null
              : () async {
            if (_usernameController.text.isEmpty) {
              toastInfo(msg:'message_username'.tr, width: width);
              return;
            }
            if (_passwordController.text.isEmpty) {
              toastInfo(msg: 'message_password'.tr, width: width);
              return;
            }

            await _authController.login(
              _usernameController.text,
              _passwordController.text,
              int.tryParse(_orgIdController.text) ?? 3,
              width,
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.button,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(width * 0.15),
            ),
          ),
          child: _authController.isLoading.value
              ? CircularProgressIndicator(
            color: AppColors.background,
          )
              : Text(
            'login_button'.tr,
            style: PrimaryFont.bold.copyWith(
              color: AppColors.background,
              fontSize: width * 0.045,
            ),
          ),
        );
      }),
    );
  }

  Widget _buildLanguage(double width) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _languageButton('vi', Flag(Flags.vietnam), width),
        SizedBox(width: width * 0.05),
        _languageButton('en', Flag(Flags.united_kingdom), width),
      ],
    );
  }

  Widget _languageButton(String langCode, Widget flagWidget, double width) {
    return Obx(() {
      final isSelected = _languageController.currentLocale.value.languageCode == langCode;

      return GestureDetector(
        onTap: () {
          _languageController.changeLanguage(langCode);
        },
        child: Container(
          width: width * 0.12,
          height: width * 0.08,
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.text : Colors.transparent,
              width: width * 0.005,
            ),
            borderRadius: BorderRadius.circular(0),
          ),
          clipBehavior: Clip.hardEdge,
          child: FittedBox(
            fit: BoxFit.cover,
            child: flagWidget,
          ),
        ),
      );
    });
  }

  Widget _buildVersion(double width) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 5, 0, 0),
      child: Align(
        alignment: Alignment.topCenter,
        child: Text(
          'v1.0.0',
          style: PrimaryFont.regular.copyWith(
            fontSize: width * 0.035,
            color: AppColors.text,
          ),
        ),
      ),
    );
  }
}