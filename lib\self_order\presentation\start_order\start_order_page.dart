import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/controllers/language_controller.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_images.dart';
import '../../../core/theme/app_style.dart';

class StartOrderPage extends StatelessWidget {
  StartOrderPage({super.key});

  final LanguageController _languageController = Get.find<LanguageController>();
  Future<void> _saveOrderType(String type) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('order_type', type);
    print('Order type: ${prefs.getString('order_type')}');
  }

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;

    return Scaffold(
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    AppImages.logo,
                    width: width * 0.65,
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Text(
                    'start_order'.tr,
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.055,
                    ),
                  ),
                  SizedBox(height: width * 0.05),
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // Dine in
                        GestureDetector(
                          onTap: () async {
                            await _saveOrderType('DineIn');
                            Get.toNamed('/e-menu-page');
                          },
                          child: Container(
                            height: width * 0.65,
                            width: width * 0.4,
                            margin: EdgeInsets.all(width * 0.01),
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius:
                                  BorderRadius.circular(width * 0.015),
                            ),
                            child: Container(
                              margin: EdgeInsets.all(width * 0.015),
                              decoration: BoxDecoration(
                                color: AppColors.background,
                                borderRadius:
                                    BorderRadius.circular(width * 0.015),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    flex: 3,
                                    child: Icon(
                                        FluentIcons.drink_margarita_20_filled,
                                        size: width * 0.2,
                                        color: AppColors.primary
                                    ),
                                  ),
                                  SizedBox(height: width * 0.015),
                                  Expanded(
                                    flex: 1,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(horizontal: width * 0.025, vertical: width * 0.025),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(width * 0.015),
                                        color: AppColors.primary,
                                      ),
                                      margin: EdgeInsets.only(right: width * 0.015, left: width * 0.015, bottom: width * 0.015),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'dine_in'.tr,
                                            style: PrimaryFont.bold.copyWith(
                                              color: AppColors.background,
                                              fontSize: width * 0.05,
                                            ),
                                          ),
                                          Icon(Icons.chevron_right,
                                              color: AppColors.background,
                                              size: width * 0.05),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        // Take away
                        GestureDetector(
                          onTap: () async {
                            await _saveOrderType('TakeAway');
                            Get.toNamed('/e-menu-page');
                          },
                          child: Container(
                            height: width * 0.65,
                            width: width * 0.4,
                            margin: EdgeInsets.all(width * 0.01),
                            decoration: BoxDecoration(
                              color: AppColors.button,
                              borderRadius:
                                  BorderRadius.circular(width * 0.015),
                            ),
                            child: Container(
                              margin: EdgeInsets.all(width * 0.015),
                              decoration: BoxDecoration(
                                color: AppColors.background,
                                borderRadius:
                                    BorderRadius.circular(width * 0.015),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                      flex: 3,
                                      child: Icon(
                                          FluentIcons.box_24_regular,
                                          size: width * 0.2,
                                          color: AppColors.button
                                      ),
                                  ),
                                  SizedBox(height: width * 0.015),
                                  Expanded(
                                    flex: 1,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(horizontal: width * 0.025, vertical: width * 0.025),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(width * 0.015),
                                        color: AppColors.button,
                                      ),
                                      margin: EdgeInsets.only(right: width * 0.015, left: width * 0.015, bottom: width * 0.015),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'take_away'.tr,
                                            style: PrimaryFont.bold.copyWith(
                                              color: AppColors.background,
                                              fontSize: width * 0.05,
                                            ),
                                          ),
                                          Icon(Icons.chevron_right,
                                              color: AppColors.background,
                                              size: width * 0.05),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(bottom: width * 0.05),
                    child: _buildLanguage(width),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildLanguage(double width) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _languageButton('vi', Flag(Flags.vietnam), width),
        SizedBox(width: width * 0.05),
        _languageButton('en', Flag(Flags.united_kingdom), width),
      ],
    );
  }

  Widget _languageButton(String langCode, Widget flagWidget, double width) {
    return Obx(() {
      final isSelected = _languageController.currentLocale.value.languageCode == langCode;

      return GestureDetector(
        onTap: () {
          _languageController.changeLanguage(langCode);
        },
        child: Container(
          width: width * 0.12,
          height: width * 0.08,
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.text : Colors.transparent,
              width: width * 0.005,
            ),
            borderRadius: BorderRadius.circular(0),
          ),
          clipBehavior: Clip.hardEdge,
          child: FittedBox(
            fit: BoxFit.cover,
            child: flagWidget,
          ),
        ),
      );
    });
  }

}

// Future<String?> getOrderType() async {
//   final prefs = await SharedPreferences.getInstance();
//   return prefs.getString('order_type');
// }
