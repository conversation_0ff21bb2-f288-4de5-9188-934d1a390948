import 'package:get/get.dart';

import '../../data/datasources/order/order_remote_data_source.dart';
import '../../data/repositories/order/order_repository_impl.dart';
import '../../domain/usecases/order/generate_zalo_usecase.dart';
import '../../domain/usecases/order/order_usecase.dart';
import '../../domain/usecases/order/status_usecase.dart';
import 'check_out_controller.dart';

class CheckoutBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() {
      final orderRepository = OrderRepositoryImpl(
        remoteDataSource: OrderRemoteDataSourceImpl(
          dioClient: Get.find(),
        ),
      );
      return CheckoutController(
        createOrderUseCase: CreateOrderUseCase(
          repository: orderRepository,
        ),
        generateZALOQRUseCase: GenerateZALOQRUseCase(repository: orderRepository),
        updateStatusUseCase: UpdateStatusUseCase(repository: orderRepository),
      );
    });
  }
}