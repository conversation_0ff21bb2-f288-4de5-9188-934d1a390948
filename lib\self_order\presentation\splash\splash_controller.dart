import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkLoginStatus();
    });
  }

  Future<void> _checkLoginStatus() async {

    final prefs = Get.find<SharedPreferences>();
    final token = prefs.getString('token_so');
    final rememberMe = prefs.getBool('rememberMe') ?? false;

    if (token != null && token.isNotEmpty && rememberMe) {
      Get.offAllNamed('/banner-page');
    } else {
      Get.offAllNamed('/login-page');
    }
  }
}
