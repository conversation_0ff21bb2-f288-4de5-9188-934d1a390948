// Dashboard
class OverviewRevenueEntity {
  OverviewRevenueEntity({
    required this.paymentAmount,
    required this.itemAmount,
    required this.discountAmount,
    required this.totalBill,
    required this.vatAmount,
    required this.totalInvoice,
  });

  final double? paymentAmount;
  final double? itemAmount;
  final double? discountAmount;
  final double? totalBill;
  final double? vatAmount;
  final double? totalInvoice;
}

class TopBottomBranchesRevenueEntity {
  final List<BranchesRevenueEntity> topBranchesRevenue;
  final List<BranchesRevenueEntity> bottomBranchesRevenue;
  TopBottomBranchesRevenueEntity({
    required this.topBranchesRevenue,
    required this.bottomBranchesRevenue,
  });

}

class BranchesRevenueEntity {
  final int? branchId;
  final String? branchName;
  final double? paymentAmount;
  final int? paidQuantity;
  final double? itemAmount;
  final int? discountAmount;
  final int? vatAmount;
  final int? svcAmount;
  final int? canceledAmount;
  final int? customerQty;
  final int? unPaidAmount;
  final double? focAmount;
  final double? avgPayment;
  final String? orderBranch;

  BranchesRevenueEntity({
    required this.branchId,
    required this.branchName,
    required this.paymentAmount,
    required this.paidQuantity,
    required this.itemAmount,
    required this.discountAmount,
    required this.vatAmount,
    required this.svcAmount,
    required this.canceledAmount,
    required this.customerQty,
    required this.unPaidAmount,
    required this.focAmount,
    required this.avgPayment,
    required this.orderBranch,
  });

}

class Revenue7DaysEntity {
  final String rDay;
  final int paymentAmount;

  const Revenue7DaysEntity({
    required this.rDay,
    required this.paymentAmount,
  });

}

class TopBranchesRevenueByDateEntity {
  final DateTime? revenueDate;
  final Map<String, double> revenues;

  TopBranchesRevenueByDateEntity({
    required this.revenueDate,
    required this.revenues,
  });

}


// By Item
class TopAndBottomItemsRevenueEntity {
  TopAndBottomItemsRevenueEntity({
    required this.topItemsByTotalQuantity,
    required this.topItemsByTotalAmount,
    required this.bottomItems,
  });

  final List<ItemEntity> topItemsByTotalQuantity;
  final List<ItemEntity> topItemsByTotalAmount;
  final List<ItemEntity> bottomItems;
}
class ItemEntity {
  ItemEntity({
    required this.itemName,
    required this.totalQuantity,
    required this.totalAmount,
  });

  final String? itemName;
  final double? totalQuantity;
  final double? totalAmount;
}

class TopItemsRevenueByDateEntity {
  final DateTime? revenueDate;
  final Map<String, double> revenues;

  TopItemsRevenueByDateEntity({
    required this.revenueDate,
    required this.revenues,
  });

}

// By Category
class TopAndBottomCategoriesByAmountEntity {
  TopAndBottomCategoriesByAmountEntity({
    required this.topCategoriesByQuantity,
    required this.topCategoriesByAmount,
    required this.bottomCategoriesByAmount,
  });

  final List<CategoriesByAmountEntity> topCategoriesByQuantity;
  final List<CategoriesByAmountEntity> topCategoriesByAmount;
  final List<CategoriesByAmountEntity> bottomCategoriesByAmount;
}

class CategoriesByAmountEntity {
  CategoriesByAmountEntity({
    required this.groupName,
    required this.totalQuantity,
    required this.totalAmount,
  });

  final String? groupName;
  final double? totalQuantity;
  final double? totalAmount;
}

class TopCatsRevenueByDateEntity {
  final DateTime? revenueDate;
  final Map<String, double> revenues;

  TopCatsRevenueByDateEntity({
    required this.revenueDate,
    required this.revenues,
  });

}

// By Payment
class TotalPaymentAmountEntity {
  TotalPaymentAmountEntity({
    required this.payName,
    required this.paymentAmount,
    required this.percent,
  });

  final String? payName;
  final double? paymentAmount;
  final double? percent;
}

class TotalPaymentAmountByDateEntity {
  final DateTime? paymentDate;
  final Map<String, double> payments;

  TotalPaymentAmountByDateEntity({
    required this.paymentDate,
    required this.payments,
  });
}