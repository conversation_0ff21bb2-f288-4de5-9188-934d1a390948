class BranchesListResponseModel {
  BranchesListResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.branches,
    required this.errorDetail,
  });

  final bool? success;
  final int? code;
  final String? message;
  final List<BranchesResponseModel> branches;
  final dynamic errorDetail;

  factory BranchesListResponseModel.fromJson(Map<String, dynamic> json) {
    return BranchesListResponseModel(
      success: json["Success"],
      code: json["Code"],
      message: json["Message"],
      branches: json["Result"] == null
          ? []
          : List<BranchesResponseModel>.from(
          json["Result"].map((x) => BranchesResponseModel.fromJson(x))),
      errorDetail: json["ErrorDetail"],
    );
  }
}


class BranchesResponseModel {
  final int? branchId;
  final String? branchName;
  final String? branchNo;
  final int? branchParentId;

  BranchesResponseModel({
    required this.branchId,
    required this.branchName,
    required this.branchNo,
    required this.branchParentId,
  });

  factory BranchesResponseModel.fromJson(Map<String, dynamic> json){
    return BranchesResponseModel(
      branchId: json["BranchId"],
      branchName: json["BranchName"],
      branchNo: json["BranchNo"],
      branchParentId: json["BranchParentId"],
    );
  }

}