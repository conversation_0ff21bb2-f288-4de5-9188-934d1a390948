import '../../../domain/entities/sale_common/areas_reponse_entity.dart';
import '../../../domain/repositories/sale_common/areas_repository.dart';
import '../../datasources/sale_common/areas_remote_data_source.dart';

class AreasRepositoryImpl implements AreasRepository {
  final AreasRemoteDataSource remoteDataSource;

  AreasRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<AreasResponseEntity>> getAreas(int branchId, int counterId) async {
    final models = await remoteDataSource.getAreas(branchId, counterId);
    return models
        .map((e) => AreasResponseEntity(
        areaId: e.areaId,
        areaName: e.areaName,
    )).toList();
  }
}
