import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_header_modal.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';
import 'package:gls_self_order/general/views/manufacture/manufacture_create_or_update_view.dart';
import 'package:gls_self_order/general/views/product/product_create_or_update_view.dart';
import 'package:intl/intl.dart';

class ExportWarehouseCreateOrUpdateView extends StatefulWidget {
  const ExportWarehouseCreateOrUpdateView({super.key});

  @override
  State<ExportWarehouseCreateOrUpdateView> createState() => _ExportWarehouseCreateOrUpdateViewState();
}

class _ExportWarehouseCreateOrUpdateViewState extends State<ExportWarehouseCreateOrUpdateView> {
  //variable
  int manufactureSelected = 0;
  int warehouseSelected = 0;
  ProductController productController = Get.find<ProductController>();
  List productWithCategories = [];
  final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getData();
  }

  getData() async {
    await getProductWithCategories();
  }

  getProductWithCategories() async {
    productWithCategories = await productController.getProductWithCategories();
  }

  pickManufacture() async {
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        useSafeArea: true,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, StateSetter setStateIn) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                child: Column(
                  children: [
                    CustomHeaderModal(
                      title: 'Chọn nhà cung cấp',
                      append: InkWell(
                        onTap: () async {
                          Get.to(() => ManufactureCreateOrUpdateView());
                        },
                        child: Icon(Icons.add, color: Colors.white, size: 30,),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(10),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: TextEditingController(),
                              showLabel: false,
                              hint: 'Tìm kiếm nhà cung cấp',
                              space: false,
                            ),
                          ),
                          InkWell(
                            onTap: () async {

                            },
                            child: Container(
                              width: 45,
                              height: 45,
                              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
                              decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(10)
                              ),
                              child: Icon(Icons.search, color: Colors.white,),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          InkWell(
                            onTap: () {
                              manufactureSelected = 1;
                              setState(() {

                              });
                              Get.back();
                            },
                            child:  Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.05),
                                      spreadRadius: 0,
                                      blurRadius: 1,
                                      offset: Offset(0, 3)
                                  ),
                                ],
                              ),
                              padding: EdgeInsets.all(10),
                              margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      // Container(
                                      //   width: 50,
                                      //   height: 50,
                                      //   decoration: BoxDecoration(
                                      //     borderRadius: BorderRadius.circular(180)
                                      //   ),
                                      //   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                      //   clipBehavior: Clip.antiAlias,
                                      //   child: Image.asset('assets/images/general/avatar.jpg'),
                                      // ),
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                Expanded(child: CustomText(text: 'Công ty thực phẩm Miền Nam', bold: true, size: 18,),),
                                              ],
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                CustomText(text: '📞0913789789'),
                                              ],
                                            )
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          );
        }
    );
  }

  pickWarehouse() async {
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        useSafeArea: true,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, StateSetter setStateIn) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                child: Column(
                  children: [
                    CustomHeaderModal(
                      title: 'Chọn kho',
                      append: InkWell(
                        onTap: () async {
                          Get.to(() => ManufactureCreateOrUpdateView());
                        },
                        child: Icon(Icons.add, color: Colors.white, size: 30,),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(10),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: TextEditingController(),
                              showLabel: false,
                              hint: 'Tìm kiếm kho',
                              space: false,
                            ),
                          ),
                          InkWell(
                            onTap: () async {

                            },
                            child: Container(
                              width: 45,
                              height: 45,
                              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
                              decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(10)
                              ),
                              child: Icon(Icons.search, color: Colors.white,),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          InkWell(
                            onTap: () {
                              warehouseSelected = 1;
                              setState(() {

                              });
                              Get.back();
                            },
                            child:  Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.05),
                                      spreadRadius: 0,
                                      blurRadius: 1,
                                      offset: Offset(0, 3)
                                  ),
                                ],
                              ),
                              padding: EdgeInsets.all(10),
                              margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      // Container(
                                      //   width: 50,
                                      //   height: 50,
                                      //   decoration: BoxDecoration(
                                      //     borderRadius: BorderRadius.circular(180)
                                      //   ),
                                      //   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                      //   clipBehavior: Clip.antiAlias,
                                      //   child: Image.asset('assets/images/general/avatar.jpg'),
                                      // ),
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                Expanded(child: CustomText(text: 'Kho Hoàng Diệu', bold: true, size: 18,),),
                                              ],
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                CustomText(text: '22 Hoàng Diệu'),
                                              ],
                                            )
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          );
        }
    );
  }

  pickProduct() async {
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        useSafeArea: true,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, StateSetter setStateIn) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                child: Column(
                  children: [
                    CustomHeaderModal(
                      title: 'Chọn sản phẩm',
                      append: InkWell(
                        onTap: () async {
                          Get.to(() => ProductCreateOrUpdateView());
                        },
                        child: Icon(Icons.add, color: Colors.white, size: 30,),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(10),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: TextEditingController(),
                              showLabel: false,
                              hint: 'Tìm kiếm sản phẩm',
                              space: false,
                            ),
                          ),
                          InkWell(
                            onTap: () async {

                            },
                            child: Container(
                              width: 45,
                              height: 45,
                              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
                              decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(10)
                              ),
                              child: Icon(Icons.search, color: Colors.white,),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          for (dynamic itemCate in productWithCategories)
                            Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                      border: Border(
                                          bottom: BorderSide(
                                              width: 1,
                                              color: AppColors.shadow.withValues(alpha: 0.2)
                                          )
                                      )
                                  ),
                                  margin: EdgeInsets.fromLTRB(0, 0, 0, 5),
                                  child: CustomText(text: itemCate['ItemGroupName'], bold: true, size: 18,),
                                ),
                                for (dynamic itemProd in itemCate['Items'])
                                  InkWell(
                                    onTap: () {
                                      Get.back();
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: AppColors.primary.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(10),
                                        boxShadow: [
                                          BoxShadow(
                                              color: Colors.grey.withValues(alpha: 0.05),
                                              spreadRadius: 0,
                                              blurRadius: 1,
                                              offset: Offset(0, 3)
                                          ),
                                        ],
                                      ),
                                      clipBehavior: Clip.antiAlias,
                                      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            width: 100,
                                            height: 100,
                                            child: itemProd['Images'].isNotEmpty
                                                ? Image.network(SmeUrl.baseImageUrl + itemProd['Images'][itemProd['Images'].length - 1]['MediaUrl'], fit: BoxFit.cover,
                                              errorBuilder: (context, _, __){
                                                return Image.asset('assets/images/general/no_image.jpg', fit: BoxFit.cover,);
                                              },
                                              loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                                                if (loadingProgress == null) return child;
                                                return Center(
                                                    child: CircularProgressIndicator(color: AppColors.primary,)
                                                );
                                              },
                                            )
                                                : Image.asset('assets/images/general/no_image.jpg'),
                                          ),
                                          Expanded(
                                            child: Padding(
                                              padding: EdgeInsets.fromLTRB(10, 2.5, 10, 2.5),
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  CustomText(text: itemProd['ItemName'], bold: true,),
                                                  CustomText(text: itemCate['ItemGroupName'], italic: true,),
                                                  CustomText(text: 'Giá: ${currencyFormat.format(itemProd['Price'])}/${itemProd['UnitName']}'),
                                                  CustomText(text: itemProd['AvailableStatus'] == 'AVAILABLE' ? 'Còn hàng' : 'Hết hàng', bold: true, color: itemProd['AvailableStatus'] == 'AVAILABLE' ? AppColors.button : AppColors.danger,),
                                                ],
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  )
                              ],
                            )
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          );
        }
    );
  }

  save() async {

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Tạo phiếu xuất'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.05),
                      spreadRadius: 0,
                      blurRadius: 1,
                      offset: Offset(0, 3)
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: TextEditingController(),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                            filled: true,
                            fillColor: Colors.white,
                            enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                    width: 0.5, color: Colors.black.withValues(alpha: 0.2)),
                                borderRadius: BorderRadius.circular(7.5)),
                            focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                    width: 0.5, color: Colors.blueAccent.withValues(alpha: 0.8)),
                                borderRadius: BorderRadius.circular(7.5)),
                            hintText: 'Chọn sản phẩm...',
                            isDense: true,
                            prefixIcon: Icon(Icons.add, size: 25,)
                          ),
                          readOnly: true,
                          onTap: () {
                            pickProduct();
                          },
                        ),
                      ),
                      Container(
                        width: 50,
                        height: 50,
                        margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                        decoration: BoxDecoration(
                          color: AppColors.shadow.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10)
                        ),
                        child: Center(
                          child: Icon(Icons.document_scanner_outlined),
                        )
                      )
                    ],
                  ),
                  SizedBox(height: 10,),
                  InkWell(
                    onTap: () {
                      pickWarehouse();
                    },
                    child: warehouseSelected == 0
                        ? CustomText(text: '+ Chọn kho', bold: true, size: 18, color: AppColors.primary,)
                        : Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(Icons.warehouse, color: AppColors.primary,),
                        Expanded(
                          child: CustomText(text: ' Kho Huỳnh Tấn Phát', bold: true, size: 18, color: AppColors.primary,),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 10,),
                  InkWell(
                    onTap: () {
                      pickManufacture();
                    },
                    child: manufactureSelected == 0
                        ? CustomText(text: '+ Chọn khách hàng', bold: true, size: 18, color: AppColors.primary,)
                        : Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(Icons.person, color: AppColors.primary,),
                        Expanded(
                          child: CustomText(text: ' Anh Dũng CT', bold: true, size: 18, color: AppColors.primary,),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(10)
                    ),
                    padding: EdgeInsets.all(10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 65,
                          height: 65,
                          margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10)
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: Image.asset('assets/images/self_order/product_1.jpg', fit: BoxFit.cover,),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(text: 'Matcha latte', bold: true,),
                              CustomText(text: 'SP00015', size: 13,),
                              CustomText(text: 'Tồn kho: 15 - Giá: 40.000 ', size: 13,),
                              SizedBox(height: 5,),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    width: 130,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5),
                                      border: Border.all(
                                        width: 1,
                                        color: AppColors.shadow.withValues(alpha: 0.5)
                                      )
                                    ),
                                    padding: EdgeInsets.fromLTRB(5, 2.5, 5, 2.5),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Icon(Icons.remove),
                                        CustomText(text: '4'),
                                        Icon(Icons.add)
                                      ],
                                    ),
                                  ),
                                  CustomText(text: '160.000'),
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
