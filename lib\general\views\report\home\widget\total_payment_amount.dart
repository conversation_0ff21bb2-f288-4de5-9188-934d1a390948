import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/helper/format_helper.dart';
import '../../domain/report/entities/report.dart';

class TotalPaymentAmount extends StatelessWidget {
  final double width;
  final String title;
  final List<TotalPaymentAmountEntity> totalPaymentAmount;

  const TotalPaymentAmount({
    super.key,
    required this.width,
    required this.title,
    required this.totalPaymentAmount,
  });

  @override
  Widget build(BuildContext context) {
    bool isEmptyData = totalPaymentAmount.isEmpty || totalPaymentAmount.every((e) => (e.paymentAmount ?? 0) == 0);

    List<TotalPaymentAmountEntity> displayData = isEmptyData
        ? [TotalPaymentAmountEntity(payName: "Không có dữ liệu", paymentAmount: 0, percent: 0)]
        : totalPaymentAmount;

    Map<String, double> groupedPayments = {};
    for (var payment in displayData) {
      String paymentMethod = payment.payName ?? "Không xác định";
      double totalAmount = payment.paymentAmount ?? 0;
      groupedPayments[paymentMethod] = (groupedPayments[paymentMethod] ?? 0) + totalAmount;
    }

    List<MapEntry<String, double>> sortedPayments = groupedPayments.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final top10Payments = sortedPayments.take(10).toList();

    final List<Color> colors = [
      Colors.blue, Colors.green, Colors.red, Colors.orange, Colors.purple,
      Colors.teal, Colors.yellow, Colors.pink, Colors.cyan, Colors.brown
    ];

    List<Map<String, dynamic>> data = [];
    bool hasMultilinePayment = false;

    for (int i = 0; i < top10Payments.length; i++) {
      String formattedName = _formatPaymentName(top10Payments[i].key, 10);
      if (formattedName.contains("\n")) {
        hasMultilinePayment = true;
      }

      data.add({
        "paymentMethod": formattedName,
        "totalAmount": top10Payments[i].value.toInt(),
        "color": colors[i % colors.length],
      });
    }

    num maxAmount = data.map((e) => e["totalAmount"]).reduce((a, b) => a > b ? a : b);
    num maxYAxis = maxAmount > 0 ? (maxAmount * 1.2) : 1;

    double extraBottomPadding = hasMultilinePayment ? width * 0.05 : width * 0.015;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Container(
        padding: EdgeInsets.all(width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              title.toUpperCase(),
              style: PrimaryFont.bold.copyWith(
                color: AppColors.text,
                fontSize: width * 0.04,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
            SizedBox(height: width * 0.03),
            SizedBox(
              height: width * 0.65,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Container(
                  padding: EdgeInsets.only(left: width * 0.01, bottom: extraBottomPadding),
                  width: width,
                  child: Chart(
                    data: data,
                    variables: {
                      'paymentMethod': Variable(
                        accessor: (Map map) => map['paymentMethod'] as String,
                        scale: OrdinalScale(),
                      ),
                      'totalAmount': Variable(
                        accessor: (Map map) => map['totalAmount'] as num,
                        scale: LinearScale(
                          min: 0,
                          max: maxYAxis,
                          tickCount: 5,
                          formatter: (num value) => FormatHelper.formatCurrency(value),
                        ),
                      ),
                    },
                    marks: [
                      IntervalMark(
                        position: Varset('paymentMethod') * Varset('totalAmount'),
                        color: ColorEncode(
                          variable: 'paymentMethod',
                          values: data.length < 2
                              ? [Colors.blue, Colors.green, Colors.red, Colors.orange, Colors.purple,
                            Colors.teal, Colors.yellow, Colors.pink, Colors.cyan, Colors.brown]
                              : data.map((e) => e["color"] as Color).toList(),
                        ),

                        label: LabelEncode(
                          encoder: (tuple) => Label(
                            FormatHelper.formatCurrency(tuple['totalAmount'] as num),
                            LabelStyle(
                              textStyle: PrimaryFont.bold.copyWith(
                                fontSize: width * 0.03,
                                color: AppColors.text,
                              ),
                              offset: Offset(0, -10),
                            ),
                          ),
                        ),
                      ),
                    ],
                    axes: [
                      AxisGuide(
                        line: PaintStyle(strokeWidth: 0),
                        label: LabelStyle(
                          textStyle: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          offset: Offset(0, 10),
                        ),
                      ),
                      AxisGuide(
                        label: LabelStyle(
                          textStyle: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          offset: const Offset(-2, 0),
                        ),
                        grid: PaintStyle(
                          strokeColor: AppColors.shadow.withOpacity(0.3),
                          strokeWidth: 1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: width * 0.01),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(top10Payments.length, (i) {
                  return _buildLegendItem(i, top10Payments, colors, width);
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatPaymentName(String name, int maxLength) {
    List<String> words = name.split(' ');
    String newName = '';
    int lineLength = 0;

    for (String word in words) {
      if (lineLength + word.length > maxLength) {
        newName += '\n';
        lineLength = 0;
      }
      newName += (lineLength == 0 ? '' : ' ') + word;
      lineLength += word.length + 1;
    }

    return newName;
  }

  Widget _buildLegendItem(int index, List<MapEntry<String, double>> top10Payments, List<Color> colors, double width) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Row(
        children: [
          Container(
            width: width * 0.03,
            height: width * 0.03,
            color: colors[index % colors.length],
          ),
          SizedBox(width: width * 0.01),
          Text(
            top10Payments[index].key,
            style: PrimaryFont.regular.copyWith(
              fontSize: width * 0.028,
              color: AppColors.text,
            ),
          ),
        ],
      ),
    );
  }
}
