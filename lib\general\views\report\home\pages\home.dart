import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../../core/components/custom_back_button.dart';
import '../../../../../core/components/custom_title_app_bar.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../common/widgets/loading_indicator.dart';
import '../bloc/home_cubit.dart';
import '../bloc/home_state.dart';
import '../widget/7days_revenue.dart';
import '../widget/branch.dart';
import '../widget/over_view_revenue.dart';
import '../widget/top_cats_revenue_by_date.dart';
import '../widget/top_branches_revenue_by_date.dart';
import '../widget/top_categories_by_quantity.dart';
import '../widget/top_item_by_total_quantity.dart';
import '../widget/top_items_revenue_by_date.dart';
import '../widget/topandbottom_branches_revenue.dart';
import '../widget/topandbottom_categories_by_amount.dart';
import '../widget/topandbottom_items_revenue.dart';
import '../widget/total_payment_amount.dart';
import '../widget/total_payment_amount_by_date.dart';

class ReportView extends StatelessWidget {
  const ReportView({super.key});

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return BlocProvider(
      create: (context) => HomeCubit()..fetchData(context, [], DateTime.now(), DateTime.now()),
      child: Scaffold(
        backgroundColor: AppColors.secondBackground,
        appBar: AppBar(
          title: CustomTitleAppBar(title: 'Doanh thu'),
          backgroundColor: AppColors.primary,
          centerTitle: true,
          leading: InkWell(
            onTap: () {
              clearOnExit();
              Get.back();
            },
            child: Icon(Icons.arrow_back_ios, color: AppColors.background,),
          ),
        ),
        body: BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            if (state is HomeLoading) {
              return Center(child: loadingIndicator(width));
            }
            if (state is HomeLoaded) {
              return SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: width * 0.015, vertical: width * 0.03),
                  child: Column(
                    children: [
                      branchSelection(context, state, width),
                      SizedBox(height: width * 0.015),
                      Padding(
                          padding: EdgeInsets.symmetric(horizontal: width * 0.005),
                        child: CategorySelector(
                          onCategorySelected: (category) {
                            BlocProvider.of<HomeCubit>(context).updateSelectedCategory(category);
                          },
                        ),
                      ),
                      if (state.selectedCategory == "dashboard") ...[
                        state.isLoadingOverviewRevenue
                            ? loadingIndicator(width)
                            : Column(
                                children: [
                                SizedBox(height: width * 0.01),
                                overviewRevenue(
                                    context,
                                    width,
                                    state.overviewRevenue,
                                    "business_overview".tr,
                                    "(${DateFormat('dd/MM/yyyy').format(state.fromDate ?? DateTime.now())} - ${DateFormat('dd/MM/yyyy').format(state.toDate ?? DateTime.now())})",
                                  ),
                                  SizedBox(height: width * 0.03),
                                  state.isLoadingReport7Days
                                      ? loadingIndicator(width)
                                      : Report7DaysChart(
                                      width: width, report7Days: state.report7Days),
                                  SizedBox(height: width * 0.03),
                                  state.isLoadingTopBottomBranchesRevenue
                                      ? loadingIndicator(width)
                                      : TopBottomBranchesRevenue(
                                      width: width,
                                      branches: state.topBranchesRevenue,
                                      title: "highest_branch_revenue".tr,),
                                  SizedBox(height: width * 0.03),
                                  state.isLoadingTopBottomBranchesRevenue
                                      ? loadingIndicator(width)
                                      : TopBottomBranchesRevenue(
                                      width: width,
                                      branches: state.bottomBranchesRevenue,
                                      title: "lowest_branch_revenue".tr,),
                                  SizedBox(height: width * 0.03),
                                  state.isLoadingTopBranchesRevenueByDate
                                      ? loadingIndicator(width)
                                      : TopBranchesRevenueByDate(
                                    width: width,
                                    title:
                                    "Doanh thu các chi nhánh bán chạy theo ngày",
                                    data: state.topBranchesRevenueByDate,
                                  ),
                              ],
                            ),
                      ]
                      else if (state.selectedCategory == "by_item") ...[
                        SizedBox(height: width * 0.015),
                        state.isLoadingTopAndBottomItems
                            ? loadingIndicator(width)
                            : Column(
                                children: [
                                  SizedBox(height: width * 0.015),
                                  TopItemsByTotalQuantity(
                                    width: width,
                                    topItemsByTotalQuantity:
                                        state.topItemsByTotalQuantity,
                                  ),
                                  SizedBox(height: width * 0.03),
                                  TopAndBottomItems(
                                    width: width,
                                    items: state.topItemsByTotalAmount,
                                    title: "top_selling_products".tr
                                  ),
                                  SizedBox(height: width * 0.03),
                                  TopAndBottomItems(
                                    width: width,
                                    items: state.bottomItems,
                                    title: "slow_selling_products".tr
                                  ),
                                  SizedBox(height: width * 0.03),
                                  state.isLoadingTopItemsRevenueByDate
                                      ? loadingIndicator(width)
                                      : TopItemsRevenueByDate(
                                          width: width,
                                          title: "Doanh thu các mặt hàng bán chạy theo ngày",
                                          topItemsRevenueByDate:
                                              state.topItemsRevenueByDate,
                                        ),
                                ],
                              ),
                      ]
                      else if (state.selectedCategory == "by_category") ...[
                        SizedBox(height: width * 0.015),
                        state.isLoadingTopAndBottomCategoriesByAmount
                            ? loadingIndicator(width)
                            : Column(
                                children: [
                                  TopCategoriesByQuantity(
                                    width: width,
                                    topCategoriesByQuantity: state.topCategoriesByQuantity,
                                  ),
                                  SizedBox(height: width * 0.03),
                                  TopAndBottomCategoriesByAmount(
                                    width: width,
                                    categories: state.topCategoriesByAmount,
                                    title: "top_categories_by_revenue".tr
                                  ),
                                  SizedBox(height: width * 0.03),

                                  TopAndBottomCategoriesByAmount(
                                    width: width,
                                    categories: state.bottomCategoriesByAmount,
                                    title: "bottom_categories_by_revenue".tr
                                  ),
                                  SizedBox(height: width * 0.03),
                                  state.isLoadingTopCatsRevenueByDate
                                      ? loadingIndicator(width)
                                      : TopCatsRevenueByDate(
                                    width: width,
                                    title:
                                    "Doanh thu các nhóm hàng bán chạy theo ngày",
                                    topCatsRevenueByDate: state.topCatsRevenueByDate,
                                  ),
                                ],
                              ),
                      ]
                      else if (state.selectedCategory == "by_payment") ...[
                            SizedBox(height: width * 0.015),
                            state.isLoadingTotalPaymentAmount
                                ? loadingIndicator(width)
                                : Column(
                              children: [
                                SizedBox(height: width * 0.015),
                                TotalPaymentAmount(
                                  width: width,
                                  title: "total_payment_amount".tr,
                                  totalPaymentAmount: state.totalPaymentAmount,
                                ),
                                SizedBox(height: width * 0.03),
                                state.isLoadingTotalPaymentAmountByDate
                                    ? loadingIndicator(width)
                                    : TotalPaymentAmountByDate(
                                  width: width,
                                  title:
                                  "Doanh thu các phương thức thanh toán theo ngày",
                                  totalPaymentAmountByDate:
                                  state.totalPaymentAmountByDate,
                                ),
                              ],
                            ),

                          ]
                    ],
                  ),
                ),
              );
            }
            return Container();
          },
        ),
      ),
    );
  }
}

class CategorySelector extends StatelessWidget {
  final Function(String) onCategorySelected;

  const CategorySelector({super.key, required this.onCategorySelected});

  @override
  Widget build(BuildContext context) {
    final homeCubit = BlocProvider.of<HomeCubit>(context);
    final double width = MediaQuery.of(context).size.width;

    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        String selectedCategory =
        (state is HomeLoaded) ? state.selectedCategory : "dashboard";

        final categories = {
          "dashboard": "dashboard".tr,
          "by_item": "by_item".tr,
          "by_category": "by_category".tr,
          "by_payment": "by_payment".tr,
        };

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: width * 0.01, vertical: width * 0.01),
          child: GridView.count(
            crossAxisCount: 3,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            mainAxisSpacing: width * 0.02,
            crossAxisSpacing: width * 0.02,
            childAspectRatio: 3,
            children: categories.entries.map((entry) {
              String categoryKey = entry.key;
              String categoryName = entry.value;
              bool isSelected = categoryKey == selectedCategory;

              return Container(
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : AppColors.background,
                  borderRadius: BorderRadius.circular(width * 0.02),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadow.withOpacity(0.5),
                      blurRadius: 3,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    elevation: 0, // Tắt bóng mặc định của ElevatedButton
                    backgroundColor: Colors.transparent,
                    foregroundColor: isSelected ? AppColors.background : AppColors.text,
                    padding: EdgeInsets.symmetric(horizontal: width * 0.02),
                    shadowColor: Colors.transparent, // Đảm bảo không bị đè bóng
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(width * 0.02),
                    ),
                  ),
                  onPressed: () {
                    homeCubit.updateSelectedCategory(categoryKey);
                  },
                  child: Text(
                    categoryName,
                    textAlign: TextAlign.center,
                    style: isSelected ?
                    PrimaryFont.bold.copyWith(
                      color:  AppColors.background,
                      fontSize: width * 0.035,
                    ) :
                    PrimaryFont.regular.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.035,
                    )
                  ),
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}

Future<void> clearOnExit() async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.remove("fromDate");
  await prefs.remove("toDate");
  await prefs.remove("selectedTimeOption");
  await prefs.remove("selectedBrands");
  await prefs.remove("selectedBrandIds");
  await prefs.remove("selectedBrandId");
  await prefs.remove("selectedProvince");
}