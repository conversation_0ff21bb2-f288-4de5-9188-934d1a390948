import '../../../domain/entities/menu/menu_entity.dart';
import '../../../domain/repositories/menu/menu_repository.dart';
import '../../datasources/e_menu/menu_local_data_source.dart';
import '../../datasources/e_menu/menu_remote_data_source.dart';
import '../../models/e_menu/menu_tree_model.dart';

class MenuRepositoryImpl implements MenuRepository {
  final MenuRemoteDataSource remote;
  final MenuLocalDataSource local;

  MenuRepositoryImpl({required this.remote, required this.local});

  @override
  Future<MenuEntity> getMenuTreeWithSync({bool forceUpdate = true}) async {
    final latestMenu = await remote.fetchMenuInfo();
    final cachedMenuTree = await local.getCachedMenuTree();

    // Thêm flag forceUpdate để luôn cập nhật
    final shouldUpdate = forceUpdate ||
        cachedMenuTree == null ||
        cachedMenuTree.lastUpdatedAt != latestMenu['LastUpdatedAt'];

    if (shouldUpdate) {
      final newMenuTree = await remote.fetchMenuTree();
      final model = MenuTreeModel.fromJson(newMenuTree);
      await local.cacheMenuTree(model);
      return MenuEntity.fromJson(newMenuTree);
    }

    return MenuEntity.fromJson(cachedMenuTree!.fullJson);
  }
}
