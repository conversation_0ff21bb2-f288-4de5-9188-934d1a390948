import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../../../core/network/dio_client.dart';
import '../../../core/constants/api_url.dart';
import '../../../core/network/dio_client.dart';
import '../../../service_locator.dart';

abstract class BranchService {
  Future<Either> getBranch();
}

class BranchApiServiceImpl extends BranchService {
  @override
  Future<Either> getBranch() async {
    try {
      var response = await sl<DioClientSME>().get(
        ApiUrl.branch,
      );
      return Right(response.data);
    } on DioException catch (e) {
      return Left(e.response?.data?['message']);
    }
  }
}
