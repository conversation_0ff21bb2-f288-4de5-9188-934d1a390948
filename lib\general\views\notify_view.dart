import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/notification_controller.dart';
import 'package:intl/intl.dart';
import '../../sme/presentation/notification/firebase_notification_setup.dart';
import '../../sme/presentation/notification/inapp_notificationdialog.dart';
import '../classes/notification_model.dart';

class NotifyView extends StatefulWidget {
  const NotifyView({super.key});

  @override
  State<NotifyView> createState() => _NotifyViewState();
}

class _NotifyViewState extends State<NotifyView> {
  final NotificationController notificationController = Get.find<NotificationController>();


  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const CustomTitleAppBar(title: 'Thông báo'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_sweep, color: Colors.white),
            onPressed: () {
              // Xóa tất cả thông báo
              notificationController.clearAllNotifications();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterChips(),
          Expanded(
            child: Obx(() {
              if (notificationController.filteredNotifications.isEmpty) {
                return Center(
                  child: CustomText(text: 'Bạn chưa có thông báo nào', bold: true),
                );
              }
              return ListView.builder(
                itemCount: notificationController.filteredNotifications.length,
                itemBuilder: (context, index) {
                  final notification = notificationController.filteredNotifications[index];
                  return _buildNotificationItem(notification);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Obx(() {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildChip(NotificationType.all, 'Tất cả'),
              const SizedBox(width: 8),
              _buildChip(NotificationType.unread, 'Chưa đọc'),
              const SizedBox(width: 8),
              _buildChip(NotificationType.system, 'Hệ thống'),
              const SizedBox(width: 8),
              _buildChip(NotificationType.transaction, 'Giao dịch'),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildChip(NotificationType type, String label) {
    final bool isSelected = notificationController.currentFilter == type;
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      selectedColor: AppColors.primary,
      backgroundColor: Colors.grey[200],
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black87,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
      onSelected: (selected) {
        if (selected) {
          notificationController.setFilter(type);
        }
      },
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    final DateTime publicationDate = DateTime.fromMillisecondsSinceEpoch(notification.timestamp);
    final String formattedTime = DateFormat('dd/MM/yyyy HH:mm').format(publicationDate);
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: 0,
      color: notification.isRead ? AppColors.primary.withValues(alpha: 0.15) : AppColors.primary.withValues(alpha: 0.25),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(
          color: notification.isRead ? Colors.grey.withValues(alpha: 0.05) : Colors.grey.withValues(alpha: 0.4),
          width: 2,
        ),
      ),

      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomText(
                  text: formattedTime,
                  size: 12,
                  color: Colors.grey,
                ),
              ],
            ),

            ListTile(
              leading: Icon(
                _getIconForNotificationType(notification.type),
                color: AppColors.primary,
              ),
              title: CustomText(text: notification.title,bold: notification.isRead ? false : true,),
              subtitle: CustomText(
                text: notification.body,
                maxLines: 3,
                size: 14,
              ),
              trailing: notification.isRead
                  ? null
                  : const Icon(Icons.fiber_manual_record, color: AppColors.primary, size: 12),
              onTap: () {
                notificationController.markAsRead(notification.id);

                Get.dialog(
                  InAppNotificationDialog(
                    notification: RemoteNotification(
                      title: notification.title,
                      body: notification.body,
                    ),
                    data: notification.data,
                    onDetailTap: () {
                      handleNotificationTap(notification.data);
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForNotificationType(String type) {
    switch (type) {
      case 'system':
        return Icons.info_outline;
      case 'transaction':
        return Icons.money_outlined;
      default:
        return Icons.notifications_active;
    }
  }
}