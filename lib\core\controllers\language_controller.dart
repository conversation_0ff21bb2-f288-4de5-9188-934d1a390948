import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageController extends GetxController {
  var currentLocale = const Locale('vi').obs;

  @override
  void onInit() {
    super.onInit();
    loadSavedLanguage();
  }

  void changeLanguage(String langCode) {
    final locale = Locale(langCode);
    currentLocale.value = locale;
    Get.updateLocale(locale);
    saveLanguage(langCode);
  }

  Future<void> loadSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLang = prefs.getString('langCode') ?? 'vi';
    currentLocale.value = Locale(savedLang);
    Get.updateLocale(currentLocale.value);
  }

  Future<void> saveLanguage(String langCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('langCode', langCode);
  }
}
