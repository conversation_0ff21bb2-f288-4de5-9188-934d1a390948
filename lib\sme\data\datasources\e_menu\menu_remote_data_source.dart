import '../../../../core/constants/sme_url.dart';
import '../../../../core/network/dio_client.dart';

abstract class MenuSMERemoteDataSource {
  Future<Map<String, dynamic>> fetchMenuInfoSME();
  Future<Map<String, dynamic>> fetchMenuTreeSME();
}

class MenuSMERemoteDataSourceImpl implements MenuSMERemoteDataSource {
  final DioClientSME dioClient;

  MenuSMERemoteDataSourceImpl({required this.dioClient});

  @override
  Future<Map<String, dynamic>> fetchMenuInfoSME() async {
    final response = await dioClient.get(SmeUrl.menuInfo);
    return response.data['Result'];
  }

  @override
  Future<Map<String, dynamic>> fetchMenuTreeSME() async {
    final response = await dioClient.get(SmeUrl.menuTree);
    return response.data['Result'];
  }
}
