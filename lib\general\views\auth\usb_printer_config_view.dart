import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/controllers/usb_printer_controller.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class UsbPrinterConfigView extends StatefulWidget {
  const UsbPrinterConfigView({super.key});

  @override
  State<UsbPrinterConfigView> createState() => _UsbPrinterConfigViewState();
}

class _UsbPrinterConfigViewState extends State<UsbPrinterConfigView> {
  //variable
  UsbPrinterController usbPrinterController = Get.find();

  //function
  @override
  void initState() {
    super.initState();
    usbPrinterController.getDeviceList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'M<PERSON>y in USB'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              usbPrinterController.getDeviceList();
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.refresh, color: Colors.white, size: 35,),
            ),
          )
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Obx(() {
          return ListView(
            padding: EdgeInsets.all(10),
            children: [
              for(dynamic item in usbPrinterController.devices)
                InkWell(
                  onTap: () async {
                    await usbPrinterController.connect(item);
                    if (usbPrinterController.isConnected.value) {
                      Get.back();
                      Get.back();
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(10)
                    ),
                    padding: EdgeInsets.all(10),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 5),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: item['productName'], color: Colors.white,),
                        CustomText(text: item['manufacturer'], color: Colors.white,),
                      ],
                    ),
                  ),
                )
            ],
          );
        }),
      ),
    );
  }
}
