import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../core/classes/app_function.dart';
import '../../../../core/components/custom_back_button.dart';
import '../../../../core/components/custom_text.dart';
import '../../../../core/constants/sme_url.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../classes/user_info.dart';
import '../../../controllers/bank_account_controller.dart';

class OtpVerificationView extends StatefulWidget {
  final String requestId;
  final String authorizationId;
  final int autoId;
  final int branchId;

  const OtpVerificationView({
    super.key,
    required this.requestId,
    required this.authorizationId,
    required this.autoId,
    required this.branchId,
  });

  @override
  State<OtpVerificationView> createState() => _OtpVerificationViewState();
}

class _OtpVerificationViewState extends State<OtpVerificationView> {
  final TextEditingController _otpController = TextEditingController();
  final BankAccountController controller = Get.find<BankAccountController>();

  void _printRequestData() {
    debugPrint('=== OTP VERIFICATION REQUEST DATA ===');

    debugPrint('OTP: ${_otpController.text}');
    debugPrint('RequestId: ${widget.requestId}');
    debugPrint('AuthorizationId: ${widget.authorizationId}');
    debugPrint('AutoId: ${widget.autoId}');
    debugPrint('BranchId: ${UserInfo.branchId}');
    debugPrint(
        'Endpoint: ${SmeUrl.acbRegisterVerify}?id=${widget.autoId}&branchId=${widget.branchId}');
    debugPrint('Request Body: ${{
      "RequestId": widget.requestId,
      "AuthorizationId": widget.authorizationId,
      "Code": _otpController.text,
    }.toString()}');
    debugPrint('====================================');

    AppFunction.showSuccess('Đã in thông tin request ra console');
  }

  Future<void> _verifyOtp() async {
    if (_otpController.text.isEmpty) {
      AppFunction.showError('Vui lòng nhập mã OTP');
      return;
    }

    final success = await controller.verifyOtp(
      _otpController.text,
      widget.requestId,
      widget.authorizationId,
      widget.autoId,
      widget.branchId,
    );

    if (success) {
      Get.back(); // Quay lại màn hình thông tin tài khoản
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        leading: const CustomBackButton(),
        title: CustomText(
          text: "Xác thực OTP",
          size: 20,
          bold: true,
          color: Colors.white,
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 32),
            Text(
              "Mã OTP gửi đến ứng dụng ACB ONE của bạn",
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 24),
            TextField(
              controller: _otpController,
              keyboardType: TextInputType.number,
              maxLength: 10,
              decoration: InputDecoration(
                hintText: "Nhập mã OTP",
                prefixIcon: const Icon(Icons.lock_outline),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 24),
            // // Nút kiểm tra request
            // SizedBox(
            //   width: double.infinity,
            //   height: 45,
            //   child: OutlinedButton(
            //     onPressed: _printRequestData,
            //     style: OutlinedButton.styleFrom(
            //       side: const BorderSide(color: AppColors.primary),
            //       shape: RoundedRectangleBorder(
            //         borderRadius: BorderRadius.circular(6),
            //       ),
            //     ),
            //     child: const CustomText(
            //       text: "Kiểm tra Request",
            //       color: AppColors.primary,
            //       bold: true,
            //     ),
            //   ),
            // ),
            // const SizedBox(height: 12),
            // Nút xác thực
            SizedBox(
              width: double.infinity,
              height: 45,
              child: ElevatedButton(
                onPressed: _verifyOtp,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: controller.isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : CustomText(
                        text: "Xác thực",
                        color: Colors.white,
                        bold: true,
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
