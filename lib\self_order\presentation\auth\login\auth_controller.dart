import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/utils/toast_info.dart';
import '../../../domain/entities/login/login_request_entity.dart';
import '../../../domain/entities/login/login_response_entity.dart';
import '../../../domain/usecases/login_usecase.dart';
import '../sale_common/areas_controller.dart';
import '../sale_common/branches_controller.dart';
import '../sale_common/counters_controller.dart';
import '../sale_common/tables_controller.dart';
import 'confirm_logout_dialog.dart';

class AuthSOController extends GetxController {
  final LoginUseCase loginUseCase;

  AuthSOController({required this.loginUseCase});

  var isLoading = false.obs;
  var obscurePassword = true.obs;
  var rememberMe = false.obs;
  var loginResponse = Rx<LoginResponseEntity?>(null);
  var errorMessage = RxString('');
  var currentLocale = const Locale('vi').obs;

  void togglePasswordVisibility() {
    obscurePassword.toggle();
  }

  void toggleRememberMe() {
    rememberMe.toggle();
  }

  Future<void> login(String username, String password, int orgId, double width) async {
    try {
      isLoading(true);
      errorMessage('');

      // Lấy controller liên quan
      final branchesController = Get.find<BranchesController>();
      final counterController = Get.find<CounterController>();
      final areasController = Get.find<AreasController>();
      final tablesController = Get.find<TablesController>();

      // Kiểm tra nếu thiếu dữ liệu
      if (branchesController.selectedBranch.value == null) {
        toastInfo(msg: 'Vui lòng chọn chi nhánh', width: width);
        return;
      }
      if (counterController.selectedCounter.value == null) {
        toastInfo(msg: 'Vui lòng chọn quầy', width: width);
        return;
      }
      if (areasController.selectedArea.value == null) {
        toastInfo(msg: 'Vui lòng chọn khu vực', width: width);
        return;
      }
      if (tablesController.selectedTable.value == null) {
        toastInfo(msg: 'Vui lòng chọn bàn', width: width);
        return;
      }

      final request = LoginRequestEntity(
        username: username,
        password: password,
        orgId: orgId,
      );

      final response = await loginUseCase(request);

      if (!response.success) {
        switch (response.code) {
          case 400:
            toastInfo(msg: 'error_login_failed'.tr, width: width);
            break;
          case 401:
            toastInfo(msg: 'unauthorized'.tr, width: width);
            break;
          case 403:
            toastInfo(msg: 'forbidden'.tr, width: width);
            break;
          case 404:
            toastInfo(msg: 'not_found'.tr, width: width);
            break;
          case 500:
            toastInfo(msg: 'error_invalid_api'.tr, width: width);
            break;
          default:
            toastInfo(msg: 'error_unknown'.tr, width: width);
        }
        return;
      }

      loginResponse(response);

      if (response.token.isNotEmpty) {
        final prefs = Get.find<SharedPreferences>();
        prefs.setString('token_so', response.token);
        prefs.setString('username', username);
        prefs.setString('password', password);
        prefs.setBool('rememberMe', rememberMe.value);

        prefs.setInt('branchId', branchesController.selectedBranch.value!.branchId!);
        prefs.setString('branchName', branchesController.selectedBranch.value!.branchName!);
        prefs.setInt('counterId', counterController.selectedCounter.value!.counterId!);
        prefs.setInt('areaId', areasController.selectedArea.value!.areaId!);
        prefs.setInt('tableId', tablesController.selectedTable.value!.tableId!);
        // In ra để kiểm tra
        print('✅ Đã lưu SharedPreferences:');
        print('Token: ${prefs.getString('token_so')}');
        print('RememberMe: ${prefs.getBool('rememberMe')}');
        print('Branch ID: ${prefs.getInt('branchId')}');
        print('Counter ID: ${prefs.getInt('counterId')}');
        print('Area ID: ${prefs.getInt('areaId')}');
        print('Table ID: ${prefs.getInt('tableId')}');
        print('branch Name: ${prefs.getString('branchName')}');
      }

      Get.offAllNamed('/banner-page');
    } catch (e) {
      errorMessage(e.toString());
      toastInfo(msg: 'error_network'.tr, width: width);
    } finally {
      isLoading(false);
    }
  }

  Future<void> logout(BuildContext context, double width) async {
    final prefs = Get.find<SharedPreferences>();
    final savedPassword = prefs.getString('password') ?? '';

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return ConfirmLogoutDialog(
          savedPassword: savedPassword,
          width: width,
          onConfirmed: () async {
            await _performLogout();
          },
        );
      },
    );
  }

  Future<void> _performLogout() async {
    try {
      final prefs = Get.find<SharedPreferences>();
      await prefs.remove('token_so');
      await prefs.remove('rememberMe');
      await prefs.remove('branchId');
      await prefs.remove('counterId');
      await prefs.remove('areaId');
      await prefs.remove('tableId');
      await prefs.remove('username');
      await prefs.remove('password');
      await prefs.remove('branchName');

      loginResponse(null);

      print('Đã xóa token và rememberMe');
      Get.offAllNamed('/login-page');
    } catch (e) {
      print('Đăng xuất thất bại: $e');
    }
  }


}