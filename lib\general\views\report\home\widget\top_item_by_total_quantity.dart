import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/helper/format_helper.dart';
import '../../domain/report/entities/report.dart';

class TopItemsByTotalQuantity extends StatelessWidget {
  final double width;
  final List<ItemEntity> topItemsByTotalQuantity;

  const TopItemsByTotalQuantity({
    super.key,
    required this.width,
    required this.topItemsByTotalQuantity,
  });

  @override
  Widget build(BuildContext context) {
    bool isEmptyData = topItemsByTotalQuantity.isEmpty ||
        topItemsByTotalQuantity.every((e) => (e.totalQuantity ?? 0) == 0);

    List<ItemEntity> displayData = isEmptyData
        ? [ItemEntity(itemName: "Không có dữ liệu", totalQuantity: 0, totalAmount: 0)]
        : topItemsByTotalQuantity;

    Map<String, double> groupedItems = {};
    for (var item in displayData) {
      String itemName = item.itemName ?? "Không xác định";
      double quantity = item.totalQuantity ?? 0;
      groupedItems[itemName] = (groupedItems[itemName] ?? 0) + quantity;
    }

    List<MapEntry<String, double>> sortedItems = groupedItems.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final top10Items = sortedItems.take(10).toList();

    final List<Color> colors = [
      Colors.blue, Colors.green, Colors.red, Colors.orange, Colors.purple,
      Colors.teal, Colors.yellow, Colors.pink, Colors.cyan, Colors.brown
    ];

    List<Map<String, dynamic>> data = [];
    bool hasMultilineName = false;

    for (int i = 0; i < top10Items.length; i++) {
      String formattedName = _formatItemName(top10Items[i].key, 10);
      if (formattedName.contains("\n")) {
        hasMultilineName = true;
      }

      data.add({
        "itemName": formattedName,
        "quantity": top10Items[i].value.toInt(),
        "color": colors[i % colors.length],
      });
    }

    num maxQuantity = data.map((e) => e["quantity"]).reduce((a, b) => a > b ? a : b);
    num maxYAxis = maxQuantity > 0 ? (maxQuantity * 1.2) : 1;

    double extraBottomPadding = hasMultilineName ? width * 0.05 : width * 0.015;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: width * 0.015, vertical: width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "Sản phẩm bán chạy theo số lượng (Top 10)".toUpperCase(),
              style: PrimaryFont.bold.copyWith(
                color: AppColors.text,
                fontSize: width * 0.04,
              ),
              maxLines: 2,
            ),
            SizedBox(height: width * 0.03),
            SizedBox(
              height: width * 0.65,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Container(
                  padding: EdgeInsets.only(bottom: extraBottomPadding),
                  width: width * 1.8,
                  child: Chart(
                    data: data,
                    variables: {
                      'itemName': Variable(
                        accessor: (Map map) => map['itemName'] as String,
                        scale: OrdinalScale(),
                      ),
                      'quantity': Variable(
                        accessor: (Map map) => map['quantity'] as num,
                        scale: LinearScale(
                          min: 0,
                          max: maxYAxis,
                          tickCount: 5,
                          formatter: (num value) => FormatHelper.formatCurrency(value),
                        ),
                      ),
                    },
                    marks: [
                      IntervalMark(
                        position: Varset('itemName') * Varset('quantity'),
                        color: ColorEncode(
                          variable: 'itemName',
                          values: data.length < 2
                              ? [Colors.grey, Colors.grey]
                              : data.map((e) => e["color"] as Color).toList(),
                        ),
                        label: LabelEncode(
                          encoder: (tuple) => Label(
                            FormatHelper.formatCurrency(tuple['quantity'] as num),
                            LabelStyle(
                              textStyle: PrimaryFont.bold.copyWith(
                                fontSize: width * 0.03,
                                color: AppColors.text,
                              ),
                              offset: Offset(0, -10),
                            ),
                          ),
                        ),
                      ),
                    ],
                    axes: [
                      AxisGuide(
                        line: PaintStyle(strokeWidth: 0),
                        label: LabelStyle(
                          textStyle: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          offset: Offset(0, 10),
                        ),
                      ),
                      AxisGuide(
                        label: LabelStyle(
                          textStyle: PrimaryFont.regular.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                          offset: const Offset(-5, 0),
                        ),
                        grid: PaintStyle(
                          strokeColor: AppColors.shadow.withOpacity(0.3),
                          strokeWidth: 1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: width * 0.01),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(top10Items.length, (i) {
                  return _buildLegendItem(i, top10Items, colors, width);
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatItemName(String name, int maxLength) {
    List<String> words = name.split(' ');
    String newName = '';
    int lineLength = 0;

    for (String word in words) {
      if (lineLength + word.length > maxLength) {
        newName += '\n';
        lineLength = 0;
      }
      newName += (lineLength == 0 ? '' : ' ') + word;
      lineLength += word.length + 1;
    }

    return newName;
  }

  Widget _buildLegendItem(int index, List<MapEntry<String, double>> top10Items, List<Color> colors, double width) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Row(
        children: [
          Container(
            width: width * 0.03,
            height: width * 0.03,
            color: colors[index % colors.length],
          ),
          SizedBox(width: width * 0.01),
          Text(
            top10Items[index].key,
            style: PrimaryFont.regular.copyWith(
              fontSize: width * 0.028,
              color: AppColors.text,
            ),
          ),
        ],
      ),
    );
  }
}
