import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/branch/entities/branch.dart';
import '../../domain/branch/usecases/get_branch.dart';
import '../../domain/report/entities/report.dart';
import '../../domain/report/usecases/get_report.dart';
import '../../service_locator.dart';
import 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  final GetBranchUseCase getBranchUseCase;
  final GetOverviewRevenueUseCase getOverviewRevenueUseCase;
  final GetTopBottomBranchesRevenueUseCase getTopBottomBranchesRevenueUseCase;
  final GetRevenue7DaysUseCase getReport7DaysUseCase;
  final GetTopAndBottomItemsRevenueUseCase getTopAndBottomItemsRevenueUseCase;
  final GetTopAndBottomCategoriesByAmountUseCase getTopAndBottomCategoriesByAmountUseCase;
  final GetTopItemsRevenueByDateUseCase getTopItemsRevenueByDateUseCase;
  final GetTopBranchesRevenueByDateUseCase getTopBranchesRevenueByDateUseCase;
  final GetTopCatsRevenueByDateUseCase getTopCatsRevenueByDateUseCase;
  final GetTotalPaymentAmountUseCase getTotalPaymentAmountUseCase;
  final GetTotalPaymentAmountByDateUseCase getTotalPaymentAmountByDateUseCase;

  HomeCubit()
      : getBranchUseCase = sl<GetBranchUseCase>(),
        getOverviewRevenueUseCase = sl<GetOverviewRevenueUseCase>(),
        getTopBottomBranchesRevenueUseCase = sl<GetTopBottomBranchesRevenueUseCase>(),
        getReport7DaysUseCase = sl<GetRevenue7DaysUseCase>(),
        getTopAndBottomItemsRevenueUseCase = sl<GetTopAndBottomItemsRevenueUseCase>(),
        getTopAndBottomCategoriesByAmountUseCase = sl<GetTopAndBottomCategoriesByAmountUseCase>(),
        getTopItemsRevenueByDateUseCase = sl<GetTopItemsRevenueByDateUseCase>(),
        getTopBranchesRevenueByDateUseCase = sl<GetTopBranchesRevenueByDateUseCase>(),
        getTopCatsRevenueByDateUseCase = sl<GetTopCatsRevenueByDateUseCase>(),
        getTotalPaymentAmountUseCase = sl<GetTotalPaymentAmountUseCase>(),
        getTotalPaymentAmountByDateUseCase = sl<GetTotalPaymentAmountByDateUseCase>(),
        super(HomeInitial());

  Future<void> fetchData(BuildContext context, List<int> brandIds, DateTime from, DateTime to) async {
    emit(HomeLoading());
    final prefs = await SharedPreferences.getInstance();
    final optionKey = prefs.getString("selectedTimeOption") ?? "today";
    final fromDate = DateFormat('yyyy-MM-dd').format(from);
    final toDate = DateFormat('yyyy-MM-dd').format(to);

    final branchResult = await getBranchUseCase();
    if (branchResult.isLeft()) {
      debugPrint("❌ Failed to fetch branches");
    }

    final branches = branchResult.getOrElse(() => []);

    BranchEntity? defaultBrand;
    try {
      defaultBrand = branches.firstWhere((b) => b.branchParentId == -1);
    } catch (e) {
      defaultBrand = branches.isNotEmpty ? branches.first : BranchEntity(
        branchId: -1,
        branchName: "Default",
        branchParentId: -1,
      );
    }

    List<BranchEntity> branchesOfDefaultBrand = branches.where((b) =>
    b.branchParentId == defaultBrand?.branchId
    ).toList();
    List<String> defaultSelectedBrands = branchesOfDefaultBrand
        .map((b) => b.branchName ?? "")
        .where((name) => name.isNotEmpty)
        .toList();

    List<int> defaultSelectedBrandIds = branchesOfDefaultBrand
        .map((b) => b.branchId ?? 0)
        .where((id) => id != 0)
        .toList();

    final selectedBrandId = prefs.getInt('selectedBrandId');
    BranchEntity? selectedBrand;

    if (selectedBrandId != null) {
      try {
        selectedBrand = branches.firstWhere((b) => b.branchId == selectedBrandId);
      } catch (e) {
        selectedBrand = defaultBrand;
      }
    } else {
      selectedBrand = defaultBrand;
    }

    List<String> savedSelectedBrands = prefs.getStringList("selectedBrands") ?? defaultSelectedBrands;
    List<int> savedSelectedBrandIds = prefs.getStringList("selectedBrandIds")?.map(int.parse).toList() ?? defaultSelectedBrandIds;

    final branchNames = branches.map((e) => e.branchName).whereType<String>().toList();
    savedSelectedBrands = savedSelectedBrands.where((b) => branchNames.contains(b)).toList();

    if (savedSelectedBrands.isEmpty && defaultSelectedBrands.isNotEmpty) {
      savedSelectedBrands = defaultSelectedBrands;
      savedSelectedBrandIds = defaultSelectedBrandIds;
    }

    if (prefs.getInt('selectedBrandId') == null && defaultBrand != null) {
      await prefs.setInt('selectedBrandId', defaultBrand.branchId ?? -1);
      await prefs.setStringList("selectedBrands", defaultSelectedBrands);
      await prefs.setStringList("selectedBrandIds",
          defaultSelectedBrandIds.map((e) => e.toString()).toList());
    }

    emit(HomeLoaded(
      branches: branches,
      selectedBrand: selectedBrand,
      selectedBrands: savedSelectedBrands,
      selectedBrandIds: savedSelectedBrandIds,
      isLoadingOverviewRevenue: true,
      selectedTimeOption: optionKey,
      fromDate: from,
      toDate: to,
      isLoadingTopBottomBranchesRevenue: true,
      isLoadingReport7Days: true,
      isLoadingItemsRevenue: true,
      isLoadingTopAndBottomItems: true,
      isLoadingTopItemsRevenueByDate: true,
      isLoadingTotalAmountCats: true,
      isLoadingTopAndBottomCategoriesByAmount: true,
      isLoadingTopBranchesRevenueByDate: true,
      isLoadingTopCatsRevenueByDate: true,
      isAllBranchesSelected: true,

    ));

    //region DashBoard
    // OverviewRevenue
    getOverviewRevenueUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((overviewRevenueResult) {
      if (overviewRevenueResult.isLeft()) {
        debugPrint("❌ Failed OVERVIEW REVENUE!");
        return;
      }
      final overviewRevenue = overviewRevenueResult.fold(
            (failure) => [],
            (data) => [data],
      );

      emit((state as HomeLoaded).copyWith(
        overviewRevenue: List<OverviewRevenueEntity>.from(overviewRevenue),
        isLoadingOverviewRevenue: false,
      ));
    });

    // TopBottomBranchesRevenue
    getTopBottomBranchesRevenueUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((topBottomBranchesResult) {
      if (topBottomBranchesResult.isLeft()) {
        debugPrint("❌ Failed TOP BOTTOM BRANCH REVENUE");
      }

      final topBottomBranches = topBottomBranchesResult.getOrElse(() =>
          TopBottomBranchesRevenueEntity(topBranchesRevenue: [], bottomBranchesRevenue: [])
      );

      emit((state as HomeLoaded).copyWith(
        topBranchesRevenue: topBottomBranches.topBranchesRevenue,
        bottomBranchesRevenue: topBottomBranches.bottomBranchesRevenue,
        isLoadingTopBottomBranchesRevenue: false,
      ));
    });

    // Report 7 Days
    getReport7DaysUseCase(params: savedSelectedBrandIds).then((report7DaysResult) {
      if (report7DaysResult.isLeft()) {
        debugPrint("❌ Failed 7 DAYS REVENUE");
      }

      final report7Days = report7DaysResult.getOrElse(() => []);
      emit((state as HomeLoaded).copyWith(
        report7Days: report7Days,
        isLoadingReport7Days: false,
      ));
    });

    // Top Branches Revenue By Date
    getTopBranchesRevenueByDateUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((result) {
      if (result.isLeft()) {
        debugPrint("❌ Failed BRANCH BY DATE");
      } else {
        final data = result.getOrElse(() => []);

        emit((state as HomeLoaded).copyWith(
          topBranchesRevenueByDate: data,
          isLoadingTopBranchesRevenueByDate: false,
        ));
      }
    });
    //endregion

    //region By Item
    // Top & Bottom Items Revenue**
    getTopAndBottomItemsRevenueUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((topBottomItemsResult) {
      if (topBottomItemsResult.isLeft()) {
        debugPrint("❌ Failed TOP BOTTOM ITEMS REVENUE");
      }

      final topAndBottomItems = topBottomItemsResult.getOrElse(() => TopAndBottomItemsRevenueEntity(
        topItemsByTotalQuantity: [],
        topItemsByTotalAmount: [],
        bottomItems: [],
      ));

      emit((state as HomeLoaded).copyWith(
        topItemsByTotalQuantity: topAndBottomItems.topItemsByTotalQuantity,
        topItemsByTotalAmount: topAndBottomItems.topItemsByTotalAmount,
        bottomItems: topAndBottomItems.bottomItems,
        isLoadingTopAndBottomItems: false,
      ));
    });

    // Top Items Revenue By Date
    getTopItemsRevenueByDateUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((result) {
      if (result.isLeft()) {
        debugPrint("❌ Failed TOP ITEMS BY DATE");
        emit((state as HomeLoaded).copyWith(
          topItemsRevenueByDate: [],
          isLoadingTopItemsRevenueByDate: false,
        ));
      } else {
        final data = result.getOrElse(() => []);

        emit((state as HomeLoaded).copyWith(
          topItemsRevenueByDate: data,
          isLoadingTopItemsRevenueByDate: false,
        ));
      }
    });
    //endregion

    // region By Category
    // Top & Bottom Categories By Amount
    getTopAndBottomCategoriesByAmountUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((topBottomCategoriesResult) {
      if (topBottomCategoriesResult.isLeft()) {
        debugPrint("❌ Failed TOP BOTTOM CATEGORY");
      }

      final topAndBottomCategories = topBottomCategoriesResult.getOrElse(() => TopAndBottomCategoriesByAmountEntity(
        topCategoriesByQuantity: [],
        topCategoriesByAmount: [],
        bottomCategoriesByAmount: [],
      ));

      emit((state as HomeLoaded).copyWith(
        topCategoriesByQuantity: topAndBottomCategories.topCategoriesByQuantity,
        topCategoriesByAmount: topAndBottomCategories.topCategoriesByAmount,
        bottomCategoriesByAmount: topAndBottomCategories.bottomCategoriesByAmount,
        isLoadingTopAndBottomCategoriesByAmount: false,
      ));
    });

    // Top Cats Revenue By Date
    getTopCatsRevenueByDateUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((result) {
      if (result.isLeft()) {
        debugPrint("❌ Failed TOP CATEGORIES BY DATE");
        emit((state as HomeLoaded).copyWith(
          topCatsRevenueByDate: [],
          isLoadingTopCatsRevenueByDate: false,
        ));
      } else {
        final data = result.getOrElse(() => []);

        emit((state as HomeLoaded).copyWith(
          topCatsRevenueByDate: data,
          isLoadingTopCatsRevenueByDate: false,
        ));
      }
    });
    //endregion

    //region By Payment
    // Total Payment Amount
    getTotalPaymentAmountUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((totalPaymentAmountResult) {
      totalPaymentAmountResult.fold(
            (failure) {
          debugPrint("❌ Failed TOTAL PAYMENT AMOUNT: $failure");
          emit((state as HomeLoaded).copyWith(
            totalPaymentAmount: [],
            isLoadingTotalPaymentAmount: false,
          ));
        },
            (data) {
          emit((state as HomeLoaded).copyWith(
            totalPaymentAmount: data,
            isLoadingTotalPaymentAmount: false,
          ));
        },
      );
    });

    // Total Payment Amount By Date
    getTotalPaymentAmountByDateUseCase(params: ReportParams(
      listBrandId: savedSelectedBrandIds,
      dateFrom: fromDate,
      dateTo: toDate,
    )).then((result) {
      result.fold(
            (failure) {
          debugPrint("❌ Failed PAYMENT BY DATE: $failure");
          emit((state as HomeLoaded).copyWith(
            isLoadingTotalPaymentAmountByDate: false,
          ));
        },
            (data) {
          emit((state as HomeLoaded).copyWith(
            totalPaymentAmountByDate: data,
            isLoadingTotalPaymentAmountByDate: false,
          ));
        },
      );
    });
    //endregion
  }

  void toggleBrandSelection(int brandId, String brand) async {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;

      final branchesOfCurrentBrand = currentState.branches.where((b) =>
      b.branchParentId  == currentState.selectedBrand?.branchId
      ).toList();

      final updatedSelectedBrands = List<String>.from(currentState.selectedBrands ?? []);
      final updatedSelectedBrandIds = List<int>.from(currentState.selectedBrandIds ?? []);

      if (updatedSelectedBrands.contains(brand)) {
        updatedSelectedBrands.remove(brand);
        updatedSelectedBrandIds.remove(brandId);
      } else {
        updatedSelectedBrands.add(brand);
        updatedSelectedBrandIds.add(brandId);
      }

      final allBranchesSelected = branchesOfCurrentBrand.every((branch) =>
          updatedSelectedBrands.contains(branch.branchName)
      );

      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList("selectedBrands", updatedSelectedBrands);
      await prefs.setStringList("selectedBrandIds", updatedSelectedBrandIds.map((e) => e.toString()).toList());

      emit(currentState.copyWith(
        selectedBrands: updatedSelectedBrands,
        selectedBrandIds: updatedSelectedBrandIds,
        isAllBranchesSelected: allBranchesSelected,
      ));
    }
  }
  void toggleSelectAllBrands(List<BranchEntity> allBrands, bool selectAll) async {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;

      final branchesOfCurrentBrand = allBrands.where((branch) {
        return branch.branchParentId == currentState.selectedBrand?.branchId;
      }).toList();

      final updatedBrands = selectAll
          ? branchesOfCurrentBrand.map((e) => e.branchName).whereType<String>().toList()
          : <String>[];
      final updatedBrandIds = selectAll
          ? branchesOfCurrentBrand.map((e) => e.branchId).whereType<int>().toList()
          : <int>[];

      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList("selectedBrands", updatedBrands);
      await prefs.setStringList("selectedBrandIds",
          updatedBrandIds.map((e) => e.toString()).toList());

      emit(currentState.copyWith(
        selectedBrands: updatedBrands,
        selectedBrandIds: updatedBrandIds,
        isAllBranchesSelected: selectAll,
      ));
    }
  }
  void updateTimeFilter(String optionKey, {DateTime? from, DateTime? to}) async {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;
      DateTime now = DateTime.now();
      DateTime start, end;

      switch (optionKey) {
        case "today":
          start = end = now;
          break;
        case "yesterday":
          start = end = now.subtract(Duration(days: 1));
          break;
        case "this_week":
          start = now.subtract(Duration(days: now.weekday - 1));
          end = now;
          break;
        case "last_week":
          start = now.subtract(Duration(days: now.weekday + 6));
          end = start.add(Duration(days: 6));
          break;
        case "this_month":
          start = DateTime(now.year, now.month, 1);
          end = now;
          break;
        case "last_month":
          start = DateTime(now.year, now.month - 1, 1);
          end = DateTime(now.year, now.month, 0);
          break;
        case "this_year":
          start = DateTime(now.year, 1, 1);
          end = now;
          break;
        case "last_year":
          start = DateTime(now.year - 1, 1, 1);
          end = DateTime(now.year - 1, 12, 31);
          break;
        default:
          start = from ?? currentState.fromDate ?? now;
          end = to ?? currentState.toDate ?? now;
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString("selectedTimeOption", optionKey);

      emit(currentState.copyWith(
        selectedTimeOption: optionKey,
        fromDate: start,
        toDate: end,
      ));
    }
  }

  void updateSelectedCategory(String category) {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;
      emit(currentState.copyWith(selectedCategory: category));
    }
  }

  void updateSelectedBrand(BranchEntity brand) async {
    if (state is! HomeLoaded) return;
    final currentState = state as HomeLoaded;
    final prefs = await SharedPreferences.getInstance();

    await prefs.setInt('selectedBrandId', brand.branchId ?? -1);

    final branchesOfNewBrand = currentState.branches
        .where((b) => b.branchParentId == brand.branchId)
        .toList();

    final newSelectedBrands = branchesOfNewBrand
        .map((b) => b.branchName ?? "")
        .where((name) => name.isNotEmpty)
        .toList();
    final newSelectedBrandIds = branchesOfNewBrand
        .map((b) => b.branchId ?? 0)
        .where((id) => id != 0)
        .toList();

    await prefs.setStringList("selectedBrands", newSelectedBrands);
    await prefs.setStringList("selectedBrandIds",
        newSelectedBrandIds.map((e) => e.toString()).toList());

    emit(currentState.copyWith(
      selectedBrand: brand,
      selectedBrands: newSelectedBrands,
      selectedBrandIds: newSelectedBrandIds,
      isAllBranchesSelected: branchesOfNewBrand.isNotEmpty,
    ));
  }

  Set<int> expandedCustIds = {};

  void resetExpandedTiles() {
    expandedCustIds.clear();
  }

  Future<void> clearOnExit() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove("fromDate");
    await prefs.remove("toDate");
    await prefs.remove("selectedTimeOption");
  }

}
