import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/utils/image_cache_helper.dart';
import 'package:gls_self_order/fnb/views/fnb_home_view.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/news_controller.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';
import 'package:gls_self_order/general/views/account/account_view.dart';
import 'package:gls_self_order/general/views/customer/customer_view.dart';
import 'package:gls_self_order/general/views/finance/finance_view.dart';
import 'package:gls_self_order/general/views/news/news_all_view.dart';
import 'package:gls_self_order/general/views/news/news_detail_view.dart';
import 'package:gls_self_order/general/views/product/product_view.dart';
import 'package:iconsax/iconsax.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:gls_self_order/general/views/warehouse/warehouse_view.dart';

import '../../sme/routes/sme_app_routes.dart';
import '../controllers/revenue_controller.dart';
import 'report/home/<USER>/home.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  //variable
  NewsController newsController = Get.find();
  List news = [];
  RevenueController revenueController = Get.find();
  ProductController productController = Get.find();

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getNews();
    getSaleMenu();
    // Add this to fetch revenue data
    final now = DateTime.now();
    final dateFrom = now.toString().substring(0, 10);
    final dateTo = now.toString().substring(0, 10);
    revenueController.fetchRevenueData(dateFrom: dateFrom, dateTo: dateTo);
  }

  getNews() async {
    news = await newsController.getNews();
    setState(() {});
  }

  getSaleMenu() async {
    AppFunction.showLoading();
    await productController.getSaleMenu();
    AppFunction.hideLoading();
  }

  Widget renderItemInSummary(title, value, icon, color) {
    return Expanded(
      flex: 1,
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(8)),
        padding: EdgeInsets.fromLTRB(5, 10, 5, 10),
        margin: EdgeInsets.fromLTRB(2.5, 0, 2.5, 5),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(180)),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 0, 2.5, 0),
              child: Icon(
                icon,
                color: Colors.white,
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomText(text: title),
                CustomText(
                  text: value,
                  bold: true,
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget renderSummary() {
    final now = DateTime.now();
    //final dateFrom = now;
    final dateTo = now;

    return Container(
      padding: EdgeInsets.all(10),
      color: AppColors.primary.withValues(alpha: 0.2),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CustomText(
            text: 'TỔNG QUAN KINH DOANH',
            bold: true,
          ),
          Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 0, 5),
            child: CustomText(
              text: '(${AppFunction.formatDateNoTime(dateTo)})',
            ),
          ),
          Obx(() => Row(
                children: [
                  renderItemInSummary(
                      'Tổng tiền',
                      AppFunction.formatMoney(
                          revenueController.paymentAmount.value),
                      Icons.attach_money_sharp,
                      Colors.orangeAccent),
                  renderItemInSummary(
                      'Tiền hàng',
                      AppFunction.formatMoney(
                          revenueController.itemAmount.value),
                      Icons.insert_chart,
                      Colors.cyanAccent),
                ],
              )),
          Obx(() => Row(
                children: [
                  renderItemInSummary(
                      'Đơn hàng',
                      "${revenueController.totalBill.value}",
                      Icons.file_copy,
                      Colors.blueAccent),
                  renderItemInSummary(
                      'Giảm giá',
                      AppFunction.formatMoney(
                          revenueController.discountAmount.value),
                      Icons.discount,
                      Colors.greenAccent),
                ],
              )),
          Obx(() => Row(
                children: [
                  renderItemInSummary(
                      'Hoá đơn điện tử',
                      "${revenueController.totalInvoice.value}",
                      Icons.file_present,
                      Colors.lightGreen),
                  renderItemInSummary(
                      'Tiền VAT',
                      AppFunction.formatMoney(
                          revenueController.vatAmount.value),
                      Icons.money,
                      Colors.cyan),
                ],
              ))
        ],
      ),
    );
  }

  Widget renderItemMainButton(String title, String image, dynamic view) {
    return SizedBox(
      width: 110,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              if (view is String) {
                Get.toNamed(view);
              } else if (view is Widget) {
                Get.to(() => view);
              } else {
                throw Exception('Invalid type passed to renderItemMainButton');
              }
            },
            child: Container(
              width: 110,
              height: 100,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(image, width: 50),
                  CustomText(text: title, bold: true),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget renderMainButton() {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          _sectionHeaderRow(title: 'Ứng dụng', onViewAll: () {}),
          Wrap(
            spacing: 15,
            // runSpacing: 5,
            children: [
              renderItemMainButton('Bán hàng',
                  'assets/images/general/ban_hang.png', SmeAppRoutes.menu),
              renderItemMainButton('Tài chính',
                  'assets/images/general/tai_chinh.png', FinanceView()),
              renderItemMainButton('Doanh thu',
                  'assets/images/general/doanh_thu.png', ReportView()),
              renderItemMainButton(
                  'Fnb', 'assets/images/general/fnb.png', FnbHomeView()),
              renderItemMainButton('Quản lý kho',
                  'assets/images/general/quan_ly_kho.png', WarehouseView()),
            ],
          ),

          // Row(
          //   children: [
          //     renderItemMainButton('Bán hàng', 'assets/images/general/ban_hang.png', SmeAppRoutes.menu),
          //     renderItemMainButton('Tài chính', 'assets/images/general/tai_chinh.png', FinanceView()),
          //     renderItemMainButton('Danh mục', 'assets/images/general/vi_voucher.png', CategoryView()),
          //   ],
          // ),
          // Row(
          //   children: [
          //     renderItemMainButton('Doanh thu', 'assets/images/general/doanh_thu.png', ReportView()),
          //     // renderItemMainButton('Tài chính', 'assets/images/general/tai_chinh.png', FinanceView()),
          //     // renderItemMainButton('Khuyến mãi', 'assets/images/general/vi_voucher.png', ContactView()),
          //     // renderItemMainButton('Quản lý kho', 'assets/images/general/quan_ly_kho.png', WarehouseView()),
          //   ],
          // ),
        ],
      ),
    );
  }

  Widget renderItemNews(item) {
    return InkWell(
      onTap: () {
        Get.to(() => NewsDetailView(id: item['ID']));
      },
      child: Container(
        width: 180,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 5,
              offset: Offset(0, 2),
            ),
          ],
        ),
        margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
        clipBehavior: Clip.antiAlias,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
                width: 180,
                height: 180,
                child: item['CoverImageURL'].isNotEmpty
                    ? Image.network(
                        item['CoverImageURL'],
                        fit: BoxFit.cover,
                      )
                    : Image.asset('assets/images/general/no_image.jpg')),
            Padding(
              padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(180),
                        border: Border.all(
                            color: item['ContentType'] == 1
                                ? AppColors.primary
                                : AppColors.button,
                            width: 1)),
                    padding: EdgeInsets.fromLTRB(7, 2.5, 7, 2.5),
                    margin: EdgeInsets.fromLTRB(0, 5, 0, 0),
                    child: CustomText(
                      text: item['ContentType'] == 1 ? 'Tin tức' : 'Khuyến mãi',
                      color: item['ContentType'] == 1
                          ? AppColors.primary
                          : AppColors.button,
                    ),
                  ),
                  CustomText(
                    text: item['Title'],
                    maxLines: 2,
                    bold: true,
                  ),
                  Row(
                    children: [
                      Icon(Icons.calendar_month),
                      Expanded(
                          child: CustomText(
                              text: AppFunction.formatDateNoTime(
                                  item['UpdatedDate'])))
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget renderNews() {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          _sectionHeaderRow(
              title: "Tin tức - Khuyến mãi",
              onViewAll: () {
                Get.to(() => NewsAllView());
              }),
          Container(
            width: double.infinity,
            // color: Colors.blue,
            height: 310,
            margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.fromLTRB(2, 0, 0, 10),
              children: [
                for (dynamic item in news) renderItemNews(item),
              ],
            ),
          )
        ],
      ),
    );
  }

  // Widget updateNotifications() {
  //   final List<Map<String, String>> updateNotifications = [
  //     {
  //       "title": "New  Version Available",
  //       "description":
  //           "A new version of the app is available. Please update to the latest version."
  //     },
  //     {
  //       "title": "Bug Fixes",
  //       "description": "We fixed some issues with login and loading.",
  //     },
  //     {
  //       "title": "New Features",
  //       "description": "Now you can track your progress in real time.",
  //     },
  //     {
  //       "title": "New Features1",
  //       "description": "Now you can track your progress in real time.",
  //     },
  //     {
  //       "title": "New Features5",
  //       "description": "Now you can track your progress in real time.",
  //     },
  //   ];
  //   return Container(
  //     padding: EdgeInsets.all(10),
  //     child: Column(
  //       children: [
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.start,
  //           crossAxisAlignment: CrossAxisAlignment.center,
  //           children: [
  //             Expanded(
  //               child: CustomText(
  //                 text: 'Thông báo - Cập nhật mới',
  //                 bold: true,
  //                 size: 20,
  //               ),
  //             ),
  //             InkWell(
  //               onTap: () {
  //                 Get.to(() => NewsAllView());
  //               },
  //               child: Container(
  //                 decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(180),
  //                     border: Border.all(color: AppColors.primary, width: 2)),
  //                 padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
  //                 child: CustomText(
  //                   text: 'Xem tất cả',
  //                   color: AppColors.primary,
  //                   size: 14,
  //                 ),
  //               ),
  //             ),
  //           ],
  //         ),
  //         const SizedBox(
  //           height: 10,
  //         ),
  //         Container(
  //           decoration: BoxDecoration(
  //             color: Colors.white,
  //             borderRadius: BorderRadius.circular(12),
  //             border: Border.all(color: Colors.grey.shade300),
  //           ),
  //           padding: EdgeInsets.all(10),
  //           margin: EdgeInsets.only(top: 5),
  //           child: Column(
  //             children: [
  //               ...updateNotifications.take(3).map(
  //                     (notif) => Card(
  //                       color: AppColors.cardBackground,
  //                       elevation: 1.5,
  //                       margin: EdgeInsets.symmetric(vertical: 6),
  //                       shape: RoundedRectangleBorder(
  //                         borderRadius: BorderRadius.circular(10),
  //                       ),
  //                       child: ListTile(
  //                         leading: Icon(Icons.update, color: Colors.blue),
  //                         title: Text(notif['title'] ?? ""),
  //                         subtitle: Text(notif['description'] ?? ""),
  //                         trailing: IconButton(
  //                             onPressed: () {},
  //                             icon: Icon(
  //                               Icons.arrow_forward_ios,
  //                               size: 16,
  //                             )),
  //                       ),
  //                     ),
  //                   ),
  //             ],
  //           ),
  //         )
  //       ],
  //     ),
  //   );
  // }

  Widget _sectionHeaderRow(
      {required String title, required VoidCallback? onViewAll}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: CustomText(
              text: title,
              bold: true,
              size: 20,
            ),
          ),
          if (onViewAll != null)
            InkWell(
              onTap: onViewAll,
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(180),
                    border: Border.all(color: AppColors.primary, width: 2)),
                padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                child: CustomText(
                  text: 'Xem tất cả',
                  color: AppColors.primary,
                  size: 14,
                ),
              ),
            )
        ],
      ),
    );
  }

  Widget _bannerRow() {
    final width = MediaQuery.of(context).size.width;
    final List<Map<String, String>> mocBanners = [
      {
        "image":
            "https://cdn.builder.io/api/v1/image/assets/3c372df1d09f420992b2d762af53e9cb/1d236a2e27b54d7bc94b42b0ec339172cb50ed76?placeholderIfAbsent=true",
        "url": "https://acb.minvoicehcm.vn/"
      },
      {
        "image":
            "https://imgs.search.brave.com/nqS4oec127zrjbyejwG2hWYdsNG0pc-e2rO29sqg9lw/rs:fit:500:0:1:0/g:ce/aHR0cHM6Ly9raG9p/dG9hbnN0dWRpby5j/b20vd3AtY29udGVu/dC91cGxvYWRzLzIw/MjQvMTAvNy1DQUNI/LUNIVVAtQU5ILUJB/Ti1IQU5HLU5HSElO/LURPTi5qcGc",
        "url":
            "https://khoitoanstudio.com/7-cach-chup-anh-ban-hang-dep-de-ban-hang-nghin-don/"
      },
      {
        "image":
            "https://imgs.search.brave.com/uINvgcaJvJOI_oQvkFle3FH41jNxalv1XgQbf3L8WMw/rs:fit:500:0:1:0/g:ce/aHR0cHM6Ly9hc2lh/LTEuY29uc29sZS5z/dHJpbmdlZS5jb20v/dXBsb2FkL0lNR19V/QU5HVjNKM0hTRVNP/VDdZLmpwZw",
        "url": "https://base.vn/blog/ky-nang-ban-hang/"
      },
      {
        "image":
            "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTG8kp2fevA296FTUDkSOudiQFCbg4Y9m8tSQ&s",
        "url": "https://www.facebook.com/sobanhang.vn/"
      },
    ];
    return Padding(
      padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _sectionHeaderRow(title: "Bí kíp chốt đơn", onViewAll: null),
          CarouselSlider(
            options: CarouselOptions(
                height: width / 3, autoPlay: true, enlargeCenterPage: true),
            items: mocBanners.map((banner) {
              return Builder(builder: (BuildContext context) {
                return GestureDetector(
                  onTap: () async {
                    try {
                      final url = banner['url']!;
                      final uri = Uri.parse(url);

                      if (await canLaunchUrl(uri)) {
                        await launchUrl(uri,
                            mode: LaunchMode.externalApplication);
                      } else {
                        await launchUrl(uri,
                            mode: LaunchMode.externalApplication);
                      }
                    } catch (e) {
                      AppFunction.showError(
                          "Không thể mở liên kết. Vui lòng thử lại $e.");
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 5.0),
                    child: ImageCacheHelper.cachedBanner(
                      url: banner['image']!,
                      borderRadius: 10,
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              });
            }).toList(),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return Scaffold(
      appBar: AppBar(
        title: Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 0, 5),
            child: DecoratedBox(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(8)),
              child: DropdownButton(
                value: UserInfo.branchId,
                items: [
                  for (dynamic item in UserInfo.branches)
                    DropdownMenuItem(
                      value: item['BranchId'],
                      child: CustomText(text: item['BranchName']),
                    )
                ],
                onChanged: (value) {
                  UserInfo.branchId = int.parse(value.toString());
                  setState(() {});
                  final now = DateTime.now();
                  final date = now.toString().substring(0, 10);
                  revenueController.fetchRevenueData(
                      dateFrom: date, dateTo: date);
                },
                underline: Container(),
                isExpanded: true,
                padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
              ),
            )),
        backgroundColor: AppColors.primary,
        automaticallyImplyLeading: false,
        // leading: InkWell(
        //   onTap: () {
        //
        //   },
        //   child: Container(
        //     decoration: BoxDecoration(
        //       borderRadius: BorderRadius.circular(180)
        //     ),
        //     margin: EdgeInsets.fromLTRB(5, 0, 0, 5),
        //     clipBehavior: Clip.antiAlias,
        //     child: Image.asset('assets/images/general/assistant.jpg', width: 50, fit: BoxFit.cover,),
        //   ),
        // ),
        actions: [
          Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 5, 5),
            child: InkWell(
              onTap: () {
                Get.to(() => AccountView(
                      from: 'avatar',
                    ));
              },
              child: Container(
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(180)),
                // margin: EdgeInsets.all(5),
                clipBehavior: Clip.antiAlias,
                child: Image.asset('assets/images/general/avatar.png',
                    width: 50, fit: BoxFit.cover),
              ),
            ),
          )
        ],
      ),
      body: ListView(
        padding: EdgeInsets.zero,
        children: [
          //summary
          renderSummary(),
          _bannerRow(),

          // updateNotifications(),

          Column(
            children: [
              SizedBox(
                width: width > GlobalVar.maxWidth
                    ? GlobalVar.maxWidth
                    : double.infinity,
                child: renderMainButton(),
              ),
            ],
          ),
          renderNews(),
          // SizedBox(height: 10,)
        ],
      ),
      // renderItemMainButton('Danh mục',
      //     'assets/images/general/vi_voucher.png', CategoryView()),
      floatingActionButton: SpeedDial(
        icon: Icons.add,
        buttonSize: Size(56, 56), // Standard FAB size
        shape: CircleBorder(side: BorderSide(color: Colors.white, width: 2)),
        activeIcon: Icons.close,
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        spacing: 12,
        spaceBetweenChildren: 10,
        overlayOpacity: 0.3,
        animationDuration: Duration(milliseconds: 200),
        elevation: 8.0,
        children: [
          SpeedDialChild(
            child: Icon(
              Iconsax.category,
              color: AppColors.primary,
            ),
            label: 'Danh mục',
            labelStyle: TextStyle(
              fontWeight: FontWeight.bold,
            ),
            onTap: () => Get.to(() => ProductView(initialTab: 1)),
          ),
          SpeedDialChild(
            child: Icon(
              Iconsax.personalcard,
              color: AppColors.primary,
            ),
            label: 'Khách hàng',
            labelStyle: TextStyle(
              fontWeight: FontWeight.bold,
            ),
            onTap: () => Get.to(() => CustomerView()),
          ),
          SpeedDialChild(
            child: Icon(
              Iconsax.box,
              color: AppColors.primary,
            ),
            label: 'Sản phẩm',
            labelStyle: TextStyle(
              fontWeight: FontWeight.bold,
            ),
            onTap: () => Get.to(() => ProductView()),
          ),
          SpeedDialChild(
            child: Icon(
              Iconsax.calculator,
              color: AppColors.primary,
            ),
            label: 'Đơn Vị Tính',
            labelStyle: TextStyle(
              fontWeight: FontWeight.bold,
            ),
            onTap: () => Get.to(() => ProductView(initialTab: 2)),
          ),
        ],
      ),
    );
  }
}
