import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/left_arrow_painter.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class EmenuHomeView extends StatefulWidget {
  const EmenuHomeView({super.key});

  @override
  State<EmenuHomeView> createState() => _EmenuHomeViewState();
}

class _EmenuHomeViewState extends State<EmenuHomeView> {
  //variable
  List areas = [
    {'id': 1, 'name': 'Sân vườn'},
    {'id': 2, 'name': 'Tầng lửng'},
    {'id': 3, 'name': 'Lầu 1'},
    {'id': 4, 'name': 'Lầu 2'},
  ];
  int areaSelected = 1;
  List tables = [];

  //function
  @override
  void initState() {
    super.initState();
    for(int i = 0; i < 100; i++) {
      tables.add({
        'id': i + 1,
        'name': (i + 1).toString()
      });
    }
    setState(() {

    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget renderTopBar() {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
          color: AppColors.danger
      ),
      padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              Padding(
                padding: EdgeInsets.fromLTRB(0, 0, 10, 0),
                child: CustomBackButton(),
              ),
              DecoratedBox(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10)
                ),
                child: DropdownButton<int>(
                  value: areaSelected,
                  items: areas.map((item) {
                    return DropdownMenuItem<int>(
                      value: item['id'],
                      child: Text(item['name']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    areaSelected = int.parse(value.toString());
                    setState(() {

                    });
                  },
                  underline: Container(),
                  padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                ),
              ),
            ],
          ),
          InkWell(
            onTap: () {

            },
            child: Icon(Icons.change_circle_outlined, color: Colors.white, size: 40,),
          )
        ],
      ),
    );
  }

  Widget renderArea() {
    return Container(
      width: double.infinity,
      height: 60,
      color: Colors.grey.withValues(alpha: 0.25),
      padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for(var item in areas)
            InkWell(
              onTap: () {
                areaSelected = item['id'];
                setState(() {

                });
              },
              child: Container(
                width: 140,
                height: double.infinity,
                decoration: BoxDecoration(
                    color: item['id'] == areaSelected ? AppColors.danger : Colors.white,
                    borderRadius: BorderRadius.circular(10)
                ),
                margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                child: Center(
                  child: CustomText(text: item['name'], color: item['id'] == areaSelected ? Colors.white : Colors.black, bold: true,),
                ),
              ),
            )
        ],
      ),
    );
  }
  
  Widget renderTables() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(10),
        child: Wrap(
          spacing: 10, // khoảng cách giữa các ô ngang
          runSpacing: 10,
          children: [
            for(dynamic item in tables)
              Container(
                width: 120,
                height: 120,
                padding: EdgeInsets.all(10),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: 35,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: item['id'] == 1 ? AppColors.danger : Colors.black54,
                        borderRadius: BorderRadius.circular(10)
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      height: 35,
                      decoration: BoxDecoration(
                          color: item['id'] == 1 ? AppColors.danger : Colors.black54,
                          borderRadius: BorderRadius.circular(10)
                      ),
                    ),
                    Container(
                      width: 82,
                      height: 82,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(180),
                        color: item['id'] == 1 ? AppColors.danger : Colors.black,
                        border: Border.all(
                          width: 10,
                          color: Colors.white
                        )
                      ),
                      child: item['id'] == 1 ?
                      Center(
                        child: CustomText(text: item['name'], color: item['id'] == 1 ? Colors.white : Colors.black, size: 20, bold: true,),
                      ) :
                      Container(
                          width: 75,
                          height: 75,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(180),
                            color: Colors.white,
                              border: Border.all(
                                  width: 2,
                                  color: Colors.black
                              )
                          ),
                        child: Center(
                          child: CustomText(text: item['name'], size: 20, bold: true,),
                        ),
                      )
                    )
                  ],
                )
              )
          ],
        ),
      ),
    );
  }

  Widget renderSidebar() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        renderItemSidebar('1', 'Khách hàng: Minhcute', AppColors.danger, Colors.white),
        renderItemSidebar('2', 'Số lượng: 4 khách', Color.fromRGBO(209, 207, 207, 1), AppColors.danger),
        renderItemSidebar('3', 'Nhân viên: Tommy', Color.fromRGBO(99, 99, 99, 1), Colors.white),
        renderItemSidebar('4', 'Thời gian: 15:00 - 19:00', AppColors.danger, Colors.white),
        renderItemSidebar('5', 'Tổng tiền: 500.000', Color.fromRGBO(209, 207, 207, 1), AppColors.danger),
      ],
    );
  }

  Widget renderItemSidebar(index, content, Color color, Color textColor) {
    return Padding(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
              width: 25,
              height: 25,
              decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                      width: 1,
                      color: Colors.black
                  ),
                  borderRadius: BorderRadius.circular(180)
              ),
              margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
              child: Center(child: CustomText(text: index, bold: true,),)
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.centerLeft,
              children: [
                // Box chính
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(left: 19), // để trống chỗ cho mũi tên
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.white),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: CustomText(text: content, color: textColor, size: 20,),
                ),

                // Mũi tên
                Positioned(
                  left: 0,
                  child: CustomPaint(
                    painter: LeftArrowPainter(color: color),
                    size: Size(20, 20)
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        if (orientation == Orientation.portrait) {
          return Scaffold(
            body: Center(
              child: CustomText(text: 'VUI LÒNG XOAY NGANG MÀN HÌNH', size: 40, bold: true,),
            )
          );
        }

        return Scaffold(
          body: Container(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              children: [
                renderTopBar(),
                renderArea(),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            Container(
                              color: Colors.grey.withValues(alpha: 0.5),
                              padding: EdgeInsets.fromLTRB(0, 5, 0, 0),
                              child: Row(
                                children: [
                                  InkWell(
                                    onTap: () {

                                    },
                                    child: Container(
                                      width: 150,
                                      decoration: BoxDecoration(
                                          color: AppColors.danger,
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(15),
                                              topRight: Radius.circular(15)
                                          )
                                      ),
                                      child: Center(
                                        child: CustomText(text: 'Dinein', color: Colors.white, bold: true, size: 24,),
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {

                                    },
                                    child: Container(
                                      width: 150,
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(15),
                                              topRight: Radius.circular(15)
                                          )
                                      ),
                                      child: Center(
                                        child: CustomText(text: 'Pickup', color: AppColors.danger, bold: true, size: 24,),
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {

                                    },
                                    child: Container(
                                      width: 150,
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(15),
                                              topRight: Radius.circular(15)
                                          )
                                      ),
                                      child: Center(
                                        child: CustomText(text: 'Delivery', color: AppColors.danger, bold: true, size: 24,),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              height: 10,
                              color: AppColors.danger,
                            ),
                            Expanded(
                              child: renderTables(),
                            )
                          ],
                        ),
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.3,
                        height: double.infinity,
                        color: Colors.grey.withValues(alpha: 0.1),
                        padding: EdgeInsets.all(10),
                        child: renderSidebar(),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
