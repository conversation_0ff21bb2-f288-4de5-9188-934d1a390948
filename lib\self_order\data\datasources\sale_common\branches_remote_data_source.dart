import '../../../../core/constants/api_url.dart';
import '../../../../core/network/dio_client.dart';
import '../../models/sale_common/branches_response_model.dart';

abstract class BranchesRemoteDataSource {
  Future<List<BranchesResponseModel>> branches();
}

class BranchesRemoteDataSourceImpl implements BranchesRemoteDataSource {
  final DioClient dioClient;

  BranchesRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<BranchesResponseModel>> branches() async {
    try {
      final response = await dioClient.get(
        ApiUrl.branches,
      );
      return BranchesListResponseModel.fromJson(response.data).branches;
    } catch (e) {
      rethrow;
    }
  }
}