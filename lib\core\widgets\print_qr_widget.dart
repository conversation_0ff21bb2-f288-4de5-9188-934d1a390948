import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

final GlobalKey printQrContainer = GlobalKey();

class PrintQrWidget extends StatelessWidget {
  final String qrCode;
  final String total;

  const PrintQrWidget({
    super.key,
    required this.qrCode,
    required this.total,
  });

  @override
  Widget build(BuildContext context) {

    return RepaintBoundary(
      key: printQrContainer,
      child: Container(
        width: 288,
        color: Colors.white,
        padding: const EdgeInsets.all(5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            QrImageView(
              data: qrCode,
              version: QrVersions.auto,
              size: 250,
            ),
            SizedBox(height: 5),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Số tiền'),
                Text(total),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
