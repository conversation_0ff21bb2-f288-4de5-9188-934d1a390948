import '../../../domain/entities/menu/menu_entity.dart';
import '../../../domain/repositories/menu/menu_repository.dart';
import '../../datasources/e_menu/menu_local_data_source.dart';
import '../../datasources/e_menu/menu_remote_data_source.dart';
import '../../models/e_menu/menu_tree_sme_model.dart';

class MenuSMERepositoryImpl implements MenuSMERepository {
  final MenuSMERemoteDataSource remote;
  final MenuSMELocalDataSource local;

  MenuSMERepositoryImpl({required this.remote, required this.local});

  @override
  Future<MenuSMEEntity> getMenuTreeWithSync({bool forceUpdate = true}) async {
    final latestMenu = await remote.fetchMenuInfoSME();
    final cachedMenuTree = await local.getCachedMenuTreeSME();

    // Thêm flag forceUpdate để luôn cập nhật
    final shouldUpdate = forceUpdate ||
        cachedMenuTree == null ||
        cachedMenuTree.lastUpdatedAt != latestMenu['LastUpdatedAt'];

    if (shouldUpdate) {
      final newMenuTree = await remote.fetchMenuTreeSME();
      final model = MenuTreeSMEModel.fromJson(newMenuTree);
      await local.cacheMenuTreeSME(model);
      return MenuSMEEntity.fromJson(newMenuTree);
    }

    return MenuSMEEntity.fromJson(cachedMenuTree!.fullJson);
  }
}
