import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class CustomHeaderModal extends StatelessWidget {
  final String title;
  final Widget? append;
  const CustomHeaderModal({super.key, required this.title, this.append});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50,
      color: AppColors.primary,
      padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 10, 0),
            child: InkWell(
              onTap: () {
                Get.back();
              },
              child: Icon(Icons.close, color: AppColors.danger, size: 30,),
            ),
          ),
          Expanded(
            child: CustomText(text: title, color: Colors.white, bold: true,),
          ),
          if (append != null)
            append!,
        ],
      ),
    );
  }
}
