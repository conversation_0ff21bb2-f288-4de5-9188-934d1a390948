class CounterListResponseModel {
  final bool success;
  final int code;
  final String message;
  final List<CounterModel> counters;
  final dynamic errorDetail;

  CounterListResponseModel({
    required this.success,
    required this.code,
    required this.message,
    required this.counters,
    required this.errorDetail,
  });

  factory CounterListResponseModel.fromJson(Map<String, dynamic> json) {
    return CounterListResponseModel(
      success: json["Success"],
      code: json["Code"],
      message: json["Message"],
      counters: json["Result"] == null
          ? []
          : List<CounterModel>.from(
          json["Result"].map((x) => CounterModel.fromJson(x))),
      errorDetail: json["ErrorDetail"],
    );
  }
}


class CounterModel {
  final int counterId;
  final String counterName;

  CounterModel({
    required this.counterId,
    required this.counterName,
  });

  factory CounterModel.fromJson(Map<String, dynamic> json) {
    return CounterModel(
      counterId: json['CounterId'],
      counterName: json['CounterName'],
    );
  }
}
