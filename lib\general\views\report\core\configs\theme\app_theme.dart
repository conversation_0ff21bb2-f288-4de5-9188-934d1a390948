import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_style.dart';

class AppTheme {
  static final appTheme = ThemeData(
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.secondBackground,
      brightness: Brightness.light,
      snackBarTheme: const SnackBarThemeData(
          backgroundColor: AppColors.background,
          contentTextStyle: TextStyle(color: AppColors.text)),
      inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.secondBackground,
          hintStyle: PrimaryFont.bold.copyWith(
            color: AppColors.shadow,
      ),
      // TextStyle
      contentPadding: const EdgeInsets.all(16),
      border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: BorderSide.none),
      // Outline InputBorder
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: BorderSide.none) // Outline InputBorder
      ),
      // InputDecoration Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            elevation: 0,
            textStyle: PrimaryFont.bold.copyWith(
              color: AppColors.text,
              ),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(100))),
      ));
}
