import 'package:equatable/equatable.dart';

import '../../domain/branch/entities/branch.dart';
import '../../domain/report/entities/report.dart';

abstract class HomeState extends Equatable {
  const HomeState();
  @override
  List<Object?> get props => [];
}

class HomeInitial extends HomeState {}

class HomeLoading extends HomeState {}

class HomeLoaded extends HomeState {
  // Branch
  final BranchEntity? selectedBrand;
  final List<BranchEntity> branches;
  final List<int> selectedBrandIds;
  final List<String> selectedBrands;
  final String selectedTimeOption;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String selectedCategory;
  final bool isAllBranchesSelected;

  // By Dashboard
  final List<OverviewRevenueEntity> overviewRevenue;
  final List<BranchesRevenueEntity> topBranchesRevenue;
  final List<BranchesRevenueEntity> bottomBranchesRevenue;
  final List<Revenue7DaysEntity> report7Days;
  final List<TopBranchesRevenueByDateEntity> topBranchesRevenueByDate;
  final bool isLoadingOverviewRevenue;
  final bool isLoadingTopBottomBranchesRevenue;
  final bool isLoadingItemsRevenue;
  final bool isLoadingReport7Days;
  final bool isLoadingTopBranchesRevenueByDate;

  // By Items
  final List<ItemEntity> topItemsByTotalQuantity;
  final List<ItemEntity> topItemsByTotalAmount;
  final List<ItemEntity> bottomItems;
  final List<TopItemsRevenueByDateEntity> topItemsRevenueByDate;
  final bool isLoadingTopAndBottomItems;
  final bool isLoadingTopItemsRevenueByDate;

  // By category
  final List<CategoriesByAmountEntity> topCategoriesByQuantity;
  final List<CategoriesByAmountEntity> topCategoriesByAmount;
  final List<CategoriesByAmountEntity> bottomCategoriesByAmount;
  final List<TopCatsRevenueByDateEntity> topCatsRevenueByDate;
  final bool isLoadingTotalAmountCats;
  final bool isLoadingTopAndBottomCategoriesByAmount;
  final bool isLoadingTopCatsRevenueByDate;

  // By Payment
  final List<TotalPaymentAmountEntity> totalPaymentAmount;
  final bool isLoadingTotalPaymentAmount;
  final List<TotalPaymentAmountByDateEntity> totalPaymentAmountByDate;
  final bool isLoadingTotalPaymentAmountByDate;


  const HomeLoaded({
    required this.branches,
    this.selectedBrand,
    this.selectedBrandIds = const [],
    this.selectedBrands = const [],
    this.selectedTimeOption = "today",
    this.fromDate,
    this.toDate,
    this.selectedCategory = "dashboard",
    this.isAllBranchesSelected = false,
    this.overviewRevenue = const [],
    this.isLoadingOverviewRevenue = false,
    this.topBranchesRevenue = const [],
    this.bottomBranchesRevenue = const [],
    this.report7Days = const [],
    this.topItemsByTotalQuantity = const [],
    this.topItemsByTotalAmount = const [],
    this.bottomItems = const [],
    this.topCategoriesByQuantity = const [],
    this.topCategoriesByAmount = const [],
    this.bottomCategoriesByAmount = const [],
    this.isLoadingTopBottomBranchesRevenue = false,
    this.isLoadingItemsRevenue = false,
    this.isLoadingReport7Days = false,
    this.isLoadingTopAndBottomItems = false,
    this.isLoadingTotalAmountCats = false,
    this.isLoadingTopAndBottomCategoriesByAmount = false,
    this.topItemsRevenueByDate = const [],
    this.isLoadingTopItemsRevenueByDate = false,
    this.topBranchesRevenueByDate = const [],
    this.isLoadingTopBranchesRevenueByDate = false,
    this.topCatsRevenueByDate = const [],
    this.isLoadingTopCatsRevenueByDate = false,
    this.totalPaymentAmount = const [],
    this.totalPaymentAmountByDate = const [],
    this.isLoadingTotalPaymentAmountByDate = false,
    this.isLoadingTotalPaymentAmount = false,
  });

  @override
  List<Object?> get props => [
    // Branch
    selectedBrand,
    branches,
    selectedBrandIds,
    selectedBrands,
    selectedTimeOption,
    fromDate,
    toDate,
    selectedCategory,
    isAllBranchesSelected,

    // By Dashboard
    overviewRevenue,
    topBranchesRevenue,
    bottomBranchesRevenue,
    report7Days,
    topBranchesRevenueByDate,
    isLoadingOverviewRevenue,
    isLoadingTopBottomBranchesRevenue,
    isLoadingItemsRevenue,
    isLoadingReport7Days,
    isLoadingTopBranchesRevenueByDate,

    // By Items
    topItemsByTotalQuantity,
    topItemsByTotalAmount,
    bottomItems,
    topItemsRevenueByDate,
    isLoadingTopAndBottomItems,
    isLoadingTopItemsRevenueByDate,

    // By category
    topCategoriesByQuantity,
    topCategoriesByAmount,
    bottomCategoriesByAmount,
    topCatsRevenueByDate,
    isLoadingTotalAmountCats,
    isLoadingTopAndBottomCategoriesByAmount,
    isLoadingTopCatsRevenueByDate,

    // By Payment
    totalPaymentAmount,
    isLoadingTotalPaymentAmount,
    totalPaymentAmountByDate,
    isLoadingTotalPaymentAmountByDate,


  ];

  HomeLoaded copyWith({
    // Branch
    List<BranchEntity>? branches,
    BranchEntity? selectedBrand,
    List<int>? selectedBrandIds,
    List<String>? selectedBrands,
    String? selectedTimeOption,
    DateTime? fromDate,
    DateTime? toDate,
    String? selectedCategory,
    bool? isAllBranchesSelected,
    String? selectedProvince,

    // Dashboard
    List<OverviewRevenueEntity>? overviewRevenue,
    List<BranchesRevenueEntity>? topBranchesRevenue,
    List<BranchesRevenueEntity>? bottomBranchesRevenue,
    List<Revenue7DaysEntity>? report7Days,
    List<TopBranchesRevenueByDateEntity>? topBranchesRevenueByDate,
    bool? isLoadingOverviewRevenue,
    bool? isLoadingTopBottomBranchesRevenue,
    bool? isLoadingItemsRevenue,
    bool? isLoadingReport7Days,
    bool? isLoadingTopBranchesRevenueByDate,

    // By Item
    List<ItemEntity>? topItemsByTotalQuantity,
    List<ItemEntity>? topItemsByTotalAmount,
    List<ItemEntity>? bottomItems,
    List<TopItemsRevenueByDateEntity>? topItemsRevenueByDate,
    bool? isLoadingTopAndBottomItems,
    bool? isLoadingTopItemsRevenueByDate,

    // By Category
    List<CategoriesByAmountEntity>? topCategoriesByQuantity,
    List<CategoriesByAmountEntity>? topCategoriesByAmount,
    List<CategoriesByAmountEntity>? bottomCategoriesByAmount,
    List<TopCatsRevenueByDateEntity>? topCatsRevenueByDate,
    bool? isLoadingTotalAmountCats,
    bool? isLoadingTopAndBottomCategoriesByAmount,
    bool? isLoadingTopCatsRevenueByDate,

    // By Payment
    List<TotalPaymentAmountEntity>? totalPaymentAmount,
    List<TotalPaymentAmountByDateEntity>?totalPaymentAmountByDate,
    bool? isLoadingTotalPaymentAmount,
    bool? isLoadingTotalPaymentAmountByDate,

  }) {
    return HomeLoaded(
      // Branch
      selectedBrand: selectedBrand ?? this.selectedBrand,
      branches: branches ?? this.branches,
      selectedBrandIds: selectedBrandIds ?? this.selectedBrandIds,
      selectedBrands: selectedBrands ?? this.selectedBrands,
      selectedTimeOption: selectedTimeOption ?? this.selectedTimeOption,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      isAllBranchesSelected: isAllBranchesSelected ?? this.isAllBranchesSelected,
      // By Dashboard
      overviewRevenue: overviewRevenue ?? this.overviewRevenue,
      topBranchesRevenue: topBranchesRevenue ?? this.topBranchesRevenue,
      bottomBranchesRevenue: bottomBranchesRevenue ?? this.bottomBranchesRevenue,
      report7Days: report7Days ?? this.report7Days,
      topBranchesRevenueByDate: topBranchesRevenueByDate ?? this.topBranchesRevenueByDate,
      isLoadingOverviewRevenue: isLoadingOverviewRevenue ?? this.isLoadingOverviewRevenue,
      isLoadingTopBottomBranchesRevenue: isLoadingTopBottomBranchesRevenue ?? this.isLoadingTopBottomBranchesRevenue,
      isLoadingItemsRevenue: isLoadingItemsRevenue ?? this.isLoadingItemsRevenue,
      isLoadingReport7Days: isLoadingReport7Days ?? this.isLoadingReport7Days,
      isLoadingTopBranchesRevenueByDate: isLoadingTopBranchesRevenueByDate ?? this.isLoadingTopBranchesRevenueByDate,

      // By Items
      topItemsByTotalQuantity: topItemsByTotalQuantity ?? this.topItemsByTotalQuantity,
      topItemsByTotalAmount: topItemsByTotalAmount ?? this.topItemsByTotalAmount,
      bottomItems: bottomItems ?? this.bottomItems,
      topItemsRevenueByDate: topItemsRevenueByDate ?? this.topItemsRevenueByDate,
      isLoadingTopAndBottomItems: isLoadingTopAndBottomItems ?? this.isLoadingTopAndBottomItems,
      isLoadingTopItemsRevenueByDate: isLoadingTopItemsRevenueByDate ?? this.isLoadingTopItemsRevenueByDate,

      // By Category
      topCategoriesByQuantity: topCategoriesByQuantity ?? this.topCategoriesByQuantity,
      topCategoriesByAmount: topCategoriesByAmount ?? this.topCategoriesByAmount,
      bottomCategoriesByAmount: bottomCategoriesByAmount ?? this.bottomCategoriesByAmount,
      topCatsRevenueByDate: topCatsRevenueByDate ?? this.topCatsRevenueByDate,
      isLoadingTotalAmountCats: isLoadingTotalAmountCats ?? this.isLoadingTotalAmountCats,
      isLoadingTopAndBottomCategoriesByAmount: isLoadingTopAndBottomCategoriesByAmount ?? this.isLoadingTopAndBottomCategoriesByAmount,
      isLoadingTopCatsRevenueByDate: isLoadingTopCatsRevenueByDate ?? this.isLoadingTopCatsRevenueByDate,

      // By payment
      totalPaymentAmount: totalPaymentAmount ?? this.totalPaymentAmount,
      isLoadingTotalPaymentAmount: isLoadingTotalPaymentAmount ?? this.isLoadingTotalPaymentAmount,
      totalPaymentAmountByDate: totalPaymentAmountByDate ?? this.totalPaymentAmountByDate,
      isLoadingTotalPaymentAmountByDate: isLoadingTotalPaymentAmountByDate ?? this.isLoadingTotalPaymentAmountByDate,
    );
  }
}
