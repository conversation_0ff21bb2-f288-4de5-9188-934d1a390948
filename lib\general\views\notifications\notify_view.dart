import 'package:flutter/material.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/views/notifications/widget/tab1.dart';
import 'package:gls_self_order/general/views/notifications/widget/tab2.dart';
import 'package:gls_self_order/general/views/notifications/widget/tab3.dart';

import '../../../core/components/custom_title_app_bar.dart';

class NotifyView extends StatelessWidget {
  const NotifyView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: 3,
        child: Scaffold(
          appBar: AppBar(
            title: CustomTitleAppBar(title: 'Thông báo'),
            backgroundColor: AppColors.primary,
            automaticallyImplyLeading: false,
            centerTitle: true,
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(48),
              child: Container(
                color: AppColors.background,
                child: const Tab<PERSON><PERSON>(
                  labelColor: AppColors.primary,
                  // unselectedLabelColor: Colors.grey,
                  indicatorColor:Colors.blue,
                  tabs: [
                    Tab(text: 'Thông báo'),
                    Tab(text: 'Tin tức'),
                    Tab(text: 'Khuyến mãi'),
                  ],
                ),
              ),
            ),
          ),
          body: const TabBarView(
            children: [
              Tab1(),
              Tab2(),
              Tab3(),
            ],
          ),
        ));
  }
}
