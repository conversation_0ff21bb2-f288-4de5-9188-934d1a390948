import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../../../../core/widgets/default_button.dart';
import '../../domain/branch/entities/branch.dart';
import '../bloc/home_cubit.dart';
import '../bloc/home_state.dart';
import 'package:intl/intl.dart';

Widget branchSelection(BuildContext context, HomeLoaded state, double width) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: width * 0.015),
    child: Row(
      children: [
        Expanded(
          child: BrandDropdown(
            items: state.branches,
            selectedBrand: state.selectedBrand,
          ),
        ),
        SizedBox(width: width * 0.03),
        Expanded(
          child: BranchDropdown(items: state.branches,selectedBrand: state.selectedBrand),
        ),
      ],
    ),
  );
}
// Brands
class BrandDropdown extends StatelessWidget {
  final List<BranchEntity> items;
  final BranchEntity? selectedBrand;
  final Function(BranchEntity)? onBrandSelected;

  const BrandDropdown({
    required this.items,
    this.selectedBrand,
    this.onBrandSelected,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;

    return GestureDetector(
      onTap: () => _showBrandSelectionSheet(context),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "brand".tr,
              style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.04
              ),
            ),
            SizedBox(height: width * 0.015),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: BlocBuilder<HomeCubit, HomeState>(
                    builder: (context, state) {
                      if (state is! HomeLoaded) return Container();
                      return Text(
                        state.selectedBrand?.branchName ?? "no_selection".tr,
                        style: PrimaryFont.bold.copyWith(
                          color: AppColors.text,
                          fontSize: width * 0.04,
                        ),
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: AppColors.text,
                  size: width * 0.06,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  void _showBrandSelectionSheet(BuildContext context) {
    final homeCubit = context.read<HomeCubit>();
    double width = MediaQuery.of(context).size.width;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => BlocProvider.value(
        value: homeCubit,
        child: Container(
          color: AppColors.background,
          height: MediaQuery.of(context).size.height * 0.9,
          width: width,
          padding: EdgeInsets.all(width * 0.03),
          child: BranchSelectionSheet(items: items),
        ),
      ),
    );
  }
}
// Branches
class BranchDropdown extends StatelessWidget {
  final List<BranchEntity> items;
  final BranchEntity? selectedBrand;

  const BranchDropdown({
    required this.items,
    this.selectedBrand,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        if (state is! HomeLoaded) return Container();
        final branchesOfSelectedBrand = items.where((branch) {
          return branch.branchParentId == state.selectedBrand?.branchId;
        }).toList();
        final isAllSelected = state.selectedBrands.length == branchesOfSelectedBrand.length;
        return GestureDetector(
          onTap: () => _showBrandSelectionSheet(context),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.03),
            decoration: cardDecoration(width * 0.03, AppColors.background),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "branch".tr,
                  style: PrimaryFont.regular.copyWith(
                      color: AppColors.text, fontSize: width * 0.04),
                ),
                SizedBox(
                  height: width * 0.015,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        state.selectedBrands.isEmpty
                            ? "no_selection".tr
                            : (state.selectedBrands.length == branchesOfSelectedBrand.length || isAllSelected)
                            ? "all".tr
                            : state.selectedBrands.join(", "),
                        style: PrimaryFont.bold.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.04
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(
                      Icons.arrow_drop_down,
                      color: AppColors.text,
                      size: width * 0.06,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  void _showBrandSelectionSheet(BuildContext context) {
    final homeCubit = context.read<HomeCubit>();
    double width = MediaQuery.of(context).size.width;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => BlocProvider.value(
        value: homeCubit,
        child: Container(
          color: AppColors.background,
          height: MediaQuery.of(context).size.height * 0.95,
          width: width,
          padding: EdgeInsets.all(width * 0.03),
          child: BranchSelectionSheet(items: items),
        ),
      ),
    );
  }
}
// Branch list
class BranchSelectionSheet extends StatefulWidget {
  final List<BranchEntity> items;

  const BranchSelectionSheet({required this.items, super.key});

  @override
  _BranchSelectionSheetState createState() => _BranchSelectionSheetState();
}

class _BranchSelectionSheetState extends State<BranchSelectionSheet> {
  TextEditingController searchController = TextEditingController();
  String? selectedProvince;

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        if (state is! HomeLoaded) return Container();

        final branchesOfSelectedBrand = widget.items.where((branch) {
          return branch.branchParentId == state.selectedBrand?.branchId;
        }).toList();

        final filteredBranches = branchesOfSelectedBrand.where((branch) {
          final searchQuery = searchController.text.toLowerCase();
          final branchCode = branch.branchId?.toString().toLowerCase() ?? "";
          final branchName = branch.branchName?.toLowerCase() ?? "";
          return branchCode.contains(searchQuery) || branchName.contains(searchQuery);
        }).toList();

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "select_branch_time".tr,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: width * 0.045),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: AppColors.text, size: width * 0.06),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            SizedBox(height: width * 0.05),
            DropdownButtonFormField<BranchEntity>(
              dropdownColor: AppColors.background,
              value: state.selectedBrand,
              isExpanded: true,
              items: widget.items
                  .where((brand) => brand.branchParentId == -1)
                  .map((brand) {
                return DropdownMenuItem<BranchEntity>(
                  value: brand,
                  child: Text(
                    state.selectedBrand?.branchName ?? "unknown_brand".tr,
                    style: PrimaryFont.regular.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (BranchEntity? newValue) {
                if (newValue != null) {
                  context.read<HomeCubit>().updateSelectedBrand(newValue);
                  setState(() {
                    selectedProvince = null;
                  });
                }
              },
              decoration: InputDecoration(
                fillColor: AppColors.background,
                border: OutlineInputBorder(),
                labelText: "Chọn thương hiệu".tr,
                labelStyle: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.04,
                ),
                filled: true,
              ),
            ),
            SizedBox(height: width * 0.03),
            // 🔹 Dropdown chọn thời gian
            DropdownButtonFormField<String>(
              dropdownColor: AppColors.background,
              value: [
                "today", "yesterday", "this_week", "last_week",
                "this_month", "last_month", "this_year", "last_year", "custom"
              ].contains(state.selectedTimeOption)
                  ? state.selectedTimeOption
                  : "today",
              items: [
                "today", "yesterday", "this_week", "last_week",
                "this_month", "last_month", "this_year", "last_year", "custom"
              ].map((optionKey) {
                return DropdownMenuItem(
                  value: optionKey,
                  child: Text(
                    optionKey.tr,
                    style: PrimaryFont.regular.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  context.read<HomeCubit>().updateTimeFilter(value);
                }
              },
              decoration: InputDecoration(
                fillColor: AppColors.background,
                border: OutlineInputBorder(),
                labelText: "select_time".tr,
                labelStyle: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.04,
                ),
                filled: true,
              ),
            ),

            SizedBox(height: width * 0.03),

            Row(
              children: [
                Expanded(
                  child: _buildDatePicker(context, width, "from_date", state.fromDate, (date) {
                    context.read<HomeCubit>().updateTimeFilter("custom", from: date);
                  }),
                ),
                SizedBox(width: width * 0.03),
                Expanded(
                  child: _buildDatePicker(context, width, "to_date", state.toDate, (date) {
                    context.read<HomeCubit>().updateTimeFilter("custom", to: date);
                  }),
                ),
              ],
            ),
            SizedBox(height: width * 0.03),
            Container(
              decoration: BoxDecoration(
                color: AppColors.secondBackground,
                borderRadius: BorderRadius.circular(width * 0.0185),
              ),
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.03),
                    child: Icon(Icons.search, size: width * 0.06, color: AppColors.text),
                  ),
                  Expanded(
                    child: TextField(
                      controller: searchController,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: "search_branch".tr,
                        hintStyle: PrimaryFont.regular.copyWith(
                          color: AppColors.text.withOpacity(0.6),
                          fontSize: width * 0.04,
                        ),
                      ),
                      onChanged: (value) => setState(() {}),
                      style: PrimaryFont.regular.copyWith(
                        color: AppColors.text,
                        fontSize: width * 0.04,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            CheckboxListTile(
              title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "select_all".tr,
                    style: PrimaryFont.regular.copyWith(
                        color: AppColors.text,
                        fontSize: width * 0.04
                    ),
                  ),
                  Text(
                    "${state.selectedBrands.length} / ${branchesOfSelectedBrand.length}",
                    style: PrimaryFont.bold.copyWith(
                        color: AppColors.primary,
                        fontSize: width * 0.04
                    ),
                  ),
                ],
              ),
              activeColor: AppColors.primary,
              value: state.selectedBrands.length == branchesOfSelectedBrand.length &&
                  branchesOfSelectedBrand.isNotEmpty,
              controlAffinity: ListTileControlAffinity.leading,
              onChanged: (bool? checked) {
                context.read<HomeCubit>().toggleSelectAllBrands(
                    branchesOfSelectedBrand,
                    checked ?? false
                );
              },
              contentPadding: EdgeInsets.only(left: width * 0.015),
            ),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: SizedBox(
                    width: width,
                    child: DataTable(
                      columnSpacing: 0,
                      horizontalMargin: 0,
                      headingRowHeight: width * 0.1,
                      dataRowMinHeight: width * 0.1,
                      dataRowMaxHeight: double.infinity,
                      border: TableBorder.all(color: AppColors.shadow),
                      columns: [
                        DataColumn(
                          headingRowAlignment: MainAxisAlignment.center,
                          label: Text(
                            "#",
                            style: PrimaryFont.bold.copyWith(color: AppColors.text, fontSize: width * 0.04),
                          ),
                        ),
                        DataColumn(
                          headingRowAlignment: MainAxisAlignment.center,
                          label: Text(
                            "branch_code".tr,
                            style: PrimaryFont.bold.copyWith(color: AppColors.text, fontSize: width * 0.04),
                          ),
                        ),
                        DataColumn(
                          headingRowAlignment: MainAxisAlignment.center,
                          label: Text(
                            "branch_name".tr,
                            style: PrimaryFont.bold.copyWith(color: AppColors.text, fontSize: width * 0.04),
                          ),
                        ),
                      ],
                      rows: filteredBranches.map((branch) {
                        final isSelected = state.selectedBrands.contains(branch.branchName);

                        return DataRow(
                          cells: [
                            DataCell(
                              Center(
                                child: Checkbox(
                                  value: isSelected,
                                  activeColor: AppColors.primary,
                                  onChanged: (bool? checked) {
                                    context.read<HomeCubit>().toggleBrandSelection(branch.branchId!, branch.branchName!);
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Center(
                                child: Text(branch.branchId?.toString() ?? "N/A",
                                    style: PrimaryFont.regular.copyWith(fontSize: width * 0.04, color: AppColors.text)),
                              ),
                            ),
                            DataCell(
                              SizedBox(
                                width: width * 0.55,
                                child: Container(
                                  constraints: BoxConstraints(minWidth: width * 0.4, maxWidth: width * 0.8),
                                  padding: EdgeInsets.only(left: width * 0.03),
                                  child: Text(
                                    branch.branchName ?? "branch_name".tr,
                                    style: PrimaryFont.regular.copyWith(fontSize: width * 0.04, color: AppColors.text),
                                    softWrap: true,
                                    overflow: TextOverflow.visible,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    )

                ),
              ),
            ),

            SizedBox(height: width * 0.03),

            DefaultButton(
              onPress: () {
                final homeCubit = context.read<HomeCubit>();
                if (homeCubit.state is HomeLoaded) {
                  final currentState = homeCubit.state as HomeLoaded;
                  // Kiểm tra nếu không có chi nhánh nào được chọn
                  if (currentState.selectedBrands.isEmpty) {
                    // Hiển thị thông báo lỗi
                    //toastInfo(msg: "Vui lòng chọn ít nhất một nhánh", width * 0.05);
                    return;
                  }
                  homeCubit.fetchData(
                    context,
                    currentState.selectedBrands
                        .map<int?>((e) => int.tryParse(e))
                        .whereType<int>()
                        .toList(),
                    currentState.fromDate ?? DateTime.now(),
                    currentState.toDate ?? DateTime.now(),
                  );

                }
                Navigator.pop(context);
              },

              title: "fetch_data".tr,
              titleStyle: PrimaryFont.bold.copyWith(
                  color: AppColors.background,
                  fontSize: width * 0.04
              ),
              color: AppColors.primary,
              widthPercentage: 0.3,
              borderRadius: width * 0.065,
              heightPercentage: 0.08,
            ),
          ],
        );
      },
    );
  }
  Widget _buildDatePicker(
      BuildContext context, double width, String labelKey,
      DateTime? date, Function(DateTime) onDateSelected) {
    DateTime displayDate = date ?? DateTime.now();
    return InkWell(
      onTap: () async {
        DateTime? pickedDate = await showDatePicker(
          context: context,
          initialDate: displayDate,
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
          builder: (context, child) {
            return Theme(
              data: ThemeData.light().copyWith(
                primaryColor: AppColors.primary,
                hintColor: AppColors.primaryLight,
                colorScheme: ColorScheme.light(primary: AppColors.primary),
                buttonTheme: ButtonThemeData(textTheme: ButtonTextTheme.primary),
              ),
              child: child!,
            );
          },
        );

        if (pickedDate != null) {
          onDateSelected(pickedDate);
        }

      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: labelKey.tr,
          suffixIcon: Icon(Icons.calendar_month_sharp, color: AppColors.text, size: width * 0.06,),
        ),
        child: Text(
          DateFormat('dd/MM/yyyy').format(displayDate),
          style: PrimaryFont.regular.copyWith(
            color: AppColors.text,
            fontSize: width * 0.04,
          ),
        ),
      ),
    );
  }
}
