import 'package:flutter/material.dart';
class CustomText extends StatelessWidget {
  final String text;
  final bool bold;
  final bool italic;
  final double size;
  final Color color;
  final int? maxLines;
  final TextAlign? textAlign;
  final String align;
  const CustomText({
    super.key,
    required this.text,
    this.bold = false,
    this.italic = false,
    this.size = 16,
    this.color = Colors.black,
    this.maxLines = 1, // mặc định 1 dòng
    this.textAlign,
    this.align = 'left'
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      maxLines: maxLines == 0 ? null : maxLines, // 0 nghĩa là không giới hạn dòng
      overflow: maxLines != null && maxLines! > 0 ? TextOverflow.ellipsis : null,
      softWrap: true, // Cho phép text tự động xuống dòng
      textAlign: align == 'center' ? TextAlign.center : (align == 'right' ? TextAlign.right : TextAlign.left),
      style: TextStyle(
        color: color,
        fontSize: size,
        fontWeight: bold ? FontWeight.bold : FontWeight.normal,
        fontStyle: italic ? FontStyle.italic : FontStyle.normal,
        fontFamily: 'Roboto',
      ),
    );
  }
}
