import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';

class ChangePasswordView extends StatefulWidget {
  const ChangePasswordView({super.key});

  @override
  State<ChangePasswordView> createState() => _ChangePasswordViewState();
}

class _ChangePasswordViewState extends State<ChangePasswordView> {
  //variable
  AuthController authController = Get.find();
  TextEditingController oldPasswordController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();

  //function
  save() async {
    AppFunction.showLoading();
    bool success = await authController.changePassword(
      oldPasswordController.text,
      newPasswordController.text,
      confirmPasswordController.text
    );
    if (success) {
      UserInfo.password = newPasswordController.text;
      bool useBio = await authController.checkUseBio();
      if (useBio) {
        await authController.saveDataBio(UserInfo.username, UserInfo.password);
      }
      Get.back();
    }
    AppFunction.hideLoading();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Đổi mật khẩu'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [

        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(10),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  CustomTextField(
                    controller: oldPasswordController,
                    label: 'Mật khẩu cũ',
                    required: true,
                    secure: true,
                  ),
                  CustomTextField(
                    controller: newPasswordController,
                    label: 'Mật khẩu mới',
                    required: true,
                    secure: true,
                  ),
                  CustomTextField(
                    controller: confirmPasswordController,
                    label: 'Nhập lại mật khẩu',
                    required: true,
                    secure: true,
                  )
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
              height: 50,
              child: CustomButton(text: 'Đổi mật khẩu', onTap: () {
                save();
              }, color: AppColors.primary),
            )
          ],
        ),
      ),
    );
  }
}
