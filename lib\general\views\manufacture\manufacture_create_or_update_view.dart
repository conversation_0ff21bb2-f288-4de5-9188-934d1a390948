import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:intl/intl.dart';

class ManufactureCreateOrUpdateView extends StatefulWidget {
  final dynamic item;
  const ManufactureCreateOrUpdateView({super.key, this.item});

  @override
  State<ManufactureCreateOrUpdateView> createState() => _ManufactureCreateOrUpdateViewState();
}

class _ManufactureCreateOrUpdateViewState extends State<ManufactureCreateOrUpdateView> {
  //variable
  CustomerController customerController = Get.find();
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  
  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    // if (widget.item != null) {
    //   dynamic item = await customerController.getDetail(widget.item['CustomerNo']);
    //   if (item != null) {
    //     nameController.text = item['FullName'] ?? '';
    //     phoneController.text = item['PhoneNumber'] ?? '';
    //     if (item['Birthday'] != null) {
    //       dateOfBirth = DateTime.parse(item['Birthday']);
    //       dobController.text = AppFunction.formatDateNoTime(item['Birthday']);
    //     }
    //     if (item['Gender'] != null && item['Gender'] != 0) {
    //       gender = item['Gender'];
    //     }
    //     addressController.text = item['CustomerAdress'] ?? '';
    //     setState(() {
    //
    //     });
    //   }
    // }
  }

  
  save() async {
    AppFunction.showLoading();
    if (widget.item != null) {
      // bool success = await customerController.postUpdate(
      //     widget.item['CustomerId'],
      //     nameController.text,
      //     phoneController.text,
      //     dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth!) : null,
      //     gender,
      //     addressController.text
      // );
      // if (success) {
      //   Get.back(result: success);
      // }
    }
    else {
      // int customerId = await customerController.postCreate(
      //     nameController.text,
      //     phoneController.text,
      //     dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth!) : null,
      //     gender,
      //     addressController.text
      // );
      // if (customerId != 0) {
      //   Get.back(result: customerId);
      // }
    }
    AppFunction.hideLoading();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: widget.item != null ? 'Chỉnh sửa nhà cung cấp' : 'Thêm nhà cung cấp'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            CustomTextField(
              controller: nameController,
              label: 'Tên nhà cung cấp',
              required: true,
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Số điện thoại',
              required: true,
            ),
            CustomTextField(
              controller: phoneController,
              label: 'Email',
            ),
            CustomTextField(
              controller: addressController,
              label: 'Địa chỉ',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }
}
