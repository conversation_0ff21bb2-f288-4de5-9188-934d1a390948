import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

import '../../../../core/theme/app_colors.dart';

import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class TermsPdfView extends StatefulWidget {
  const TermsPdfView({super.key});

  @override
  State<TermsPdfView> createState() => _TermsPdfViewState();
}

class _TermsPdfViewState extends State<TermsPdfView> {
  String? localPath;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    try {
      // 1. Lấy file từ assets
      final ByteData data = await rootBundle.load('assets/pdfs/terms.pdf');

      // 2. Tạo file tạm
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/terms.pdf');

      // 3. Ghi dữ liệu vào file tạm
      await tempFile.writeAsBytes(data.buffer.asUint8List(), flush: true);

      setState(() {
        localPath = tempFile.path;
        _isLoading = false;
      });
    } catch (e) {
      print("Error loading PDF: $e");
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Điều khoản và điều kiện'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : localPath != null
          ? PDFView(
        filePath: localPath,
        enableSwipe: true,
        swipeHorizontal: false,
        autoSpacing: true,
        onError: (error) {
          print(error.toString());
        },
        onPageError: (page, error) {
          print('$page: ${error.toString()}');
        },
      )
          : const Center(
        child: Text('Không thể tải tài liệu',
            style: TextStyle(fontSize: 16)),
      ),
    );
  }
}
