import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/components/reusable_filter_widget.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/order_controller.dart';
import 'package:gls_self_order/general/controllers/payment_controller.dart';
import 'package:gls_self_order/general/views/order/order_detail_view.dart';
import 'package:gls_self_order/general/views/order/widgets/order_row_info.dart';
import 'package:intl/intl.dart';

class PaymentView extends StatefulWidget {
  const PaymentView({super.key});

  @override
  State<PaymentView> createState() => _PaymentViewState();
}

class _PaymentViewState extends State<PaymentView> {
  //variable
  PaymentController paymentController = Get.find();
  OrderController orderController = Get.find();
  TextEditingController searchController = TextEditingController();
  List payments = [];

  // Filter data
  FilterData filterData = FilterData(
    dateFrom: DateTime.now(),
    dateTo: DateTime.now(),
    // dateFrom: DateTime(DateTime.now().year, DateTime.now().month, 1),
    // dateTo: DateTime(DateTime.now().year, DateTime.now().month + 1, 1)
    //     .subtract(Duration(days: 1)),
    viewType: 'payment',
    // Đặt mặc định cho payment
    paymentMethodSelected: '',
    paymentMethodName: 'Tất cả',
    statusSelected: '',
    statusName: 'Tất cả',
  );

  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  getData() async {
    AppFunction.showLoading();
    await getList();
    AppFunction.hideLoading();
    setState(() {});
  }

  getList() async {
    payments = await paymentController.getList(
        DateFormat('yyyy-MM-dd').format(filterData.dateFrom),
        DateFormat('yyyy-MM-dd').format(filterData.dateTo),
        filterData.paymentMethodSelected.isEmpty
            ? 'credit'
            : filterData.paymentMethodSelected);
  }

  void _showDateFilterModal() {
    // Sử dụng static method từ ReusableFilterWidget
    ReusableFilterWidget.showDateFilterModal(
      context,
      filterData,
      (newFilterData) {
        setState(() {
          filterData = newFilterData;
        });
        getData(); // Auto refresh data when filter changes
      },
    );
  }

  Widget renderItemPayment(item) {
    return GestureDetector(
      onTap: () {
        Get.to(() => OrderDetailView(code: item['OrderCode'] ?? ''));
      },
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
                color: Colors.grey.withValues(alpha: 0.05),
                spreadRadius: 0,
                blurRadius: 1,
                offset: Offset(0, 3)),
          ],
        ),
        padding: EdgeInsets.all(10),
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
        child: ListView.separated(
          shrinkWrap: true,
          // Ensures ListView takes only the space it needs
          physics: const NeverScrollableScrollPhysics(),
          // Disables scrolling if not needed
          itemCount: 6,
          separatorBuilder: (context, index) => const SizedBox(height: 10),
          itemBuilder: (context, index) {
            return [
              OrderRowInfo(
                label: 'Mã giao dịch:',
                value: item['TransCode'] ?? '',
              ),
              OrderRowInfo(
                label: 'Thời gian: ',
                value: AppFunction.formatDateWithTime(item['RealTransDate']),
              ),
              OrderRowInfo(
                label: 'Số tiền: ',
                value: AppFunction.formatMoney(item['OrderAmount'] ?? '0'),
              ),
              OrderRowInfo(
                label: 'Phương thức thanh toán: ',
                value: item['TransChannel'] ?? '',
              ),
              OrderRowInfo(
                label: 'Đơn hàng: ',
                value: item['OrderCode'] ?? 'Đang cập nhật thông tin',
              ),
              OrderRowInfo(
                label: 'Chi tiết giao dịch:',
                value: item['TransactionContent'] ?? '',
                maxLines: 5,
                expandable: true,
              ),
            ][index];
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Thanh toán'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: _showDateFilterModal,
            icon: Icon(
              Icons.filter_list,
              color: Colors.white,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          if (payments.isNotEmpty)
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  for (dynamic item in payments) renderItemPayment(item),
                ],
              ),
            ),
          if (payments.isEmpty)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomText(
                      text: 'Chưa có dữ liệu',
                      bold: true,
                    ),
                  ],
                ),
              ),
            )
        ],
      ),
    );
  }
}
