import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';
import 'package:gls_self_order/general/views/home_main_view.dart';

import '../../../core/components/custom_text.dart';

class ApplyEvoucherView extends StatefulWidget {
  const ApplyEvoucherView({super.key});

  @override
  State<ApplyEvoucherView> createState() => _ApplyEvoucherViewState();
}

class _ApplyEvoucherViewState extends State<ApplyEvoucherView> {
  //variable
  AuthController authController = Get.find();
  TextEditingController voucherController = TextEditingController();
  bool showAddKeyForm = false;

  //function

  apply() async {
    AppFunction.showLoading();
    bool success =
        await authController.postActiveLicense(voucherController.text.trim());
    if (success) {
      await authController.getUserInfo();
      setState(() {
        showAddKeyForm = false;
      });
      voucherController.clear();
      // Kích hoạt thành công → Chuyển thẳng đến home (bỏ qua login)
      Get.offAll(() => HomeMainView());
    }
    AppFunction.hideLoading();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Áp dụng e-voucher ACB'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          if (!showAddKeyForm && UserInfo.licenseKey.isNotEmpty)
            IconButton(
              icon: Icon(Icons.add, color: Colors.white),
              onPressed: () {
                setState(() {
                  showAddKeyForm = true;
                });
              },
            ),
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(10),
        child: Column(
          children: [
            if (showAddKeyForm || UserInfo.licenseKey.isEmpty) ...[
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    CustomTextField(
                      controller: voucherController,
                      label: 'Mã kích hoạt',
                      required: true,
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                height: 50,
                child: CustomButton(
                    text: 'Áp dụng',
                    onTap: () {
                      apply();
                    },
                    color: AppColors.primary),
              ),
            ],
            if (!showAddKeyForm && UserInfo.licenseKey.isNotEmpty) ...[
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    _buildReadOnlyField("Mã License", UserInfo.licenseKey),
                    const SizedBox(height: 8),
                    _buildReadOnlyField("Còn lại", "${UserInfo.daysLeft} ngày"),
                    const SizedBox(height: 8),
                    _buildReadOnlyField(
                        "Ngày bắt đầu", _formatDate(UserInfo.startDate)),
                    const SizedBox(height: 8),
                    _buildReadOnlyField(
                        "Ngày hết hạn", _formatDate(UserInfo.expiryDate)),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReadOnlyField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: label,
          size: 16,
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Text(value, style: const TextStyle(fontSize: 16)),
        ),
      ],
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}";
  }
}
