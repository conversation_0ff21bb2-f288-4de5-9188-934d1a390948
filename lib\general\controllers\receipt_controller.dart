import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';

class ReceiptController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();

  //function
  getList(code, groupId, itemId, paymentMethodId, customerName, from, to, type) async {
    List list = [];

    dynamic body = {
      "ReceiptCode": code.isEmpty ? null : code,
      "ReceiptType": type,
      "ReceiptGroupId": groupId != 0 ? groupId : null,
      "ReceiptItemId": itemId != 0 ? itemId : null,
      "PaymentMethodId": paymentMethodId != 0 ? paymentMethodId : null,
      "CustomerName": customerName,
      "FromDate": from,
      "ToDate": to
    };

    try {
      final response = await dioClient.post(
          SmeUrl.receiptList,
          data: body
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
    }

    return list;
  }

  getDetail(id) async {
    dynamic item;
    try {
      final response = await dioClient.get(
        '${SmeUrl.detailReceipt}?Id=$id',
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        item = data['Result'];
      }
    } catch (e) {

    }
    return item;
  }

  postCreateOrUpdate(
      id,
      userId,
      date,
      customerId,
      groupId,
      currencyType,
      rate,
      itemId,
      amount,
      paymentMethodId,
      note,
      customerName,
      customerPhone,
      customerAddress,
      type
  ) async {
    int receiptId = 0;

    if (groupId == 0) {
      AppFunction.showError('Vui lòng chọn danh mục');
      return receiptId;
    }

    if (itemId == 0) {
      AppFunction.showError('Vui lòng chọn khoản mục');
      return receiptId;
    }

    if (paymentMethodId == 0) {
      AppFunction.showError('Vui lòng chọn phương thức thanh toán');
      return receiptId;
    }

    if (amount.isEmpty) {
      AppFunction.showError('Vui lòng nhập số tiền');
      return receiptId;
    }

    dynamic body = {
      "Id": id,
      "DocumentId": 1,
      "DocumentNo": "",
      "Amount": amount,
      "Note": note,
      "ReceiptType": type,
      "ReceiptGroupId": groupId,
      "ReceiptItemId": itemId,
      "ReceiptDate": date,
      "PaymentMethodId": paymentMethodId,
      "CustomerId": customerId != 0 ? customerId : null,
      "CustomerName": customerName,
      "CustomerPhone": customerPhone,
      "CustomerAddress": customerAddress,
      "CreatedById": userId,
    };

    try {
      final response = await dioClient.post(
          SmeUrl.createOrUpdateReceipt,
          data: body
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        receiptId = data['Result']['Id'];
        AppFunction.showSuccess(data['Message']);
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
    }
    return receiptId;
  }

  postDelete(id) async {
    try {
      final response = await dioClient.delete(
        '${SmeUrl.deleteReceipt}?Id=$id',
      );
      dynamic data = response;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      }
      else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }
}