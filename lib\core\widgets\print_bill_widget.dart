import 'package:flutter/material.dart';

final GlobalKey previewContainer = GlobalKey();

class PrintBillWidget extends StatelessWidget {
  final String billCode;
  final String customerName;
  final String dateTime;
  final String total;
  final String sub_total;
  final String vat;
  final List items;

  const PrintBillWidget({
    super.key,
    required this.billCode,
    required this.customerName,
    required this.dateTime,
    required this.total,
    required this.items,
    required this.sub_total,
    required this.vat
  });

  @override
  Widget build(BuildContext context) {

    return RepaintBoundary(
      key: previewContainer,
      child: Container(
        width: 288,
        color: Colors.white,
        padding: const EdgeInsets.all(5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('HOÁ ĐƠN THANH TOÁN', style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),)
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Số: $billCode', textAlign: TextAlign.center),
              ],
            ),
            SizedBox(height: 5),
            Text('Ngày: $dateTime', textAlign: TextAlign.left),
            Text('KH: $customerName', textAlign: TextAlign.left),
            SizedBox(height: 5),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Tên món'),
                Text('SL'),
                Text('Đơn giá'),
                Text('Thành tiền'),
              ],
            ),
            Divider(),
            ...items.map((item) => Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(item['name']),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('              '),
                    Text('${item['qty']}'),
                    Text('${item['unit_price']}'),
                    Text('${item['total']}'),
                  ],
                )
              ],
            )),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Thành tiền', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(sub_total, style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('VAT', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(vat, style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Tổng thanh toán', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(total, style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
