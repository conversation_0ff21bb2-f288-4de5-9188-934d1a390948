import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:graphic/graphic.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../common/helper/format_helper.dart';
import '../../domain/report/entities/report.dart';

class Report7DaysChart extends StatelessWidget {
  final double width;
  final List<Revenue7DaysEntity> report7Days;

  const Report7DaysChart({
    super.key,
    required this.width,
    required this.report7Days,
  });

  @override
  Widget build(BuildContext context) {
    List<Map<String, dynamic>> data = [];

    if (report7Days.isEmpty) {
      data = List.generate(7, (index) {
        return {
          "day": "${index + 1}/03",
          "revenue": 0,
        };
      });
    } else {
      for (var report in report7Days) {
        data.add({
          "day": _formatDate(report.rDay),
          "revenue": report.paymentAmount?.toDouble() ?? 0,
        });
      }
    }

    num maxRevenue = data.map((e) => e['revenue'] as num).reduce((a, b) => a > b ? a : b);
    num maxYAxis = maxRevenue > 0 ? (maxRevenue * 1.2) : 1;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Container(
        padding: EdgeInsets.all(width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
            "revenue_last_7_days".tr.toUpperCase(),
              style: PrimaryFont.bold.copyWith(
                color: AppColors.text,
                fontSize: width * 0.04,
              ),
            ),
            SizedBox(
              height: width * 0.6,
              child: Chart(
                data: data,
                variables: {
                  'day': Variable(
                    accessor: (Map map) => map['day'] as String,
                    scale: OrdinalScale(),
                  ),
                  'revenue': Variable(
                    accessor: (Map map) => map['revenue'] as num,
                    scale: LinearScale(
                      min: 0,
                      max: maxYAxis.isInfinite || maxYAxis.isNaN ? 1 : maxYAxis,
                      formatter: (value) => FormatHelper.formatCurrency(value),
                    ),
                  ),
                },
                marks: [
                  IntervalMark(
                    position: Varset('day') * Varset('revenue'),
                    color: ColorEncode(value: AppColors.primary),
                    label: LabelEncode(
                      encoder: (tuple) => Label(
                        FormatHelper.formatCurrency(tuple['revenue']),
                        LabelStyle(
                          textStyle: PrimaryFont.bold.copyWith(
                            fontSize: width * 0.03,
                            color: AppColors.text,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
                axes: [
                  AxisGuide(
                    line: PaintStyle(strokeWidth: 0),
                    label: LabelStyle(
                      textStyle: PrimaryFont.bold.copyWith(
                        fontSize: width * 0.03,
                        color: AppColors.text,
                      ),
                      offset: Offset(0, 10),
                    ),
                  ),
                  AxisGuide(
                    label: LabelStyle(
                      textStyle: PrimaryFont.regular.copyWith(
                        fontSize: width * 0.03,
                        color: AppColors.text,
                      ),
                      offset: const Offset(-2, 0),
                    ),
                    grid: PaintStyle(
                      strokeColor: AppColors.shadow.withOpacity(0.3),
                      strokeWidth: 1,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: width * 0.01),
          ],
        ),
      ),
    );
  }

  String _formatDate(String? date) {
    if (date == null || date.isEmpty) {
      return "N/A";
    }

    List<String> parts = date.split('/');

    if (parts.length == 3) {
      return "${parts[0]}/${parts[1]}";
    }

    return "N/A";
  }
}
