import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/controllers/bluetooth_printer_controller.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class BluetoothPrinterConfigView extends StatefulWidget {
  const BluetoothPrinterConfigView({super.key});

  @override
  State<BluetoothPrinterConfigView> createState() => _BluetoothPrinterConfigViewState();
}

class _BluetoothPrinterConfigViewState extends State<BluetoothPrinterConfigView> {
  //variable
  BluetoothPrinterController bluetoothPrinterController = Get.find();

  //function
  @override
  void initState() {
    super.initState();
    bluetoothPrinterController.getDeviceList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: '<PERSON><PERSON><PERSON> in Bluetooth'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [

          InkWell(
            onTap: () async {
              bluetoothPrinterController.getDeviceList();
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.refresh, color: Colors.white, size: 35,),
            ),
          ),
          InkWell(
            onTap: () async {
              bluetoothPrinterController.printTest();
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.receipt_long_outlined, color: Colors.white, size: 35,),
            ),
          )
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Obx(() {
          if (bluetoothPrinterController.isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return ListView(
            padding: const EdgeInsets.all(10),
            children: [
              for (dynamic item in bluetoothPrinterController.devices)
                InkWell(
                  onTap: () async {
                    if (item['mac'] == bluetoothPrinterController.connectedMac.value){
                      await bluetoothPrinterController.disconnectPrinter(item['mac']);
                    }
                    else{
                      await bluetoothPrinterController.connectPrinter(item['mac']);
                      if (bluetoothPrinterController.isConnected.value) {
                        Get.back();
                        Get.back();
                      }
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.all(10),
                    margin: const EdgeInsets.only(bottom: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomText(text: item['name'], color: Colors.white),
                            CustomText(text: item['mac'], color: Colors.white),
                          ],
                        ),
                        if (item['mac'] == bluetoothPrinterController.connectedMac.value)
                          const Icon(Icons.check_circle, color: Colors.white),
                      ],
                    ),
                  ),
                ),
            ],
          );
        }),
      ),
    );
  }
}

