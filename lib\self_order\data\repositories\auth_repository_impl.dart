import '../../domain/entities/login/login_request_entity.dart';
import '../../domain/entities/login/login_response_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_data_source.dart';
import '../models/login/login_request_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;

  AuthRepositoryImpl({required this.remoteDataSource});

  @override
  Future<LoginResponseEntity> login(LoginRequestEntity request) async {
    final requestModel = LoginRequestModel(
      username: request.username,
      password: request.password,
      orgId: request.orgId,
    );

    final response = await remoteDataSource.login(requestModel);

    return LoginResponseEntity(
      success: response.success,
      code: response.code,
      message: response.message,
      token: response.result,
      errorDetail: response.errorDetail,
    );
  }
}