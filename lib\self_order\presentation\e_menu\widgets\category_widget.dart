import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_images.dart';
import '../../../../core/theme/app_style.dart';

class CategoryWidget extends StatefulWidget {
  final double width;
  final List itemGroups;
  final int selectedIndex;
  final ValueChanged<int> onSelected;

  const CategoryWidget({super.key,
    required this.width,
    required this.itemGroups,
    required this.selectedIndex,
    required this.onSelected,
  });

  @override
  State<CategoryWidget> createState() => _CategoryWidgetState();
}

class _CategoryWidgetState extends State<CategoryWidget> {
  final ScrollController _scrollController = ScrollController();
  bool _isAtEnd = false;
  bool isScrollingByArrow = false;

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(() {
      final atEnd = _scrollController.offset >= _scrollController.position.maxScrollExtent;
      if (_isAtEnd != atEnd) {
        setState(() => _isAtEnd = atEnd);
      }
    });
  }

  void _scrollRight() {
    setState(() => isScrollingByArrow = true);

    final offset = _scrollController.offset + widget.width * 0.3;
    _scrollController.animateTo(
      offset.clamp(0, _scrollController.position.maxScrollExtent),
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOut,
    ).whenComplete(() {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) setState(() => isScrollingByArrow = false);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SingleChildScrollView(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.only(left: widget.width * 0.015),
          child: Row(
            children: List.generate(widget.itemGroups.length, (index) {
              final group = Map<String, dynamic>.from(widget.itemGroups[index]);
              final isSelected = index == widget.selectedIndex;

              return GestureDetector(
                onTap: () => widget.onSelected(index),
                child: categoryWidgetFromMap(widget.width, group, isSelected),
              );
            }),
          ),
        ),
        Positioned(
          right: 0,
          top: 0,
          bottom: 0,
          child: Visibility(
            visible: !_isAtEnd,
            child: GestureDetector(
              onTap: _scrollRight,
              child: Container(
                width: widget.width * 0.05,
                alignment: Alignment.centerRight,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.transparent,
                      AppColors.background.withOpacity(isScrollingByArrow ? 0.8 : 0.2),
                    ],
                  ),
                ),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: widget.width * 0.035,
                  color: AppColors.text.withOpacity(0.5),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

Widget categoryWidgetFromMap(double width, Map<String, dynamic> group, bool isActive) {
  final groupName = group['ItemGroupName'] ?? 'Danh mục';
  final imageUrl = group['ImageUrl'] ?? '';

  return Column(
    children: [
      Stack(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: EdgeInsets.only(left: width * 0.015, right: width * 0.02),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(width * 0.03),
              border: Border.all(
                color: isActive
                    ? AppColors.primary.withOpacity(0.6)
                    : Colors.transparent,
                width: 3,
              ),
            ),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(width * 0.02),
                  child: Image.network(
                    imageUrl,
                    width: width * 0.12,
                    height: width * 0.12,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => Container(
                      color: Colors.grey[200],
                      width: width * 0.12,
                      height: width * 0.12,
                      child: Image.asset(
                        AppImages.product_1,
                        width: width * 0.12,
                        height: width * 0.12,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                if (isActive)
                  Positioned(
                    top: -width * 0.008,
                    right: -width * 0.01,
                    child: Container(
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.background,
                      ),
                      child: Icon(
                        Icons.check_circle_rounded,
                        color: AppColors.primary,
                        size: width * 0.035,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      SizedBox(height: width * 0.012),
      SizedBox(
        width: width * 0.18,
        child: Text(
          groupName,
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: PrimaryFont.bold.copyWith(
            color: isActive ? AppColors.primary : AppColors.text,
            fontSize: width * 0.025,
          ),
        ),
      )
    ],
  );
}