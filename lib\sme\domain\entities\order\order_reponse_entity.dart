class OrderResponseEntity {
  final bool success;
  final int code;
  final String message;
  final OrderResultEntity? result;
  final String? errorDetail;

  OrderResponseEntity({
    required this.success,
    required this.code,
    required this.message,
    this.result,
    this.errorDetail,
  });
}

class OrderResultEntity {
  final OrderResponse orderResponse;
  final List<OrderDetailResponse> orderDetails;

  OrderResultEntity({
    required this.orderResponse,
    required this.orderDetails,
  });
}

class OrderResponse {
  final int orderId;
  final int branchId;
  final String branchName;
  final String orderCode;
  final String? ticketCode;
  final String orderType;
  final String statusCode;
  final String? paymentMethod;
  final String? paymentKey;
  final String? paymentMsg;
  final String customerPhone;
  final String customerNo;
  final String customerName;
  final double orderSubTotalVAT;
  final double orderDiscountAmountVAT;
  final double orderVATAmount;
  final double orderTotalVAT;
  final double orderSubTotalView;
  final double orderDiscountAmountView;
  final double orderTotalView;
  final String orderNote;
  final String createdAt;
  final int createdBy;
  final String rowVersion;

  OrderResponse({
    required this.orderId,
    required this.branchId,
    required this.branchName,
    required this.orderCode,
    this.ticketCode,
    required this.orderType,
    required this.statusCode,
    this.paymentMethod,
    this.paymentKey,
    this.paymentMsg,
    required this.customerPhone,
    required this.customerNo,
    required this.customerName,
    required this.orderSubTotalVAT,
    required this.orderDiscountAmountVAT,
    required this.orderVATAmount,
    required this.orderTotalVAT,
    required this.orderSubTotalView,
    required this.orderDiscountAmountView,
    required this.orderTotalView,
    required this.orderNote,
    required this.createdAt,
    required this.createdBy,
    required this.rowVersion,
  });
}

class OrderDetailResponse {
  final int detailId;
  final bool isParent;
  final String detailCode;
  final int orderId;
  final String orderCode;
  final String? ticketItemId;
  final String itemNo;
  final String itemName;
  final String? itemNote;
  final double itemVATPer;
  final bool includeVAT;
  final double itemQty;
  final double itemPriceVAT;
  final double subTotalVAT;
  final double discountAmountVAT;
  final double vatAmount;
  final double totalAmountVAT;
  final double itemPrice;
  final double subTotal;
  final double discountAmount;
  final double totalAmount;
  final String createdDate;
  final List<OrderDetailResponse>? children;

  OrderDetailResponse({
    required this.detailId,
    required this.isParent,
    required this.detailCode,
    required this.orderId,
    required this.orderCode,
    this.ticketItemId,
    required this.itemNo,
    required this.itemName,
    this.itemNote,
    required this.itemVATPer,
    required this.includeVAT,
    required this.itemQty,
    required this.itemPriceVAT,
    required this.subTotalVAT,
    required this.discountAmountVAT,
    required this.vatAmount,
    required this.totalAmountVAT,
    required this.itemPrice,
    required this.subTotal,
    required this.discountAmount,
    required this.totalAmount,
    required this.createdDate,
    this.children,
  });
}
