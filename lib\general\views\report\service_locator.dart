import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import 'core/network/dio_client.dart';
import 'data/branch/repositories/branch.dart';
import 'data/branch/sources/branch.dart';
import 'data/report/repositories/report.dart';
import 'data/report/sources/report.dart';
import 'domain/branch/repositories/branch.dart';
import 'domain/branch/usecases/get_branch.dart';
import 'domain/report/repositories/report.dart';
import 'domain/report/usecases/get_report.dart';

final sl = GetIt.instance;

void setupServiceLocator() {
  sl.registerSingleton<DioClientSME>(DioClientSME());
  sl.registerLazySingleton<DioClientReport>(() => DioClientReport());

  // Services
  sl.registerSingleton<BranchService>(BranchApiServiceImpl());
  sl.registerSingleton<OverviewRevenueService>(OverviewRevenueApiServiceImpl());
  sl.registerSingleton<TopBottomBranchesRevenueService>(TopBottomBranchesRevenueApiServiceImpl());
  sl.registerSingleton<Revenue7DaysService>(Revenue7DaysApiServiceImpl());
  sl.registerSingleton<TopAndBottomItemsRevenueService>(TopAndBottomItemsRevenueApiServiceImpl());
  sl.registerSingleton<TopAndBottomCategoriesByAmountService>(TopAndBottomCategoriesByAmountApiServiceImpl());
  sl.registerSingleton<TopItemsRevenueByDateService>(TopItemsRevenueByDateApiServiceImpl());
  sl.registerSingleton<TopBranchesRevenueByDateService>(TopBranchesRevenueByDateApiServiceImpl());
  sl.registerSingleton<TopCatsRevenueByDateService>(TopCatsRevenueByDateApiServiceImpl());
  sl.registerSingleton<TotalPaymentAmountByDateService>(TotalPaymentAmountByDateApiServiceImpl());
  sl.registerSingleton<TotalPaymentAmountService>(TotalPaymentAmountApiServiceImpl());

  // Repositories
  sl.registerSingleton<BranchRepository>(BranchRepositoryImpl());
  sl.registerSingleton<OverviewRevenueRepository>(OverviewRevenueRepositoryImpl(overviewRevenueService: sl<OverviewRevenueService>()));
  sl.registerSingleton<TopBottomBranchesRevenueRepository>(TopBottomBranchesRevenueRepositoryImpl(topBottomBranchesRevenueService: sl<TopBottomBranchesRevenueService>()));
  sl.registerSingleton<Revenue7DaysRepository>(Revenue7DaysRepositoryImpl(revenue7DaysService: sl<Revenue7DaysService>()));
  sl.registerSingleton<TopAndBottomItemsRevenueRepository>(TopAndBottomItemsRevenueRepositoryImpl(topAndBottomItemsRevenueService: sl<TopAndBottomItemsRevenueService>()));
  sl.registerSingleton<TopAndBottomCategoriesByAmountRepository>(TopAndBottomCategoriesByAmountRepositoryImpl(topAndBottomCategoriesByAmountService: sl<TopAndBottomCategoriesByAmountService>()));
  sl.registerSingleton<TopItemsRevenueByDateRepository>(TopItemsRevenueByDateRepositoryImpl(topItemsRevenueByDateService: sl<TopItemsRevenueByDateService>()));
  sl.registerSingleton<TopBranchesRevenueByDateRepository>(TopBranchesRevenueByDateRepositoryImpl(topBranchesRevenueByDateService: sl<TopBranchesRevenueByDateService>()));
  sl.registerSingleton<TopCatsRevenueByDateRepository>(TopCatsRevenueByDateRepositoryImpl(topCatsRevenueByDateService: sl<TopCatsRevenueByDateService>()));
  sl.registerSingleton<TotalPaymentAmountByDateRepository>(TotalPaymentAmountByDateRepositoryImpl(totalPaymentAmountByDateService: sl<TotalPaymentAmountByDateService>()));
  sl.registerSingleton<TotalPaymentAmountRepository>(TotalPaymentAmountRepositoryImpl(totalPaymentAmountService: sl<TotalPaymentAmountService>()));

  // UseCases
  sl.registerSingleton<GetBranchUseCase>(GetBranchUseCase());
  sl.registerSingleton<GetOverviewRevenueUseCase>(GetOverviewRevenueUseCase(repository: sl<OverviewRevenueRepository>()));
  sl.registerSingleton<GetTopBottomBranchesRevenueUseCase>(GetTopBottomBranchesRevenueUseCase(repository: sl<TopBottomBranchesRevenueRepository>()));
  sl.registerSingleton<GetRevenue7DaysUseCase>(GetRevenue7DaysUseCase(repository: sl<Revenue7DaysRepository>()));
  sl.registerSingleton<GetTopAndBottomItemsRevenueUseCase>(GetTopAndBottomItemsRevenueUseCase(repository: sl<TopAndBottomItemsRevenueRepository>()));
  sl.registerSingleton<GetTopAndBottomCategoriesByAmountUseCase>(GetTopAndBottomCategoriesByAmountUseCase(repository: sl<TopAndBottomCategoriesByAmountRepository>()));
  sl.registerSingleton<GetTopItemsRevenueByDateUseCase>(GetTopItemsRevenueByDateUseCase(repository: sl<TopItemsRevenueByDateRepository>()));
  sl.registerSingleton<GetTopBranchesRevenueByDateUseCase>(GetTopBranchesRevenueByDateUseCase(repository: sl<TopBranchesRevenueByDateRepository>()));
  sl.registerSingleton<GetTopCatsRevenueByDateUseCase>(GetTopCatsRevenueByDateUseCase(repository: sl<TopCatsRevenueByDateRepository>()));
  sl.registerSingleton<GetTotalPaymentAmountByDateUseCase>(GetTotalPaymentAmountByDateUseCase(repository: sl<TotalPaymentAmountByDateRepository>()));
  sl.registerSingleton<GetTotalPaymentAmountUseCase>(GetTotalPaymentAmountUseCase(repository: sl<TotalPaymentAmountRepository>()));

}
