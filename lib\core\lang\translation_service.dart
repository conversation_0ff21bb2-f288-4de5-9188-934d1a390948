import 'package:get/get.dart';
import 'dart:ui';

class TranslationService extends Translations {
  static final fallbackLocale = const Locale('vi');

  @override
  Map<String, Map<String, String>> get keys => {
        'en': {
          'login': 'Login',
          'welcome_message': 'Please login to your account!',
          'username': 'Username',
          'password': 'Password',
          'branches': 'Branches',
          'select_branch': 'Select branch',
          'search': 'Search',
          'counter': 'Counter',
          'select_counter': 'Select counter',
          'area': 'Area',
          'select_area': 'Select area',
          'table': 'Table',
          'select_table': 'Select table',
          'remember_me': 'Remember me',
          'login_button': 'LOGIN',
          'error': 'Error',
          'message_username': 'Please enter username',
          'message_password': 'Please enter password',
          'message_incorrect_credentials': 'Username or password is incorrect',
          'error_invalid_api': 'Invalid API response',
          'error_login_failed': '<PERSON><PERSON> failed. Please check your credentials.',
          'error_network': 'Network error. Please check your connection.',
          'error_unknown': 'An unknown error occurred. Please try again later.',
          'unauthorized': 'Your session has expired\nPlease log in again',
          'forbidden': 'Your account does not have access permissions',
          'not_found': 'Resource does not exist',
          'no_data_found': "No data found",
          'order_here': 'ORDER HERE',
          'take_away': 'Take away',
          'dine_in': 'Dine in',
          'start_order': 'Where will you be eating today?',
          'cart': 'Cart',
          'item': 'item',
          'items': 'items',
          'note': 'Note',
          'note_hint': 'Add your note here!',
          'update_cart': 'Update cart',
          'add_to_cart': 'Add to cart',
          'qty': 'Qty',
          'check_out': 'Check out',
          'total': 'Total',
          'cart_empty': 'Cart is empty',
          'order_summary': 'Order Summary',
          'add_item': 'Add Item',
          'delete': 'Delete',
          'select': 'Select',
          'edit': 'Edit',
          'collapse': 'Collapse',
          'see_more': 'See more...',
          'payment_details': 'Payment Details',
          'subtotal': 'Subtotal',
          'discount_applied': 'Discount Applied',
          'payment_method': 'Payment Method',
          'apply_promotion': 'Apply Promotion',
          'select_payment_method': 'Select Payment Method',
          'cancel': 'Cancel',
          'confirm': 'Confirm',
          'select_promotion': 'Select Promotion',
          'member_points': 'Member Points',
          'enter_phone': 'Enter Phone Number',
          'scan_to_pay': 'Scan to pay',
          'check': 'Check',
          'amount': 'Amount',
          'confirm_cancel_payment': 'Are you sure you want to cancel payment?',
          'back': 'Back',
          'content': 'Content',
          'payment_success': 'Payment success',
          'new_order': 'Order more dishes',
          'print_qr': 'Print QR',
          'recent_searches': 'Recent Searches',
          'all_products': 'All products',
          'results_for': 'Results for',
          'select_customer': 'Select customer',
          'search_customer': 'Search customer',
          'add_new_customer': 'Add new customer',
          'select_or_enter_customer': 'Select or enter customer',
          'enter_customer_info': 'Enter customer information',
          'customer_info': 'Customer information',
          'name': 'Name',
          'enter_name': 'Enter name',
          'phone': 'Phone number',
          "search_product": "Search product",
          "field_required": "This field is required",
          'confirm_payment': 'Confirm Payment',
          'enter_payment_note': 'Enter payment confirmation note',
          'payment_note_hint': 'Example: Transferred via ACB bank',
          'payment_note_required': 'Please enter the payment note',
          'enter_search_term': 'Enter search term',
          'no_customer_found': 'No customer found',
          "back_to_menu": "Back to menu",


          "dashboard": "Dashboard",
          "by_item": "Item",
          "by_category": "Category",
          "by_branch": "Branch",
          "by_payment": "Payment Method",
          "by_channel": "Channel",
          "by_promotion": "Promotion",

          "business_overview": "BUSINESS OVERVIEW",
          "brand": "Brand",
          "branch": "Branch",
          "all": "All",
          "no_selection": "No selection",
          "total_amount": "Total Amount",
          "item_amount": "Item Amount",
          "tax": "Tax",
          "discount": "Discount",
          "bills": "Bills",
          "foc": "FOC",
          "revenue_sharing": "Revenue Sharing",
          "revenue_last_7_days": "revenue last 7 days",
          "highest_branch_revenue": "top highest revenue branches",
          "lowest_branch_revenue": "top lowest revenue branches",
          "top_selling_products": "top 10 best-selling products",
          "slow_selling_products": "top 10 slow-selling products" ,
          "top_categories_by_revenue": "Top 5 Highest Revenue Categories",
          "bottom_categories_by_revenue": "Top 5 Lowest Revenue Categories",
          "total_payment_amount": "Top 10 Revenue by Payment Method",

          "select_branch_time": "Select branch & time",
          "select_time": "Select time",
          "today": "Today",
          "yesterday": "Yesterday",
          "this_week": "This week",
          "last_week": "Last week",
          "this_month": "This month",
          "last_month": "Last month",
          "this_year": "This year",
          "last_year": "Last year",
          "custom": "Custom",
          "from_date": "From date",
          "to_date": "To date",
          "select_all": "Select all",
          "branch_code": "Branch Code",
          "branch_name": "Branch Name",
          "fetch_data": "Fetch Data",
          "search_branch": "Search branch",
          "save": "save"
        },
        'vi': {
          'login': 'Đăng nhập',
          'welcome_message': 'Vui lòng đăng nhập vào tài khoản của bạn nhé!',
          'username': 'Tên đăng nhập',
          'password': 'Mật khẩu',
          'branches': 'Chi nhánh',
          'select_branch': 'Chọn chi nhánh',
          'search': 'Tìm kiếm',
          'counter': 'Quầy',
          'select_counter': 'Chọn quầy',
          'area': 'Khu',
          'select_area': 'Chọn khu',
          'table': 'Bàn',
          'select_table': 'Chọn bàn',
          'remember_me': 'Ghi nhớ tài khoản',
          'login_button': 'ĐĂNG NHẬP',
          'error': 'Lỗi',
          'message_username': 'Vui lòng nhập tên đăng nhập',
          'message_password': 'Vui lòng nhập mật khẩu',
          'message_incorrect_credentials': 'Tên tài khoản hoặc mật khẩu không đúng',
          'error_invalid_api': 'Phản hồi API không hợp lệ',
          'error_login_failed': 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin',
          'error_network': 'Lỗi mạng. Vui lòng kiểm tra kết nối của bạn',
          'error_unknown': 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau',
          'unauthorized': 'Phiên đăng nhập đã hết hạn\nVui lòng đăng nhập lại',
          'forbidden': 'Tài khoản của bạn không có quyền truy cập',
          'not_found': 'Tài nguyên không tồn tại',
          'no_data_found': 'Không tìm thấy dữ liệu',
          'order_here': 'GỌI MÓN TẠI ĐÂY',
          'take_away': 'Mang đi',
          'dine_in': 'Tại chỗ',
          'start_order': 'Hôm nay bạn sẽ ăn ở đâu?',
          'cart': 'Giỏ hàng',
          'item': 'món',
          'items': 'món',
          'note': 'Ghi chú',
          'note_hint': 'Hãy ghi chú vào đây nhé!',
          'update_cart': 'Cập nhật giỏ hàng',
          'add_to_cart': 'Thêm vào giỏ hàng',
          'qty': 'SL',
          'check_out': 'Thanh toán',
          'total': 'Tổng cộng',
          'cart_empty': 'Giỏ hàng trống',
          'order_summary': 'Tóm tắt đơn hàng',
          'add_item': 'Thêm món',
          'delete': 'Xoá',
          'select': 'Lựa chọn',
          'edit': 'Chỉnh sửa',
          'collapse': 'Thu gọn',
          'see_more': 'Xem thêm...',
          'payment_details': 'Chi tiết thanh toán',
          'subtotal': 'Tạm tính',
          'discount_applied': 'Đã giảm',
          'payment_method': 'Phương thức thanh toán',
          'apply_promotion': 'Áp dụng ưu đãi',
          'select_payment_method': 'Chọn phương thức thanh toán',
          'cancel': 'Hủy',
          'confirm': 'Xác nhận',
          'select_promotion': 'Chọn ưu đãi',
          'member_points': 'Tích điểm thành viên',
          'enter_phone': 'Nhập số điện thoại',
          'scan_to_pay': 'Quét mã để thanh toán',
          'check': 'Kiểm tra',
          'amount': 'Số tiền',
          'confirm_cancel_payment': 'Bạn có chắc muốn huỷ thanh toán?',
          'back': 'Quay lại',
          'content': 'Nội dung',
          'payment_success': 'Thanh toán thành công',
          'new_order': 'Đặt thêm món',
          'print_qr': 'In mã',
          'recent_searches': 'Tìm kiếm gần đây',
          'all_products': 'Danh sách món',
          'results_for' : 'Kết quả cho',
          'select_customer': 'Chọn khách hàng',
          'search_customer': 'Tìm kiếm khách hàng',
          'add_new_customer': 'Thêm khách mới',
          'select_or_enter_customer': 'Chọn hoặc nhập khách hàng',
          'enter_customer_info': 'Nhập thông tin khách hàng',
          'customer_info': 'Thông tin khách hàng',
          'name': 'Tên',
          'enter_name': 'Nhập tên',
          'phone': 'Số điện thoại',
          "search_product": "Tìm kiếm mặt hàng",
          "field_required": "không được bỏ trống",
          'confirm_payment': 'Xác nhận thanh toán',
          'enter_payment_note': 'Nhập ghi chú xác nhận thanh toán',
          'payment_note_hint': 'Ví dụ: Đã chuyển khoản ngân hàng ACB',
          'payment_note_required': 'Vui lòng nhập ghi chú thanh toán',
          'enter_search_term': 'Vui lòng nhập từ khóa tìm kiếm',
          'no_customer_found': 'Không tìm thấy khách hàng',
          "back_to_menu": "Quay về bán hàng",


          "dashboard": "Tổng quan",
          "by_item": "Mặt hàng",
          "by_category": "Nhóm hàng",
          "by_branch": "Chi nhánh",
          "by_payment": "Hình thức TT",
          "by_channel": "Kênh bán",
          "by_promotion": "Chương trình KM",

          "business_overview": "TỔNG QUAN KINH DOANH",
          "brand": "Thương hiệu",
          "branch": "Cửa hàng",
          "all": "Tất cả",
          "no_selection": "Chưa chọn",
          "total_amount": "Tổng tiền",
          "item_amount": "Tiền hàng",
          "tax": "Thuế",
          "discount": "Giảm giá",
          "bills": "Hóa đơn",
          "foc": "FOC",
          "revenue_sharing": "Chia sẻ doanh thu",
          "revenue_last_7_days": "Doanh thu 7 ngày gần nhất",
          "highest_branch_revenue": "Chi nhánh doanh thu cao nhất",
          "lowest_branch_revenue": "Chi nhánh doanh thu thấp nhất",
          "top_selling_products": "Top 10 sản phẩm bán chạy",
          "slow_selling_products": "Top 10 sản phẩm bán chậm",
          "top_categories_by_revenue": "Top 5 Nhóm hàng có doanh thu cao nhất",
          "bottom_categories_by_revenue": "Top 5 Nhóm hàng có doanh thu thấp nhất",
          "total_payment_amount": "Top 10 doanh thu theo hình thức thanh toán",

          "select_branch_time": "Chọn chi nhánh & thời gian",
          "select_time": "Chọn thời gian",
          "today": "Hôm nay",
          "yesterday": "Hôm qua",
          "this_week": "Tuần này",
          "last_week": "Tuần trước",
          "this_month": "Tháng này",
          "last_month": "Tháng trước",
          "this_year": "Năm nay",
          "last_year": "Năm trước",
          "custom": "Tùy chọn",
          "from_date": "Từ ngày",
          "to_date": "Đến ngày",
          "select_all": "Chọn tất cả",
          "branch_code": "Mã CN",
          "branch_name": "Tên chi nhánh",
          "fetch_data": "Lấy dữ liệu",
          "search_branch": "Tìm chi nhánh",
          "save": "Lưu"
        },
      };
}
