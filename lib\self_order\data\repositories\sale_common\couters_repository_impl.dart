import '../../../domain/entities/sale_common/counters_response_entity.dart';
import '../../../domain/repositories/sale_common/couters_repository.dart';
import '../../datasources/sale_common/counters_remote_data_source.dart';

class CounterRepositoryImpl implements CounterRepository {
  final CounterRemoteDataSource remoteDataSource;

  CounterRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<CounterResponseEntity>> getCounters(int branchId) async {
    final models = await remoteDataSource.getCounters(branchId);
    return models
        .map((e) => CounterResponseEntity(
        counterId: e.counterId,
        counterName: e.counterName
    )).toList();
  }
}
