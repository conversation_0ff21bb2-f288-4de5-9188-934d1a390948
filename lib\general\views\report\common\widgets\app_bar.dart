import 'package:flutter/material.dart';
import '../../core/configs/theme/app_colors.dart';
import 'dart:io';
import '../../core/configs/theme/app_style.dart';

class BasicAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? leading;
  final Color? background;
  final Color? backArrow;
  final bool hideBack;
  final Widget? customTitle;
  final List<Widget>? actions;

  const BasicAppbar({
    this.title,
    this.leading,
    this.background,
    this.backArrow,
    this.hideBack = false,
    this.customTitle,
    this.actions,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;

    return AppBar(
      leading: leading ??
          (hideBack
              ? null
              : IconButton(
            icon: Icon(
              Platform.isIOS ? Icons.arrow_back_ios : Icons.arrow_back,
              color: backArrow,
              size: width * 0.06,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          )),
      title: customTitle ??
          Text(
            title ?? "",
            style: PrimaryFont.bold.copyWith(
              fontSize: width * 0.05,
              color: backArrow,
            ),
          ),
      surfaceTintColor: AppColors.primary,
      shadowColor: AppColors.primary,
      backgroundColor: background,
      centerTitle: true,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(50);
}
