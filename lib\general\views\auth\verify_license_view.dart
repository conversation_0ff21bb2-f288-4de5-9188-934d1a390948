import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';

class VerifyLicenseView extends StatefulWidget {
  const VerifyLicenseView({super.key});
 
  @override
  State<VerifyLicenseView> createState() => _VerifyLicenseViewState();
}

class _VerifyLicenseViewState extends State<VerifyLicenseView> {
  //variable
  AuthController authController = Get.find();
  TextEditingController licenseController = TextEditingController();

  //function
  @override
  void initState() {
    super.initState();
  }

  verify() async {
    if (licenseController.text.isEmpty) {
      AppFunction.showError('Vui lòng nhập mã kích hoạt');
      return;
    }
    bool success = await authController.verifyLicense(licenseController.text);
    if (success) {
      await authController.saveLicense(licenseController.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset('assets/images/general/banner.png', width: 250,),
            SizedBox(height: 10,),
            CustomText(text: 'Vui lòng nhập mã kích hoạt để bắt đầu sử dụng', bold: true,),
            SizedBox(height: 10,),
            CustomTextField(
              controller: licenseController,
              label: 'Mã kích hoạt',
            ),
            SizedBox(height: 10,),
            CustomButton(
              text: 'Xác thực',
              color: AppColors.primary,
              onTap: () {
                verify();
              },
            )
          ],
        ),
      ),
    );
  }
}
