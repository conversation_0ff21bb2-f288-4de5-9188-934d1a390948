import 'package:get/get.dart';
import '../../presentation/e_menu/search_menu/search_menu_controller.dart';
import '../../../core/network/dio_client.dart';
import '../../data/datasources/e_menu/menu_local_data_source.dart';
import '../../data/datasources/e_menu/menu_remote_data_source.dart';
import '../../data/repositories/e_menu/menu_repository_impl.dart';
import '../../domain/repositories/menu/menu_repository.dart';
import '../../domain/usecases/menu/menu_usecase.dart';
import 'e_menu_controller.dart';

class EMenuBinding extends Bindings {
  @override
  void dependencies() {
    final dioClient = Get.find<DioClient>();

    Get.lazyPut<MenuRemoteDataSource>(() => MenuRemoteDataSourceImpl(dioClient: dioClient));

    Get.lazyPut<MenuLocalDataSource>(() => MenuLocalDataSourceImpl());

    Get.lazyPut<MenuRepository>(() => MenuRepositoryImpl(
      remote: Get.find(),
      local: Get.find(),
    ));

    Get.lazyPut<MenuUseCase>(() => MenuUseCase(repository: Get.find()));

    Get.lazyPut<EMenuController>(() => EMenuController(menuUseCase: Get.find()));

    Get.lazyPut<SearchMenuController>(() => SearchMenuController());
  }
}
