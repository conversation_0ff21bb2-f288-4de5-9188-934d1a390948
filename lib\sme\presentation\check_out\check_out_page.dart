import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/controllers/bluetooth_printer_controller.dart';
import 'package:gls_self_order/core/controllers/usb_printer_controller.dart';
import 'package:gls_self_order/core/widgets/print_bill_widget.dart';
import 'package:gls_self_order/core/widgets/print_qr_widget.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_images.dart';
import '../../../core/theme/app_style.dart';
import '../../../core/widgets/card_decoration.dart';
import '../../../core/widgets/default_button.dart';
import '../../../general/classes/user_info.dart';
import '../../domain/entities/order/status_request_entity.dart';
import '../../routes/sme_app_routes.dart';
import '../e_menu/cart/cart_controller.dart';
import '../e_menu/widgets/product_widget.dart';
import 'check_out_controller.dart';

class CheckoutPage extends StatelessWidget {
  final CartSMEController cartController = Get.find<CartSMEController>();
  final CheckoutController controller = Get.find<CheckoutController>();
  final BluetoothPrinterController bluetoothPrinterController = Get.find();
  final UsbPrinterController usbPrinterController = Get.find();
  final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0);
  CheckoutPage({super.key});


  @override
  Widget build(BuildContext context) {
    final cartController = Get.find<CartSMEController>();
    
    final width = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: AppColors.secondBackground,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        title: Text('check_out'.tr),
        titleTextStyle: PrimaryFont.bold.copyWith(
          fontSize: width * 0.05,
          color: AppColors.text,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Platform.isIOS ? Icons.arrow_back_ios : Icons.arrow_back,
            color: AppColors.text,
            size: width * 0.06,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Obx(() {
        if (cartController.cartItems.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'cart_empty'.tr,
                  style: PrimaryFont.regular.copyWith(
                    fontSize: width * 0.05,
                    color: AppColors.text,
                  ),
                ),
                SizedBox(height: 20),
                DefaultButton(
                  onPress: () {
                    Get.back();
                  },
                  title: 'back_to_menu'.tr,
                  titleStyle: PrimaryFont.bold.copyWith(
                    fontSize: 16,
                    color: AppColors.background,
                  ),
                  color: AppColors.primary,
                  widthPercentage: 0.45,
                  borderRadius: 10,
                  heightPercentage: 0.1,
                ),
              ],
            ),
          );
        }

        final itemsToShow = controller.showAllItems.value
            ? cartController.cartItems
            : cartController.cartItems.take(3).toList();

        return Container(
          width: double.infinity,
          height: double.infinity,
          child: Stack(
            children: [
              PrintBillWidget(
                billCode: controller.orderCode.value,
                customerName: controller.orderResponse.value?.result?.orderResponse.customerName ?? 'Khách hàng',
                dateTime: controller.orderResponse.value?.result?.orderResponse.createdAt ?? DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now()),
                total: currencyFormat.format(controller.orderResponse.value?.result?.orderResponse.orderTotalView ?? cartController.totalPrice.value),
                sub_total: currencyFormat.format(controller.orderResponse.value?.result?.orderResponse.orderSubTotalView ?? cartController.totalPrice.value),
                vat: currencyFormat.format(controller.orderResponse.value?.result?.orderResponse.orderVATAmount ?? 0),
                items: controller.orderResponse.value?.result?.orderDetails.map((detail) {
                  return {
                    'name': detail.itemName,
                    'qty': detail.itemQty,
                    'unit_price': currencyFormat.format(detail.itemPrice),
                    'total': currencyFormat.format(detail.totalAmount),
                    'note': detail.itemNote,
                  };
                }).toList() ?? [],
              ),
              PrintQrWidget(
                  qrCode: controller.zaloQRCodeData.value ?? '',
                  total: currencyFormat.format(controller.orderResponse.value?.result?.orderResponse.orderTotalView ?? cartController.totalPrice.value),
              ),
              Positioned.fill(
                child: Container(
                  color: Colors.white,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(vertical: width * 0.015),
                    child: Column(
                      children: [
                        _buildAddressSection(context, width),
                        SizedBox(height: width * 0.01),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: width * 0.015),
                          decoration: cardDecoration(width * 0, AppColors.background),
                          child: Column(
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: width * 0.03),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'order_summary'.tr,
                                      style: PrimaryFont.bold.copyWith(
                                        color: AppColors.text,
                                        fontSize: width * 0.03,
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () => Get.back(),
                                      child: Text(
                                        'add_item'.tr,
                                        style: PrimaryFont.regular.copyWith(
                                          color: AppColors.primary,
                                          fontSize: width * 0.03,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: width * 0.01),
                              ListView.builder(
                                itemCount: itemsToShow.length + (cartController.cartItems.length > 3 ? 1 : 0),
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  if (index < itemsToShow.length) {
                                    final item = itemsToShow[index];
                                    final product = item['product'];
                                    final images = product['Images'] as List? ?? [];
                                    final imageUrl = images.isNotEmpty ? images.first['MediaUrl'] ?? '' : '';
                                    final itemName = product['ItemName'] ?? '';
                                    final quantity = item['quantity'];
                                    double price = (product['Price'] as num?)?.toDouble() ?? 0.0;
                                    final originalPrice = (product['OriginalPrice'] as num?)?.toDouble();
                                    final defaultItems = product['DefaultItems'] as List?;

                                    final selectedChoices = product['SelectedChoices'];
                                    if (selectedChoices != null && selectedChoices is Map) {
                                      for (var items in selectedChoices.values) {
                                        if (items is List) {
                                          for (var item in items) {
                                            if (item is Map && item['Price'] != null) {
                                              price += (item['Price'] as num).toDouble();
                                            }
                                          }
                                        }
                                      }
                                    }
                                    return Slidable(
                                      endActionPane: ActionPane(
                                        motion: const ScrollMotion(),
                                        extentRatio: 0.15,
                                        children: [
                                          CustomSlidableAction(
                                            backgroundColor: Colors.red,
                                            padding: EdgeInsets.symmetric(horizontal: width * 0.01),
                                            onPressed: (_) => cartController.removeFromCart(index),
                                            child: Text(
                                              'delete'.tr,
                                              style: PrimaryFont.bold.copyWith(
                                                color: AppColors.background,
                                                fontSize: width * 0.03,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      child: Builder(
                                          builder: (slidableContext) {
                                            return Container(
                                              padding: EdgeInsets.fromLTRB(width * 0.03, width * 0.015, width * 0.03, 0),
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.circular(width * 0.01),
                                                    child: Image.network(
                                                      getValidImageUrl(imageUrl),
                                                      width: width * 0.12,
                                                      height: width * 0.12,
                                                      fit: BoxFit.cover,
                                                      errorBuilder: (_, __, ___) => SizedBox(
                                                          height: width * 0.12,
                                                          width: width * 0.12,
                                                          child: Image.asset('assets/images/general/no_image.jpg', fit: BoxFit.cover,)
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: width * 0.015),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Text(
                                                          "$itemName x$quantity",
                                                          style: PrimaryFont.bold.copyWith(
                                                            color: AppColors.text,
                                                            fontSize: width * 0.03,
                                                          ),
                                                        ),
                                                        if (defaultItems != null && defaultItems.isNotEmpty)
                                                          Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: defaultItems.map<Widget>((defaultItem) {
                                                              return Padding(
                                                                padding: EdgeInsets.only(top: width * 0.005),
                                                                child: Text(
                                                                  '• ${defaultItem['ItemName']} x${defaultItem['Qty']}',
                                                                  style: PrimaryFont.medium.copyWith(
                                                                    color: AppColors.text.withOpacity(0.7),
                                                                    fontSize: width * 0.028,
                                                                  ),
                                                                ),
                                                              );
                                                            }).toList(),
                                                          ),
                                                        if (product['SelectedChoices'] != null)
                                                          ...(product['SelectedChoices'] as Map).entries.map((entry) {
                                                            final items = entry.value as List;

                                                            return Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              children: [
                                                                Text(
                                                                  '↳ ${items.first['ChoiceName'] ?? 'select'.tr}:',
                                                                  style: PrimaryFont.regular.copyWith(
                                                                    color: AppColors.shadow,
                                                                    fontSize: width * 0.028,
                                                                  ),
                                                                ),
                                                                ...items.map<Widget>((item) {
                                                                  return Padding(
                                                                    padding: EdgeInsets.only(left: width * 0.03),
                                                                    child: Text(
                                                                      '• ${item['ItemName']}',
                                                                      style: PrimaryFont.regular.copyWith(
                                                                        color: AppColors.shadow,
                                                                        fontSize: width * 0.028,
                                                                      ),
                                                                    ),
                                                                  );
                                                                }),
                                                              ],
                                                            );
                                                          }),
                                                        if (item['note'] != null && item['note'].toString().isNotEmpty)
                                                          Text(
                                                            '${'note'.tr}: ${item['note']}',
                                                            style: PrimaryFont.regular.copyWith(
                                                              color: AppColors.shadow,
                                                              fontSize: width * 0.028,
                                                            ),
                                                          ),
                                                        GestureDetector(
                                                          onTap: () {
                                                            final cartController = Get.find<CartSMEController>();
                                                            final itemToEdit = cartController.getCartItem(index);
                                                            if (itemToEdit != null) {
                                                              Get.toNamed(
                                                                SmeAppRoutes.productDetail,
                                                                arguments: {
                                                                  'product': itemToEdit['product'] ?? {},
                                                                  'editMode': true,
                                                                  'cartIndex': index,
                                                                  'initialQuantity': itemToEdit['quantity'] ?? 1,
                                                                  'initialNote': itemToEdit['note'] ?? '',
                                                                  'initialSelectedChoices': (itemToEdit['product']['SelectedChoices'] as Map?)?.cast<String, dynamic>() ?? {},
                                                                  'ItemNo': itemToEdit['product']['ItemNo'] ?? '',
                                                                  'ItemName': itemToEdit['product']['ItemName'] ?? '',
                                                                  'Price': itemToEdit['product']['Price'] ?? 0,
                                                                  'ItemDesc': itemToEdit['product']['ItemDesc'] ?? '',
                                                                  'Images': itemToEdit['product']['Images'] ?? [],
                                                                  'MediaUrl': itemToEdit['product']['Images']?.isNotEmpty == true
                                                                      ? itemToEdit['product']['Images'].first['MediaUrl'] ?? ''
                                                                      : '',
                                                                  'DefaultItems': itemToEdit['product']['DefaultItems'] ?? [],
                                                                  'Choices': itemToEdit['product']['Choices'] ?? [],
                                                                },
                                                              );
                                                            }
                                                          },
                                                          child: SizedBox(
                                                            height: width * 0.065,
                                                            child: Row(
                                                              mainAxisSize : MainAxisSize.min,
                                                              children: [
                                                                Text(
                                                                  'edit'.tr,
                                                                  style: PrimaryFont.bold.copyWith(
                                                                    color: AppColors.primary,
                                                                    fontSize: width * 0.03,
                                                                  ),
                                                                ),
                                                                Row(
                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                  children: [
                                                                    RawMaterialButton(
                                                                      onPressed: () {
                                                                        final currentQty = item['quantity'];
                                                                        if (currentQty > 0.0) {
                                                                          cartController.updateItemQuantity(index, currentQty - 1.0);

                                                                          if (currentQty <= 1.0) {
                                                                            Future.delayed(Duration(milliseconds: 100), () {
                                                                              final slidable = Slidable.of(slidableContext);
                                                                              slidable?.openEndActionPane();
                                                                            });
                                                                          }
                                                                        }
                                                                      },

                                                                      shape: CircleBorder(),
                                                                      fillColor: AppColors.primary,
                                                                      elevation: 0,
                                                                      highlightElevation: 0,
                                                                      constraints: BoxConstraints.tightFor(
                                                                        width: width * 0.03,
                                                                        height: width * 0.03,
                                                                      ),
                                                                      child: Icon(
                                                                        Icons.remove,
                                                                        color: AppColors.background,
                                                                        size: width * 0.03,
                                                                      ),
                                                                    ),
                                                                    Text(
                                                                      '${item['quantity']}',
                                                                      style: PrimaryFont.regular.copyWith(
                                                                        color: AppColors.text,
                                                                        fontSize: width * 0.03,
                                                                      ),
                                                                    ),
                                                                    RawMaterialButton(
                                                                      onPressed: () {
                                                                        final currentQty = item['quantity'];
                                                                        cartController.updateItemQuantity(index, currentQty + 1.0);
                                                                        Future.delayed(Duration(milliseconds: 100), () {
                                                                          final slidable = Slidable.of(slidableContext);
                                                                          slidable?.close();
                                                                        });
                                                                      },
                                                                      shape: CircleBorder(),
                                                                      fillColor: AppColors.primary,
                                                                      elevation: 0,
                                                                      highlightElevation: 0,
                                                                      constraints: BoxConstraints.tightFor(
                                                                        width: width * 0.03,
                                                                        height: width * 0.03,
                                                                      ),
                                                                      child: Icon(
                                                                        Icons.add,
                                                                        color: AppColors.background,
                                                                        size: width * 0.03,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.end,
                                                    children: [
                                                      Text(
                                                        currencyFormat.format(price),
                                                        style: PrimaryFont.regular.copyWith(
                                                          color: AppColors.text,
                                                          fontSize: width * 0.03,
                                                        ),
                                                      ),
                                                      // // Chỉ hiển thị giá gốc bị gạch ngang nếu có giá tạm thời
                                                      // if (product['OriginalPrice'] != null &&
                                                      //     (product['OriginalPrice'] as num).toDouble() != price)
                                                      //   Text(
                                                      //     currencyFormat.format((product['OriginalPrice'] as num).toDouble()),
                                                      //     style: PrimaryFont.regular.copyWith(
                                                      //       color: AppColors.text.withOpacity(0.6),
                                                      //       fontSize: width * 0.03,
                                                      //       decoration: TextDecoration.lineThrough,
                                                      //     ),
                                                      //   ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                      ),
                                    );
                                  } else {
                                    return Center(
                                      child: InkWell(
                                        onTap: () => controller.toggleExpandCollapse(),
                                        child: Text(
                                          controller.showAllItems.value ? 'collapse'.tr : 'see_more'.tr,
                                          style: PrimaryFont.bold.copyWith(
                                            color: AppColors.primary,
                                            fontSize: width * 0.03,
                                          ),
                                        ),
                                      ),
                                    );
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: width * 0.01),
                        _buildPaymentDetails(width),
                        SizedBox(height: width * 0.01),
                        _buildPaymentMethodSection(width)
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        );
      }),
      bottomNavigationBar: Obx(() {
        if (cartController.cartItems.isEmpty) {
          return SizedBox.shrink();
        }

        return Container(
          color: AppColors.background,
          padding: EdgeInsets.all(width * 0.03),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${'total'.tr}:',
                    style: PrimaryFont.bold.copyWith(
                      fontSize: width * 0.045,
                      color: AppColors.text,
                    ),
                  ),
                  Text(
                    currencyFormat.format(cartController.totalPrice.value),
                    style: PrimaryFont.bold.copyWith(
                      fontSize: width * 0.045,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: width * 0.03),
              DefaultButton(
                onPress: () async {
                  final currentMethod = controller.getCurrentPaymentMethod();
                  if (currentMethod == 'CASH') {
                    Get.dialog(
                      Center(child: CircularProgressIndicator(color: AppColors.primary)),
                      barrierDismissible: false,
                    );
                    try {
                      // 1. Tạo đơn hàng trước
                      await controller.createOrder();
                      controller.printOrderSummary();

                      // 2. Xử lý thanh toán tiền mặt mà không cần note
                      await controller.processCashPayment();

                      // 3. Hiển thị thông báo thành công
                      Get.back(); // Đóng dialog loading

                      _showPaymentSuccessDialog(context);
                    } catch (e) {
                      Get.back(); // Đóng dialog loading nếu có lỗi
                      Get.snackbar('Lỗi', 'Đã xảy ra lỗi trong quá trình thanh toán');
                    }
                  } else {
                    // Hiển thị loading
                    Get.dialog(
                      Center(child: CircularProgressIndicator(color: AppColors.primary)),
                      barrierDismissible: false,
                    );

                    try {
                      // 1. Tạo đơn hàng trước
                      await controller.createOrder();
                      controller.printOrderSummary();

                      // 2. Nếu thành công thì tạo QR code
                      if (controller.orderSuccess.value) {
                        if (currentMethod == 'ZALOPAY') {
                          await controller.generateQRCode();
                        }
                        else if (currentMethod == 'ACB') {
                          await controller.generateACBQRCode();
                          if (controller.qrError.value.isNotEmpty) {
                            Get.back();
                            Get.snackbar('Lỗi', controller.qrError.value);
                            return;
                          }
                        }
                        //controller.startCountdown();
                        Get.back();
                        _genQrCode(context);
                      } else {
                        Get.back();
                      }
                    } catch (e) {
                      Get.back();
                      Get.snackbar('Lỗi', 'Đã xảy ra lỗi trong quá trình thanh toán');
                    }
                  }
                },
                title: 'check_out'.tr,
                titleStyle: PrimaryFont.bold.copyWith(
                  fontSize: width * 0.045,
                  color: AppColors.background,
                ),
                color: AppColors.primary,
                widthPercentage: 1,
                borderRadius: width * 0.02,
                heightPercentage: 0.12,
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildPaymentDetails(double width) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: width * 0.03, vertical: width * 0.015),
      decoration: cardDecoration(width * 0, AppColors.background),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'payment_details'.tr,
            style: PrimaryFont.bold.copyWith(
              color: AppColors.text,
              fontSize: width * 0.03,
            ),
          ),
          SizedBox(height: width * 0.015),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'subtotal'.tr,
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
              Text(
                currencyFormat.format(cartController.totalPrice.value),
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
            ],
          ),
          SizedBox(height: width * 0.015),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'discount_applied'.tr,
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
              Text(
                currencyFormat.format(0),
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
            ],
          ),
          Divider(height: width * 0.05),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'total'.tr,
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
              Text(
                currencyFormat.format(cartController.totalPrice.value),
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSection(double width) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: width * 0.03, vertical: width * 0.015),
      decoration: cardDecoration(width * 0, AppColors.background),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'payment_method'.tr,
            style: PrimaryFont.bold.copyWith(
              color: AppColors.text,
              fontSize: width * 0.03,
            ),
          ),
          GestureDetector(
            onTap: () => _showPaymentOptionsBottomSheet(Get.context!),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: width * 0.015),
              child: Row(
                children: [
                  SizedBox(
                    width: width * 0.04,
                    child: _getPaymentMethodIcon(
                        controller.paymentMethod.value, width),
                  ),
                  SizedBox(width: width * 0.02),
                  Expanded(
                    child: Text(
                      controller.paymentMethod.value,
                      style: PrimaryFont.regular.copyWith(
                          fontSize: width * 0.03, color: AppColors.text),
                    ),
                  ),
                  Icon(Icons.arrow_forward_ios,
                      size: width * 0.03, color: AppColors.text),
                ],
              ),
            ),
          ),
          // GestureDetector(
          //   onTap: () => _showVoucherDialog(width),
          //   child: Padding(
          //     padding: EdgeInsets.symmetric(vertical: width * 0.015),
          //     child: Row(
          //       children: [
          //         Icon(Bootstrap.gift,
          //             color: AppColors.primary, size: width * 0.04),
          //         SizedBox(width: width * 0.02),
          //         Expanded(
          //           child: Text(
          //             'apply_promotion'.tr,
          //             style: PrimaryFont.regular.copyWith(
          //                 fontSize: width * 0.03, color: AppColors.text),
          //           ),
          //         ),
          //         Icon(Icons.arrow_forward_ios,
          //             size: width * 0.03, color: AppColors.text),
          //       ],
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _getPaymentMethodIcon(String method, double width) {
    switch (method) {
      case 'ZALOPAY':
        return Image.asset(AppImages.zaloPay, width: width * 0.05);
      case 'MOMO':
        return Image.asset(AppImages.momo, width: width * 0.05);
      case 'ACB':
        return Image.asset(AppImages.acb, width: width * 0.05);
      case 'CASH':
        return Icon(Icons.money, size: width * 0.05, color: Colors.green);
      default:
        return Icon(Icons.payment,
            size: width * 0.05, color: AppColors.primary);
    }
  }

  void _showPaymentOptionsBottomSheet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final controller = Get.find<CheckoutController>();

    controller.setTempPaymentMethod(controller.getCurrentPaymentMethod());

    Get.dialog(
      Dialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.04),
        ),
        insetPadding: EdgeInsets.symmetric(
          horizontal: width * 0.05,
          vertical: width * 0.1,
        ),
        child: Padding(
          padding: EdgeInsets.all(width * 0.03),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'select_payment_method'.tr,
                style: PrimaryFont.bold.copyWith(
                  fontSize: width * 0.04,
                  color: AppColors.text,
                ),
              ),
              SizedBox(height: width * 0.03),
              Obx(() => Column(
                children: [
                  if (UserInfo.activeACB)
                    _buildPaymentOption(
                      context,
                      icon: Image.asset(AppImages.acb, width: width * 0.05),
                      title: 'ACB',
                      isSelected: controller.tempPaymentMethod.value == 'ACB',
                      onTap: () => controller.setTempPaymentMethod('ACB'),
                    ),
                  // _buildPaymentOption(
                  //   context,
                  //   icon: Image.asset(AppImages.momo, width: width * 0.05),
                  //   title: 'Momo',
                  //   isSelected:
                  //   controller.tempPaymentMethod.value == 'MOMO',
                  //   onTap: () => controller.setTempPaymentMethod('MOMO'),
                  // ),
                  // _buildPaymentOption(
                  //   context,
                  //   icon:
                  //   Image.asset(AppImages.zaloPay, width: width * 0.05),
                  //   title: 'ZaloPay',
                  //   isSelected:
                  //   controller.tempPaymentMethod.value == 'ZALOPAY',
                  //   onTap: () => controller.setTempPaymentMethod('ZALOPAY'),
                  // ),
                  _buildPaymentOption(
                    context,
                    icon: Icon(Icons.money, color: Colors.green),
                    title: 'Tiền mặt',
                    isSelected:
                    controller.tempPaymentMethod.value == 'CASH',
                    onTap: () => controller.setTempPaymentMethod('CASH'),
                  ),
                ],
              )),
              SizedBox(height: width * 0.01),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'cancel'.tr,
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                  SizedBox(width: width * 0.02),
                  ElevatedButton(
                    onPressed: () {
                      controller.confirmPaymentMethod();
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      padding: EdgeInsets.symmetric(
                          horizontal: width * 0.02, vertical: width * 0.015),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'confirm'.tr,
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentOption(
      BuildContext context, {
        required Widget icon,
        required String title,
        required bool isSelected,
        required VoidCallback onTap,
      }) {
    final width = MediaQuery.of(context).size.width;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
            vertical: width * 0.015, horizontal: width * 0.015),
        margin: EdgeInsets.only(bottom: width * 0.02),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: Row(
          children: [
            icon,
            SizedBox(width: width * 0.03),
            Text(
              title,
              style: PrimaryFont.regular.copyWith(
                fontSize: width * 0.03,
                color: AppColors.text,
              ),
            ),
            Spacer(),
            if (isSelected)
              Icon(Icons.check_circle,
                  color: AppColors.primary, size: width * 0.05),
          ],
        ),
      ),
    );
  }

  void _showVoucherDialog(double width) {
    Get.dialog(
      Dialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: Padding(
          padding: EdgeInsets.all(width * 0.04),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'select_promotion'.tr,
                style: PrimaryFont.bold.copyWith(
                  fontSize: width * 0.045,
                ),
              ),
              SizedBox(height: width * 0.03),

              // Danh sách voucher
              ListView.builder(
                shrinkWrap: true,
                itemCount: 3,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text('Giảm ${(index + 1) * 10}%'),
                    subtitle: Text(
                        'Đơn tối thiểu ${NumberFormat.currency(locale: 'vi_VN', symbol: '₫').format((index + 1) * 100000)}'),
                    trailing: Radio(
                      value: index,
                      groupValue: null,
                      onChanged: (value) {},
                    ),
                  );
                },
              ),

              SizedBox(height: width * 0.03),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Get.back(),
                      child: Text('cancel'.tr),
                    ),
                  ),
                  SizedBox(width: width * 0.03),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Get.back(),
                      child: Text('Áp dụng'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressSection(BuildContext context,double width) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: width * 0.03, vertical: width * 0.03),
      decoration: cardDecoration(width * 0, AppColors.background),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, size: width * 0.05, color: Colors.red),
              SizedBox(width: width * 0.015),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(() => Text(
                      controller.branchName.value,
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.text,
                        fontSize: width * 0.03,
                      ),
                    )),
                  ],
                ),
              ),
            ],
          ),
          Divider(height: 20, thickness: 1),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () => _showNoteDialog(width),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.note_alt,
                        size: width * 0.05, color: Colors.black),
                    SizedBox(width: width * 0.015),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'note'.tr,
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.text,
                              fontSize: width * 0.03,
                            ),
                          ),
                          Obx(() => Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.shadow.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(width * 0.012),
                            ),
                            child: Text(
                              controller.orderNote.value.isEmpty
                                  ? 'note_hint'.tr
                                  : controller.orderNote.value,
                              style: PrimaryFont.regular.copyWith(
                                color: controller.orderNote.value.isEmpty
                                    ? AppColors.shadow
                                    : AppColors.text,
                                fontSize: width * 0.03,
                              ),
                            ),
                          )),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: width * 0.015),
              Obx(() => GestureDetector(
                onTap: () => _showCustomerSelectionDialog(context),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.person, size: width * 0.05, color: Colors.black),
                    SizedBox(width: width * 0.015),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'customer_info'.tr,
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.text,
                              fontSize: width * 0.03,
                            ),
                          ),
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.shadow.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(width * 0.012),
                            ),
                            child: controller.selectedCustomer.value != null
                                ? Stack(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      controller.selectedCustomer.value!['name'] ?? '',
                                      style: PrimaryFont.regular.copyWith(
                                        color: AppColors.text,
                                        fontSize: width * 0.03,
                                      ),
                                    ),
                                    Text(
                                      controller.selectedCustomer.value!['phone'] ?? '',
                                      style: PrimaryFont.regular.copyWith(
                                        color: AppColors.text,
                                        fontSize: width * 0.03,
                                      ),
                                    ),
                                  ],
                                ),
                                Positioned(
                                  right: 0,
                                  top: 0,
                                  child: GestureDetector(
                                    onTap: () {
                                      controller.selectedCustomer.value = null;
                                    },
                                    child: Container(
                                      width: width * 0.05,
                                      height: width * 0.05,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: Icon(
                                          Icons.close,
                                          size: width * 0.03,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                                : Text(
                              'select_or_enter_customer'.tr,
                              style: PrimaryFont.regular.copyWith(
                                color: AppColors.shadow,
                                fontSize: width * 0.03,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
            ],
          )
        ],
      ),
    );
  }

  _genQrCode(context) {
    //call api get qr code
    print("QR ZALO hiển thị trên UI: ${controller.zaloQRCodeData.value}");
    print("QR ACB hiển thị trên UI: ${controller.acbQRCodeData.value}");

    showGeneralDialog(
        context: context,
        barrierDismissible: false,
        pageBuilder: (context, animation, secondaryAnimation) {
          return Scaffold(
            body: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.white,
              child: Obx(() {
                if (controller.paymentSuccess.value) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 0, 0, 20),
                        child: CustomText(
                          text: 'payment_success'.tr,
                          size: 24,
                          bold: true,
                        ),
                      ),
                      Image.asset(
                        'assets/images/self_order/payment_success.png',
                        width: 200,
                      ),
                      Container(
                        width: 150,
                        height: 50,
                        margin: EdgeInsets.fromLTRB(0, 30, 0, 0),
                        child: DefaultButton(
                          onPress: () {
                            // Clear the cart
                            final cartController = Get.find<CartSMEController>();
                            for (var item in cartController.cartItems) {
                              final product = item['product'];
                              if (product['OriginalPrice'] != null) {
                                product['Price'] = product['OriginalPrice'];
                              }
                            }
                            cartController.clearCart();

                            Get.back();
                            Get.back();
                          },
                          title: 'new_order'.tr,
                          titleStyle: PrimaryFont.bold.copyWith(
                            fontSize: 16,
                            color: AppColors.background,
                          ),
                          color: AppColors.primary,
                          widthPercentage: 1,
                          borderRadius: 10,
                          heightPercentage: 0.12,
                        ),
                      ),
                    ],
                  );
                }

                return SingleChildScrollView(
                  padding: EdgeInsets.only(top: 120),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 0, 0, 10),
                        child: CustomText(
                          text: 'scan_to_pay'.tr,
                          size: 24,
                          bold: true,
                        ),
                      ),
                      if (controller.paymentMethod.value == 'ZALOPAY')
                        Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Image.asset(
                                'assets/images/self_order/zalopay.png',
                                width: 50,
                              ),
                              CustomText(
                                text: 'ZaloPay',
                                size: 24,
                                bold: true,
                                color: AppColors.primary,
                              )
                            ],
                          ),
                        ),
                      if (controller.paymentMethod.value == 'ACB')
                        Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Image.asset('assets/images/self_order/acb.png', width: 70),
                              //CustomText(text: 'ACB', size: 24, bold: true, color: AppColors.primary)
                            ],
                          ),
                        ),
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 320,
                            height: 320,
                            decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary, width: 20),
                                borderRadius: BorderRadius.circular(40),
                                color: Colors.white),
                          ),
                          if (controller.paymentMethod.value == 'ZALOPAY' &&
                              controller.zaloQRCodeData.value.isNotEmpty)
                            QrImageView(
                              data: controller.zaloQRCodeData.value,
                              version: QrVersions.auto,
                              size: 250,
                            ),
                          if (controller.paymentMethod.value == 'ACB' &&
                              controller.acbQRCodeData.value.isNotEmpty)
                            QrImageView(
                              data: controller.acbQRCodeData.value,
                              version: QrVersions.auto,
                              size: 250,
                            ),
                          // if (controller.paymentTimeout.value == 0)
                          //   Container(
                          //     width: 320,
                          //     height: 320,
                          //     decoration: BoxDecoration(
                          //         borderRadius: BorderRadius.circular(40),
                          //         color: AppColors.shadow.withOpacity(0.8)),
                          //     child: Center(
                          //       child: Column(
                          //         mainAxisAlignment: MainAxisAlignment.center,
                          //         children: [
                          //           Text(
                          //             'Mã QR đã hết hạn',
                          //             style: TextStyle(
                          //                 fontSize: 18,
                          //                 fontWeight: FontWeight.bold,
                          //                 color: Colors.white),
                          //           ),
                          //           SizedBox(height: 20),
                          //           InkWell(
                          //             onTap: () async {
                          //               // Gọi hàm refresh khi người dùng bấm
                          //               await controller.refreshQRCode();
                          //             },
                          //             child: Container(
                          //               decoration: BoxDecoration(
                          //                   borderRadius:
                          //                   BorderRadius.circular(180),
                          //                   color: AppColors.primary),
                          //               padding: EdgeInsets.all(15),
                          //               child: Icon(Icons.refresh,
                          //                   color: Colors.white, size: 50),
                          //             ),
                          //           ),
                          //           SizedBox(height: 10),
                          //           Text(
                          //             'Nhấn để làm mới mã QR',
                          //             style: TextStyle(
                          //                 fontSize: 14, color: Colors.white),
                          //           )
                          //         ],
                          //       ),
                          //     ),
                          //   )
                        ],
                      ),
                      // if (controller.paymentTimeout.value > 0)
                      //   Padding(
                      //       padding: EdgeInsets.fromLTRB(0, 5, 0, 0),
                      //       child: Row(
                      //         mainAxisAlignment: MainAxisAlignment.center,
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         children: [
                      //           CustomText(
                      //             text: 'Mã hết hạn sau: ',
                      //             size: 14,
                      //           ),
                      //           CustomText(
                      //             text: '${controller.paymentTimeout.value}s',
                      //             bold: true,
                      //             color: AppColors.primary,
                      //             size: 14,
                      //           ),
                      //         ],
                      //       ))
                      // else
                      //   Padding(
                      //       padding: EdgeInsets.fromLTRB(0, 5, 0, 0),
                      //       child: Row(
                      //         mainAxisAlignment: MainAxisAlignment.center,
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         children: [
                      //           CustomText(
                      //             text: 'Mã QR đã hết hạn',
                      //             size: 14,
                      //           ),
                      //         ],
                      //       )),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 10, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: '${'amount'.tr}: ',
                                italic: true,
                                size: 18,
                              ),
                              CustomText(
                                text: currencyFormat.format(controller.paymentAmount.value),
                                bold: true,
                                italic: true,
                                color: AppColors.primary,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: '${'content'.tr}: ',
                                italic: true,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: 'Thanh toán đơn hàng',
                                bold: true,
                                italic: true,
                                color: AppColors.primary,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: '#${controller.orderCode.value}',
                                bold: true,
                                italic: true,
                                color: AppColors.primary,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 20, 0, 0),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 100,
                                  margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                  child: DefaultButton(
                                    onPress: () {
                                      showDialog(
                                          context: context,
                                          builder: (context) {
                                            return AlertDialog(
                                              title: CustomText(text: 'confirm_cancel_payment'.tr),
                                              actions: [
                                                Container(
                                                  width: 100,
                                                  margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                                  child: DefaultButton(
                                                    onPress: () {
                                                      Get.back();
                                                    },
                                                    title: 'back'.tr,
                                                    titleStyle: PrimaryFont.bold.copyWith(
                                                      fontSize: 16,
                                                      color: AppColors.background,
                                                    ),
                                                    color: AppColors.shadow,
                                                    widthPercentage: 1,
                                                    borderRadius: 10,
                                                    heightPercentage: 0.12,
                                                  ),
                                                ),
                                                Container(
                                                  width: 100,
                                                  margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                                  child: DefaultButton(
                                                    onPress: (){
                                                      controller.cancelACBOrder();
                                                      Get.back();
                                                      Get.back(); // Đóng dialog QR
                                                    },
                                                    title: 'confirm'.tr,
                                                    titleStyle: PrimaryFont.bold.copyWith(
                                                      fontSize: 16,
                                                      color: AppColors.background,
                                                    ),
                                                    color: AppColors.primary,
                                                    widthPercentage: 1,
                                                    borderRadius: 10,
                                                    heightPercentage: 0.1,
                                                  ),
                                                ),
                                              ],
                                            );
                                          });
                                    },
                                    title: 'cancel'.tr,
                                    titleStyle: PrimaryFont.bold.copyWith(
                                      fontSize: 16,
                                      color: AppColors.background,
                                    ),
                                    color: AppColors.danger,
                                    widthPercentage: 1,
                                    borderRadius: 10,
                                    heightPercentage: 0.12,
                                  ),
                                ),
                                Container(
                                  width: 100,
                                  margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                                  child: DefaultButton(
                                    onPress: () {
                                      if (GlobalVar.typePrinter == 'bluetooth') {
                                        bluetoothPrinterController.printQr();
                                      }
                                      else if (GlobalVar.typePrinter == 'usb') {
                                        usbPrinterController.printQr();
                                      }
                                      else {
                                        AppFunction.showError('Máy in chưa được kết nối');
                                      }
                                    },
                                    title: 'print_qr'.tr,
                                    titleStyle: PrimaryFont.bold.copyWith(
                                      fontSize: 16,
                                      color: AppColors.background,
                                    ),
                                    color: AppColors.primary,
                                    widthPercentage: 1,
                                    borderRadius: 10,
                                    heightPercentage: 0.12,
                                  ),
                                ),
                                // Container(
                                //   width: 100,
                                //   margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                                //   child: DefaultButton(
                                //     onPress: () {
                                //       //manual check transaction
                                //       controller.manualCheckTransaction();
                                //     },
                                //     title: 'check'.tr,
                                //     titleStyle: PrimaryFont.bold.copyWith(
                                //       fontSize: 16,
                                //       color: AppColors.background,
                                //     ),
                                //     color: AppColors.primary,
                                //     widthPercentage: 1,
                                //     borderRadius: 10,
                                //     heightPercentage: 0.12,
                                //   ),
                                // ),
                                // Nút kiểm tra thanh toán (chỉ hiển thị khi chưa thanh toán)
                                Container(
                                  width: 100,
                                  margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                                  child: DefaultButton(
                                    onPress: () {
                                      final cartController = Get.find<CartSMEController>();
                                      cartController.clearCart();

                                      Get.back();
                                    },
                                    title: 'Thanh toán sau',
                                    titleStyle: PrimaryFont.bold.copyWith(
                                      fontSize: 16,
                                      color: AppColors.background,
                                    ),
                                    color: AppColors.primary,
                                    widthPercentage: 1,
                                    borderRadius: 10,
                                    heightPercentage: 0.12,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 15),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                if (!controller.paymentSuccess.value)
                                  Container(
                                    width: 320,
                                    margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    child: DefaultButton(
                                      onPress: () => _showManualPaymentConfirmation(context),
                                      title: 'confirm_payment'.tr,
                                      titleStyle: PrimaryFont.bold.copyWith(
                                        fontSize: 16,
                                        color: AppColors.background,
                                      ),
                                      color: AppColors.primary,
                                      widthPercentage: 1,
                                      borderRadius: 10,
                                      heightPercentage: 0.12,
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                );
              }),
            ),
          );
        });
  }

  void _showPaymentSuccessDialog(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierLabel: "PaymentSuccess",
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return WillPopScope(
          onWillPop: () async => false,
          child: Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.08),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomText(
                      text: 'payment_success'.tr,
                      size: 24,
                      bold: true,
                    ),
                    const SizedBox(height: 20),
                    Image.asset(
                      'assets/images/self_order/payment_success.png',
                      width: width * 0.4,
                    ),
                    const SizedBox(height: 30),
                    SizedBox(
                      width: width * 0.5,
                      height: 50,
                      child: DefaultButton(
                        onPress: () {
                          final cartController = Get.find<CartSMEController>();
                          cartController.clearCart();
                          Get.back(); // Close dialog
                          Get.back(); // Back to previous screen
                        },
                        title: 'new_order'.tr,
                        titleStyle: PrimaryFont.bold.copyWith(
                          fontSize: 16,
                          color: AppColors.background,
                        ),
                        color: AppColors.primary,
                        widthPercentage: 1,
                        borderRadius: 10,
                        heightPercentage: 0.12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          ),
          child: child,
        );
      },
    );
  }

  void _showNoteDialog(double width) {
    final controller = Get.find<CheckoutController>();
    final textController =
    TextEditingController(text: controller.orderNote.value);

    Get.dialog(
      Dialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: width * 0.03),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: width * 0.02),
                    child: Text(
                      'note'.tr,
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.text,
                        fontSize: width * 0.04,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close,
                        color: AppColors.text, size: width * 0.05),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.02),
                child: TextField(
                  controller: textController,
                  decoration: InputDecoration(
                    hintText: 'note_hint'.tr,
                    hintStyle: PrimaryFont.regular.copyWith(
                      color: AppColors.shadow,
                      fontSize: width * 0.03,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(width * 0.01),
                    ),
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: width * 0.03, vertical: width * 0.03),
                  ),
                  maxLines: 3,
                  maxLength: 100,
                  style: PrimaryFont.regular.copyWith(
                    color: AppColors.text,
                    fontSize: width * 0.03,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  bottom: width * 0.03,
                  right: width * 0.02,
                  left: width * 0.02,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      onPressed: () => Get.back(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        minimumSize: Size(width * 0.2, width * 0.08),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(width * 0.02),
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: PrimaryFont.bold.copyWith(
                          color: AppColors.background,
                          fontSize: width * 0.03,
                        ),
                      ),
                    ),
                    SizedBox(width: width * 0.02),
                    ElevatedButton(
                      onPressed: () {
                        controller.updateOrderNote(textController.text.trim());
                        Get.back();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        minimumSize: Size(width * 0.2, width * 0.08),
                        padding: EdgeInsets.symmetric(
                            horizontal: width * 0.02, vertical: width * 0.015),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(width * 0.02),
                        ),
                      ),
                      child: Text(
                        'confirm'.tr,
                        style: PrimaryFont.bold.copyWith(
                          color: AppColors.background,
                          fontSize: width * 0.03,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Customer
  void _showCustomerSelectionDialog(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final checkoutController = Get.find<CheckoutController>();
    final searchController = TextEditingController();
    final RxList<dynamic> filteredCustomers = RxList<dynamic>([]);
    final RxBool isLoading = false.obs;
    final RxBool hasSearched = false.obs; // follow status

    void searchCustomers(String query) async {
      if (query.isEmpty) {
        filteredCustomers.clear();
        hasSearched.value = false;
        return;
      }

      isLoading.value = true;
      hasSearched.value = true;
      try {
        final result = await checkoutController.customerController.getList(
          1,
          10,
          '2025-06-01T00:00:00',
          '2077-01-01T00:00:00',
          query.isEmpty ? null : query,
        );

        final customers = result[0] as List<dynamic>;
        filteredCustomers.assignAll(customers);
      } catch (e) {
        filteredCustomers.clear();
      } finally {
        isLoading.value = false;
      }
    }
    searchCustomers('');

    Get.dialog(
      Dialog  (
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: Padding(
          padding: EdgeInsets.all(width * 0.03),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'select_customer'.tr,
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: AppColors.text, size: width * 0.05),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),

              Padding(
                padding: EdgeInsets.symmetric(vertical: width * 0.02),
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: 'search_customer'.tr,
                    prefixIcon: Icon(Icons.search, size: width * 0.05),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(width * 0.01),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(width * 0.01),
                      borderSide: BorderSide(color: AppColors.text, width: 2.0),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(width * 0.01),
                      borderSide: BorderSide(color: AppColors.text, width: 1.0),
                    ),
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      vertical: width * 0.03,
                    ),
                  ),
                  onChanged: (value) {
                    searchCustomers(value);
                  },
                ),
              ),

              SizedBox(
                width: width * 0.8,
                height: width * 0.65,
                child: Obx(() {
                  if (isLoading.value) {
                    return Center(child: CircularProgressIndicator(color: AppColors.primary,));
                  }

                  if (!hasSearched.value) {
                    return Center(
                      child: Text(
                        'enter_search_term'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.shadow,
                        ),
                      ),
                    );
                  }

                  if (filteredCustomers.isEmpty) {
                    return Center(child: Text('no_customer_found'.tr));
                  }

                  return ListView.builder(
                    itemCount: filteredCustomers.length,
                    itemBuilder: (context, index) {
                      final customer = filteredCustomers[index];
                      return ListTile(
                        title: Text(customer['FullName'] ?? ''),
                        subtitle: Text(customer['PhoneNumber'] ?? ''),
                        onTap: () {
                          checkoutController.selectedCustomer.value = {
                            'name': customer['FullName'],
                            'phone': customer['PhoneNumber'],
                            'customerNo': customer['CustomerNo'],
                          };
                          Get.back();
                        },
                      );
                    },
                  );
                }),
              ),

              Padding(
                padding: EdgeInsets.only(top: width * 0.03),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      onPressed: () => Get.back(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        minimumSize: Size(width * 0.2, width * 0.08),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(width * 0.02),
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: PrimaryFont.bold.copyWith(
                          color: AppColors.background,
                          fontSize: width * 0.03,
                        ),
                      ),
                    ),
                    SizedBox(width: width * 0.02),
                    ElevatedButton(
                      onPressed: () => _showManualCustomerInputDialog(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        minimumSize: Size(width * 0.2, width * 0.08),
                        padding: EdgeInsets.symmetric(
                            horizontal: width * 0.02, vertical: width * 0.015),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(width * 0.02),
                        ),
                      ),
                      child: Text(
                        'add_new_customer'.tr,
                        style: PrimaryFont.bold.copyWith(
                          color: AppColors.background,
                          fontSize: width * 0.03,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  void _showManualCustomerInputDialog(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final checkoutController = Get.find<CheckoutController>();
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final RxString errorText = ''.obs;

    Get.dialog(
      Dialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: Padding(
          padding: EdgeInsets.all(width * 0.03),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'enter_customer_info'.tr,
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close,
                        color: AppColors.text, size: width * 0.05),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              SizedBox(height: width * 0.02),
              Obx(() => TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'name'.tr,
                  hintText: 'enter_name'.tr,
                  hintStyle: PrimaryFont.regular.copyWith(
                    color: AppColors.shadow,
                    fontSize: width * 0.03,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.01),
                  ),
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                      horizontal: width * 0.03, vertical: width * 0.03),
                  errorText: errorText.value.isEmpty ? null : errorText.value,
                ),
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
                onChanged: (_) => errorText.value = '',
              )),
              SizedBox(height: width * 0.02),
              TextField(
                controller: phoneController,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  labelText: 'phone'.tr,
                  hintText: 'enter_phone'.tr,
                  hintStyle: PrimaryFont.regular.copyWith(
                    color: AppColors.shadow,
                    fontSize: width * 0.03,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.01),
                  ),
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                      horizontal: width * 0.03, vertical: width * 0.03),
                ),
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: width * 0.03),
                child: Obx(() {
                  if (checkoutController.isLoading.value) {
                    return Center(child: CircularProgressIndicator(color: AppColors.primary));
                  }
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton(
                        onPressed: () => Get.back(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          minimumSize: Size(width * 0.2, width * 0.08),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(width * 0.02),
                          ),
                        ),
                        child: Text(
                          'cancel'.tr,
                          style: PrimaryFont.bold.copyWith(
                            color: AppColors.background,
                            fontSize: width * 0.03,
                          ),
                        ),
                      ),
                      SizedBox(width: width * 0.02),
                      ElevatedButton(
                        onPressed: () async {
                          if (nameController.text.trim().isEmpty) {
                            errorText.value = 'name_required'.tr;
                            return;
                          }

                          await checkoutController.createNewCustomer(
                            nameController.text.trim(),
                            phoneController.text.trim(),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          minimumSize: Size(width * 0.2, width * 0.08),
                          padding: EdgeInsets.symmetric(horizontal: width * 0.02, vertical: width * 0.015),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(width * 0.02),
                          ),
                        ),
                        child: Text(
                          'save'.tr,
                          style: PrimaryFont.bold.copyWith(
                            color: AppColors.background,
                            fontSize: width * 0.03,
                          ),
                        ),
                      ),
                    ],
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showManualPaymentConfirmation(BuildContext context)  {
    final width = MediaQuery.of(context).size.width;
    final controller = Get.find<CheckoutController>();
    final noteController = TextEditingController();
    final RxString errorText = ''.obs;

    Get.dialog(
      Dialog(
        backgroundColor: AppColors.background,
        insetPadding: EdgeInsets.symmetric(
          horizontal: width * 0.05,
          vertical: width * 0.1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.03),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: width * 0.02),
                        child: Text(
                          "Xác nhận thanh toán thành công",
                          style: PrimaryFont.bold.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.04,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.close, color: AppColors.text, size: width * 0.05),
                        onPressed: () => Get.back(),
                      ),
                    ],
                  ),
                  SizedBox(height: width * 0.01),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.02),
                    child: Obx(() => TextField(
                      controller: noteController,
                      decoration: InputDecoration(
                        hintText: 'payment_note_hint'.tr,
                        hintStyle: PrimaryFont.regular.copyWith(
                          color: AppColors.shadow,
                          fontSize: width * 0.03,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(width * 0.01),
                        ),
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: width * 0.03,
                          vertical: width * 0.03,
                        ),
                        errorText:
                        errorText.value.isEmpty ? null : errorText.value,
                      ),
                      maxLines: 3,
                      maxLength: 100,
                      style: PrimaryFont.regular.copyWith(
                        color: AppColors.text,
                        fontSize: width * 0.03,
                      ),
                      onChanged: (_) => errorText.value = '',
                    )),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: width * 0.03,
                      right: width * 0.02,
                      left: width * 0.02,
                      top: width * 0.02,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          onPressed: () => Get.back(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'cancel'.tr,
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                        SizedBox(width: width * 0.02),
                        ElevatedButton(
                          onPressed: () async {
                            if (noteController.text.trim().isEmpty) {
                              errorText.value = 'payment_note_required'.tr;
                              return;
                            }

                            Get.back();

                            Get.dialog(
                              Center(
                                  child: CircularProgressIndicator(color: AppColors.primary)),
                              barrierDismissible: false,
                            );

                            try {
                              final response = await controller.updateStatusUseCase(
                                StatusRequestEntity(
                                  key: 'ACB',
                                  orderCode: controller.orderCode.value,
                                  msg: noteController.text.trim(),
                                  statusCode: 'SUCCESS',
                                ),
                              );

                              Get.back();

                              if (response.success ?? false) {
                                controller.paymentSuccess.value = true;

                                dynamic decoded = await controller.getImageBill(
                                  controller.orderCode.value,
                                  UserInfo.branchId,
                                );

                                if (decoded != null) {
                                  if (GlobalVar.typePrinter == 'bluetooth') {
                                    controller.bluetoothPrinterController.printBill(decoded);
                                  } else if (GlobalVar.typePrinter == 'usb') {
                                    controller.usbPrinterController.printBill(decoded);
                                  }
                                }
                              } else {
                                Get.snackbar('Lỗi', response.message ?? 'false');
                              }
                            } catch (e) {
                              Get.back();
                              Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi xác nhận thanh toán');
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            padding: EdgeInsets.symmetric(horizontal: width * 0.02, vertical: width * 0.015),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'confirm'.tr,
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

}
