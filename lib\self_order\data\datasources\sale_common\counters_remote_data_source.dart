import '../../../../core/constants/api_url.dart';
import '../../../../core/network/dio_client.dart';
import '../../models/sale_common/counters_response_model.dart';

abstract class CounterRemoteDataSource {
  Future<List<CounterModel>> getCounters(int branchId);
}

class CounterRemoteDataSourceImpl implements CounterRemoteDataSource {
  final DioClient dioClient;

  CounterRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<CounterModel>> getCounters(int branchId) async {
    try {
      final response = await dioClient.get(
        ApiUrl.counters,
        queryParameters: {'branchId': branchId},
      );
      return CounterListResponseModel.fromJson(response.data).counters;
    } catch (e) {
      rethrow;
    }
  }
}
