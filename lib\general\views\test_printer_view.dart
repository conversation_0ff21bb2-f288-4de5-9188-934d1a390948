import 'dart:ui' as ui;

import 'package:drago_usb_printer/drago_usb_printer.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:image/image.dart' as img;


class TestPrinterView extends StatefulWidget {
  const TestPrinterView({super.key});

  @override
  State<TestPrinterView> createState() => _TestPrinterViewState();
}

class _TestPrinterViewState extends State<TestPrinterView> {
  //variable
  DragoUsbPrinter usbPrinter = DragoUsbPrinter();
  List devices = [];
  bool connected = false;

  //function
  @override
  void initState() {
    super.initState();
    getDeviceList();
  }

  getDeviceList() async {
    List<Map<String, dynamic>> results = [];
    results = await DragoUsbPrinter.getUSBDeviceList();

    setState(() {
      devices = results;
    });
  }

  connect(item) async {
    int vendorId = int.parse(item['vendorId']);
    int productId = int.parse(item['productId']);
    bool? isConnected = await usbPrinter.connect(vendorId, productId);
    if (isConnected ?? false) {
      AppFunction.showSuccess('Kết nối thành công');
      setState(() {
        connected = true;
      });
    }
    else {
      AppFunction.showError('Kết nối thất bại');
    }
  }

  Future<ui.Image> loadImageFromAsset(String assetPath) async {
    final ByteData data = await rootBundle.load(assetPath);
    final Uint8List bytes = data.buffer.asUint8List();
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo frame = await codec.getNextFrame();
    return frame.image;
  }

  testPrint() async {
    if (connected) {
      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm80, profile);
      List<int> bytes = [];
      bytes += generator.reset();
      ui.Image image = await loadImageFromAsset('assets/images/general/sample_bill.png');
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();
      final decoded = img.decodeImage(pngBytes);
      if (decoded != null) {
        bytes += generator.image(decoded, align: PosAlign.center);
      }
      bytes += generator.feed(1);
      bytes += generator.cut();
      try {
        Uint8List dataToSend = Uint8List.fromList(bytes);
        await usbPrinter.write(dataToSend);
        await usbPrinter.close();
      }
      catch (ex) {
        AppFunction.showError('Có lỗi trong quá trình in');
        //response = 'Failed to get platform version.';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Test printer'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          InkWell(
            onTap: () async {
              getDeviceList();
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.refresh, color: Colors.white, size: 35,),
            ),
          )
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: ListView(
          padding: EdgeInsets.all(5),
          children: [
            if (!connected)
            for(dynamic item in devices)
              InkWell(
                onTap: () {
                  connect(item);
                },
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.05),
                          spreadRadius: 0,
                          blurRadius: 1,
                          offset: Offset(0, 3)
                      ),
                    ],
                  ),
                  padding: EdgeInsets.all(10),
                  margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(text: item['productName'] ?? ''),
                      CustomText(text: 'NCC: ${item['manufacturer'] ?? ''}'),
                      CustomText(text: 'NCC: ${item['vendorId'] ?? ''} - ${item['productId']}'),
                    ],
                  ),
                ),
              ),
            SizedBox(height: 30,),
            CustomButton(text: 'Test Print', onTap: () {
              testPrint();
            }, color: AppColors.primary),
          ],
        ),
      ),
    );
  }
}
