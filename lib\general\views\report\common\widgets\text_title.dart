import 'package:flutter/material.dart';

import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
class TextTitle extends StatelessWidget {
  final String title;
  final double fontSize;
  final Color? color;

  const TextTitle({
    required this.title,
    this.fontSize = 16.0,
    this.color,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: PrimaryFont.bold.copyWith(
        color: color ?? AppColors.text,
        fontSize: fontSize,
      ),
    );
  }
}
class TextTitleRegular extends StatelessWidget {
  final String title;
  final double fontSize;
  final Color? color;

  const TextTitleRegular({
    required this.title,
    this.fontSize = 16.0,
    this.color,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: PrimaryFont.regular.copyWith(
        color: color ?? AppColors.text,
        fontSize: fontSize,
      ),
    );
  }
}
