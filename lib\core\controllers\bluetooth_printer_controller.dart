import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/services/capture_bill_service.dart';
import 'package:gls_self_order/core/services/capture_qr_service.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image/image.dart' as img;
import 'package:shared_preferences/shared_preferences.dart';

class BluetoothPrinterController extends GetxController {
  RxBool isConnected = false.obs;
  RxList devices = [].obs;
  final previewBytes = Rxn<Uint8List>();
  final connectedMac = ''.obs;
  final isLoading = false.obs;

  Future<void> requestBluetoothPermission() async {
    if (await Permission.bluetoothScan.isPermanentlyDenied ||
        await Permission.bluetoothConnect.isPermanentlyDenied ||
        await Permission.location.isPermanentlyDenied) {
      openAppSettings();
      return;
    }

    if (await Permission.bluetoothScan.isDenied) {
      await Permission.bluetoothScan.request();
    }
    if (await Permission.bluetoothConnect.isDenied) {
      await Permission.bluetoothConnect.request();
    }
    if (await Permission.location.isDenied) {
      await Permission.location.request();
    }
  }

  getDeviceList() async {
    await requestBluetoothPermission();
    isLoading.value = true;
    try {
      bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
      if (!connectionStatus) {
        isConnected.value = false;
        List<BluetoothInfo> listResult =
        await PrintBluetoothThermal.pairedBluetooths;
        devices.value = [];
        await Future.forEach(listResult, (BluetoothInfo bluetooth) {
          String name = bluetooth.name;
          String mac = bluetooth.macAdress;
          devices.add({'name': name, 'mac': mac});
        });
      }
    } catch (e){
      AppFunction.showError('Không thể lấy danh sách máy in');
    } finally {
      isLoading.value = false;
    }
  }

  testData() async {
    previewBytes.value = await captureBillImage();
  }

  connectPrinter(mac) async {
    Get.snackbar('Thông tin', 'Đang kết nối: $mac');
    final bool result =
        await PrintBluetoothThermal.connect(macPrinterAddress: mac);
    if (result) {
      AppFunction.showSuccess('Kết nối máy in thành công');
      isConnected.value = true;
      GlobalVar.typePrinter = 'bluetooth';
      connectedMac.value = mac;
      savePrinterMac(mac);
    } else {
      AppFunction.showError('Kết nối thất bại');
    }
  }

  disconnectPrinter(mac) async {
    final bool result = await PrintBluetoothThermal.disconnect;
    if (result) {
      AppFunction.showSuccess('Ngắt kết nối máy in thành công');
      isConnected.value = false;
      connectedMac.value = '';
      GlobalVar.typePrinter = '';
    } else {
      AppFunction.showError('Ngắt kết nối thất bại');
    }
  }

  Future<void> savePrinterMac(String mac) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_printer_mac', mac);
  }

  Future<void> checkAndReconnectPrinter() async {
    bool status = await PrintBluetoothThermal.connectionStatus;
    if (!status) {
      final prefs = await SharedPreferences.getInstance();
      final lastMac = prefs.getString('last_printer_mac');

      if (lastMac != null && lastMac.isNotEmpty) {
        final result = await PrintBluetoothThermal.connect(macPrinterAddress: lastMac);
        if (result) {
          AppFunction.showSuccess('Kết nối máy in thành công');
          GlobalVar.typePrinter = 'bluetooth';
          print('Kết nối máy in thành công');
        } else {
          AppFunction.showError('Kết nối máy in thất bại');
          print('Kết nối máy in thất bại');
        }
      } else {
        print("⚠️ Không có MAC máy in để kết nối lại.");
      }
    }else{
      AppFunction.showSuccess('Đã Kết nối máy in thành công');
      print('Đã Kết nối máy in thành công');
    }
  }

  // Future<List<int>> billDataIsolate(img.Image decoded) async {
  //   // Load profile ở main thread
  //   final profile = await CapabilityProfile.load();
  //   return await compute(
  //       _billDataWorker, {'decoded': decoded, 'profile': profile});
  // }
  //
  // List<int> _billDataWorker(Map<String, dynamic> data) {
  //   final img.Image decoded = data['decoded'];
  //   final CapabilityProfile profile = data['profile'];
  //
  //   List<int> bytes = [];
  //   final generator = Generator(PaperSize.mm80, profile);
  //
  //   bytes += generator.reset();
  //   bytes += generator.imageRaster(decoded, align: PosAlign.center);
  //   bytes += generator.feed(1);
  //   bytes += generator.cut();
  //
  //   return bytes;
  // }
  //
  // Future<List<int>> qrDataIsolate(Uint8List pngBytes) async {
  //   final profile = await CapabilityProfile.load();
  //   return await compute(
  //       _qrDataWorker, {'pngBytes': pngBytes, 'profile': profile});
  // }
  //
  // List<int> _qrDataWorker(Map<String, dynamic> data) {
  //   final Uint8List pngBytes = data['pngBytes'];
  //   final CapabilityProfile profile = data['profile'];
  //
  //   List<int> bytes = [];
  //   final generator = Generator(PaperSize.mm80, profile);
  //
  //   bytes += generator.reset();
  //   final decoded = img.decodeImage(pngBytes);
  //   if (decoded != null) {
  //     bytes += generator.image(decoded, align: PosAlign.center);
  //   }
  //   bytes += generator.feed(1);
  //   bytes += generator.cut();
  //
  //   return bytes;
  // }

  printBill(decoded) async {
    bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
    if (connectionStatus) {
      final ticket = await billData(decoded);
      await PrintBluetoothThermal.writeBytes(ticket);
    } else {
      Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
    }
  }

  billData(decoded) async {
    List<int> bytes = [];
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);
    bytes += generator.reset();
    if (decoded != null) {
      bytes += generator.image(decoded, align: PosAlign.center);
    }
    bytes += generator.feed(1);
    bytes += generator.cut();
    return bytes;
  }

  printQr() async {
    bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
    if (connectionStatus) {
      List<int> ticket = await qrData();
      final result = await PrintBluetoothThermal.writeBytes(ticket);
    } else {
      Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
    }
  }

  qrData() async {
    List<int> bytes = [];
    // Using default profile
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);
    bytes += generator.reset();

    final pngBytes = await captureQrImage();
    final decoded = img.decodeImage(pngBytes);
    if (decoded != null) {
      bytes += generator.image(decoded, align: PosAlign.center);
    }

    bytes += generator.feed(1);
    bytes += generator.cut();
    return bytes;
  }

  printBillFastFromImage(img.Image decodedImg) async {
    bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
    if (!connectionStatus) {
      Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
      return;
    }

    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);

    List<int> bytes = [];
    bytes += generator.reset();
    bytes += generator.imageRaster(decodedImg, align: PosAlign.center);
    bytes += generator.feed(1);
    bytes += generator.cut();

    await PrintBluetoothThermal.writeBytes(bytes);
  }

  String removeVietnameseDiacritics(String str) {
    const vietnameseMap = {
      'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
      'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
      'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
      'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
      'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
      'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
      'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
      'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
      'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
      'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
      'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
      'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
      'đ': 'd',
      'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
      'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
      'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
      'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
      'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
      'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
      'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
      'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
      'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
      'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
      'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
      'Ỳ': 'Y', 'Ý': 'Y', 'Ỵ': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y',
      'Đ': 'D'
    };

    return str.split('').map((char) => vietnameseMap[char] ?? char).join();
  }


  // printTest() async {
  //   bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
  //   if (!connectionStatus) {
  //     Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
  //     return;
  //   }
  //
  //   final profile = await CapabilityProfile.load();
  //   final generator = Generator(PaperSize.mm80, profile);
  //
  //   List<int> bytes = [];
  //   bytes += generator.reset();
  //
  //   String longText = '''
  //   HÓA ĐƠN BÁN HÀNG
  //   Số bàn: 12
  //   Nhân viên: Nguyễn Văn A
  //   Thời gian: 12:34 - 07/08/2025
  //   ----------------------------
  //   1. Trà sữa đặc biệt        45.000đ
  //   2. Cà phê sữa đá           25.000đ
  //   3. Bánh ngọt              30.000đ
  //   ----------------------------
  //   Tổng cộng:               100.000đ
  //   Tiền khách đưa:          200.000đ
  //   Tiền thối lại:           100.000đ
  //   ----------------------------
  //   Cảm ơn quý khách. Hẹn gặp lại!
  // ''';
  //
  //   // Xử lý loại bỏ dấu
  //   longText = removeVietnameseDiacritics(longText);
  //
  //   bytes += generator.text(longText,
  //       styles: PosStyles(align: PosAlign.left, bold: false));
  //
  //   bytes += generator.feed(1);
  //   bytes += generator.cut();
  //
  //   await PrintBluetoothThermal.writeBytes(bytes);
  // }

  Future<void> printTest() async {
    bool connectionStatus = await PrintBluetoothThermal.connectionStatus;

    if (!connectionStatus) {
      Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
      return;
    }

    try {
      // Đọc nội dung từ file .txt trong assets
      String hexString = await rootBundle.loadString('assets/pdfs/hex.txt');

      // Chuyển thành List<int>
      List<int> bytes = hexString
          .trim()
          .split(RegExp(r'\s+')) // chia bởi khoảng trắng hoặc newline
          .map((hex) => int.parse(hex, radix: 16))
          .toList();



      // Gửi lệnh in
      await PrintBluetoothThermal.writeBytes(bytes);
    } catch (e) {
      Get.snackbar('Lỗi', 'Không thể in từ file: $e');
    }
  }
}
