import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_style.dart';
import '../../../../../core/widgets/card_decoration.dart';
import '../../domain/report/entities/report.dart';

class TopBottomBranchesRevenue extends StatelessWidget {
  final double width;
  final List<BranchesRevenueEntity> branches;
  final String title;

  const TopBottomBranchesRevenue({
    super.key,
    required this.width,
    required this.branches,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0);

    double totalRevenue = branches.fold(0.0, (sum, e) => sum + (e.paymentAmount ?? 0));
    bool isEmptyData = totalRevenue == 0;

    List<Color> colors = [
      Colors.blue,
      Colors.orange,
      Colors.pinkAccent,
      Colors.green,
      Colors.purpleAccent,
    ];

    List<PieChartSectionData> sections = isEmptyData
        ? [
      PieChartSectionData(
        value: 1,
        color: Colors.grey.withOpacity(0.3),
        radius: width * 0.12,
        title: "0%",
        titleStyle: TextStyle(fontSize: width * 0.03, color: Colors.white),
      ),
    ]
        : branches.asMap().entries.map((entry) {
      int index = entry.key;
      var branch = entry.value;
      double revenue = branch.paymentAmount ?? 0;
      double percentage = (revenue / totalRevenue) * 100;

      return PieChartSectionData(
        value: revenue,
        title: "${percentage.toStringAsFixed(1)}%",
        color: colors[index % colors.length],
        radius: width * 0.12,
        titleStyle: TextStyle(fontSize: width * 0.03, color: Colors.white),
      );
    }).toList();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: width * 0.015),
      child: Container(
        padding: EdgeInsets.all(width * 0.03),
        decoration: cardDecoration(width * 0.03, AppColors.background),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              title.toUpperCase(),
              style: PrimaryFont.bold.copyWith(
                color: AppColors.text,
                fontSize: width * 0.04,
              ),
            ),
            SizedBox(height: width * 0.02),
            SizedBox(
              height: width * 0.5,
              child: PieChart(
                PieChartData(
                  sections: sections,
                  borderData: FlBorderData(show: false),
                  centerSpaceRadius: width * 0.12,
                  sectionsSpace: 2,
                ),
              ),
            ),
            SizedBox(height: width * 0.02),
            Align(
              alignment: Alignment.center,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: isEmptyData
                    ? [
                  Text(
                    "Không có dữ liệu",
                    style: PrimaryFont.regular.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.03,
                    ),
                  ),
                ]
                    : branches.asMap().entries.map((entry) {
                  int index = entry.key;
                  var branch = entry.value;

                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: width * 0.01),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: width * 0.03,
                          height: width * 0.03,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: colors[index % colors.length],
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          "${branch.branchName} - ${currencyFormat.format(branch.paymentAmount ?? 0)}",
                          style: PrimaryFont.regular.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.03,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
