import 'package:flutter/services.dart';

class VatPercentFormatter extends TextInputFormatter {
  final double min;
  final double max;
  final int decimalPlaces;

  VatPercentFormatter({
    this.min = 0.0,
    this.max = 100.0,
    this.decimalPlaces = 1,
  });

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final text = newValue.text;

    // Cho phép rỗng
    if (text.isEmpty) {
      return newValue;
    }

    final regex = RegExp(r'^\d*\.?\d*$');
    if (!regex.hasMatch(text)) {
      return oldValue;
    }

    if (text.contains('.')) {
      final parts = text.split('.');
      if (parts.length > 2 || parts[1].length > decimalPlaces) {
        return oldValue;
      }
    }

    final value = double.tryParse(text);
    if (value == null) {
      return oldValue;
    }

    if (value < min || value > max) {
      return oldValue;
    }

    return newValue;
  }
}
