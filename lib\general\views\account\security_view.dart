import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_on_off.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';
import 'package:gls_self_order/general/views/account/change_password_view.dart';
import 'package:gls_self_order/general/views/account/delete_account_view.dart';

class SecurityView extends StatefulWidget {
  const SecurityView({super.key});

  @override
  State<SecurityView> createState() => _SecurityViewState();
}

class _SecurityViewState extends State<SecurityView> {
  //variable
  AuthController authController = Get.find();
  bool enableBiometric = false;

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    checkUseBio();
  }

  checkUseBio() async {
    enableBiometric = await authController.checkUseBio();
    setState(() {

    });
  }

  Widget renderMenuItem(title, icon, VoidCallback view) {
    return InkWell(
      onTap: view,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: AppColors.primary.withValues(alpha: 0.1)
        ),
        padding: EdgeInsets.all(10),
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(180)
              ),
              margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
              child: Icon(icon, size: 30, color: AppColors.primary,),
            ),
            Expanded(
              child: CustomText(text: title, size: 18, bold: true,),
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Bảo mật'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [

        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            renderMenuItem('Đổi mật khẩu', Icons.password, () => Get.to(() => ChangePasswordView())),
            renderMenuItem('Yêu cầu xoá tài khoản', Icons.no_accounts, () => Get.to(() => DeleteAccountView())),
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: AppColors.primary.withValues(alpha: 0.1)
              ),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(180)
                    ),
                    margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                    child: Icon(Icons.fingerprint, size: 30, color: AppColors.primary,),
                  ),
                  Expanded(
                    child: CustomText(text: 'Bật sinh trắc học', size: 18, bold: true,),
                  ),
                  CustomOnOff(controller: enableBiometric, onTap: () async {
                    if (enableBiometric) {
                      await authController.disableBio();
                      await authController.removeDataBio();
                      setState(() {
                        enableBiometric = false;
                      });
                      AppFunction.showSuccess('Đã tắt  đăng nhập bằng sinh trắc học');
                    }
                    else {
                      await authController.enableBio();
                      await authController.saveDataBio(UserInfo.username, UserInfo.password);
                      setState(() {
                        enableBiometric = true;
                      });
                      AppFunction.showSuccess('Đã bật đăng nhập bằng sinh trắc học');
                    }
                  })
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
