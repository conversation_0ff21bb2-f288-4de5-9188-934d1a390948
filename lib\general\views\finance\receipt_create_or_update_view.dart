import 'package:flutter/material.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_header_modal.dart';
import 'package:gls_self_order/core/components/custom_money_field.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:gls_self_order/general/controllers/receipt_controller.dart';
import 'package:intl/intl.dart';

class ReceiptCreateOrUpdateView extends StatefulWidget {
  final String type;
  final dynamic item;
  const ReceiptCreateOrUpdateView({super.key, required this.type, this.item});

  @override
  State<ReceiptCreateOrUpdateView> createState() => _ReceiptCreateOrUpdateViewState();
}

class _ReceiptCreateOrUpdateViewState extends State<ReceiptCreateOrUpdateView> {
  //variable
  GeneralController generalController = Get.find();
  ReceiptController receiptController = Get.find();
  CustomerController customerController = Get.find();
  List paymentMethods = [];
  List items = [];
  List groups = [];
  TextEditingController userController = TextEditingController();
  TextEditingController receiptCodeController = TextEditingController();
  TextEditingController dateController = TextEditingController();
  TextEditingController customerNameController = TextEditingController();
  TextEditingController typeController = TextEditingController();
  TextEditingController itemController = TextEditingController();
  TextEditingController currencyTypeController = TextEditingController();
  TextEditingController rateController = TextEditingController();
  TextEditingController groupController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  TextEditingController paymentMethodController = TextEditingController();
  TextEditingController noteController = TextEditingController();
  TextEditingController searchCustomerController = TextEditingController();
  int paymentMethodSelected = 0;
  int itemSelected = 0;
  int groupSelected = 0;
  DateTime today = DateTime.now();
  DateTime receiptDate = DateTime.now();
  int customerId = 0;
  String customerName = '';
  String customerPhone = '';
  String customerAddress = '';
  List customerList = [];
  dynamic customerPagination = {
    'current_page': 1,
    'page_size': 50,
    'total_page': 0,
    'total_records': 0,
    'item_count': 0
  };
  List genders = [
    {'id': 1, 'name': 'Nam'},
    {'id': 2, 'name': 'Nữ'},
  ];

  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    if (widget.item != null) {
      await getDetail();
    }
    else {
      userController.text = UserInfo.user != null ? UserInfo.user['ObjectName'] : '';
      dateController.text = AppFunction.formatDateWithTime(receiptDate);
      currencyTypeController.text = 'VND';
      rateController.text = '1';
    }
    getGroupList();
    getItemList();
    getPaymentMethodList();
    getCustomerList(null);
  }

  getDetail() async {
    dynamic item = await receiptController.getDetail(widget.item['Id']);
    if (item != null) {
      userController.text = item['CreatedByName'] ?? '';
      receiptCodeController.text = item['ReceiptCode'] ?? '';
      dateController.text = AppFunction.formatDateWithTime(item['CreatedAt']);
      customerNameController.text = item['CustomerName'] ?? '';
      typeController.text = '';
      itemController.text = '';
      groupController.text = '';
      currencyTypeController.text = 'VND';
      rateController.text = '1';
      amountController.text = toCurrencyString(
        item['Amount'].toStringAsFixed(0),
        leadingSymbol: '',
        useSymbolPadding: true,
        thousandSeparator: ThousandSeparator.Period,
        mantissaLength: 0,
      );
      paymentMethodSelected = item['PaymentMethodId'];
      groupSelected = item['ReceiptGroupId'];
      itemSelected = item['ReceiptItemId'];
      noteController.text = item['Note'] ?? '';
      customerId = item['CustomerId'];
      customerName = item['CustomerName'];
      customerPhone = item['CustomerPhone'];
      customerAddress = item['CustomerAddress'];
      setState(() {

      });
    }
  }

  getPaymentMethodList() async {
    paymentMethods = await generalController.getPaymentMethodList();
    if(widget.item != null) {
      for(dynamic item in paymentMethods) {
        if(item['PayId'] == paymentMethodSelected) {
          paymentMethodController.text = item['PayName'];
        }
      }
    }
  }

  getGroupList() async {
    groups = await generalController.getGroupList(widget.type);
    if(widget.item != null) {
      for(dynamic item in groups) {
        if(item['ReceiptGroupId'] == groupSelected) {
          groupController.text = item['ReceiptGroupName'];
        }
      }
    }
  }

  getItemList() async {
    items = await generalController.getItemList(widget.type);
    if(widget.item != null) {
      for(dynamic item in items) {
        if(item['ReceiptItemId'] == itemSelected) {
          itemController.text = item['ReceiptItemName'];
        }
      }
    }
  }

  getCustomerList(keySearch) async {
    List resp = await customerController.getList(
        customerPagination['current_page'],
        customerPagination['page_size'],
        '2025-06-01',
        '2077-01-01',
        keySearch
    );
    customerList = resp[0];
    dynamic pagination = resp[1];
    if (pagination != null) {
      customerPagination['total_page'] = pagination['TotalPages'];
      customerPagination['total_records'] = pagination['TotalRecords'];
      customerPagination['item_count'] = pagination['ItemCount'];
    }
  }

  pickGroup() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for(dynamic item in groups)
                  InkWell(
                    onTap: () {
                      groupSelected = item['ReceiptGroupId'];
                      groupController.text = item['ReceiptGroupName'];
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color: AppColors.shadow.withValues(alpha: 0.1)
                              )
                          ),
                          color: item['ReceiptGroupId'] == groupSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.white
                      ),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['ReceiptGroupName']),
                    ),
                  )
              ],
            ),
          );
        }
    );
  }

  pickItem() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for(dynamic item in items)
                  InkWell(
                    onTap: () {
                      itemSelected = item['ReceiptItemId'];
                      itemController.text = item['ReceiptItemName'];
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color: AppColors.shadow.withValues(alpha: 0.1)
                              )
                          ),
                          color: item['ReceiptItemId'] == itemSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.white
                      ),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['ReceiptItemName']),
                    ),
                  )
              ],
            ),
          );
        }
    );
  }

  pickPaymentMethod() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for(dynamic item in paymentMethods)
                  InkWell(
                    onTap: () {
                      paymentMethodSelected = item['PayId'];
                      paymentMethodController.text = item['PayName'];
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color: AppColors.shadow.withValues(alpha: 0.1)
                              )
                          ),
                          color: item['PayId'] == paymentMethodSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.white
                      ),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['PayName']),
                    ),
                  )
              ],
            ),
          );
        }
    );
  }

  Widget renderItemCustomer(item) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)
          ),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Container(
              //   width: 50,
              //   height: 50,
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(180)
              //   ),
              //   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
              //   clipBehavior: Clip.antiAlias,
              //   child: Image.asset('assets/images/general/avatar.jpg'),
              // ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(child: CustomText(text: item['FullName'] ?? '', bold: true, size: 18,),),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CustomText(text: '📞${item['PhoneNumber'] ?? ''}'),
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  pickCustomer() async {
    dynamic customer = {
      'id': null,
      'name': null,
      'phone': null,
      'address': null
    };
    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
      ),
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      useSafeArea: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, StateSetter setStateIn) {
            return SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Column(
                children: [
                  CustomHeaderModal(
                    title: 'Chọn khách hàng',
                    append: InkWell(
                      onTap: () async {
                        dynamic customer_ = await addCustomer();
                        if (customer_ != null) {
                          customer = customer_;
                          Get.back();
                        }
                      },
                      child: Icon(Icons.add, color: Colors.white, size: 30,),
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(10),
                    child: Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: searchCustomerController,
                            showLabel: false,
                            hint: 'Tìm kiếm khách hàng',
                            space: false,
                          ),
                        ),
                        InkWell(
                          onTap: () async {
                            AppFunction.showLoading();
                            await getCustomerList(searchCustomerController.text);
                            setStateIn(() {});
                            AppFunction.hideLoading();
                          },
                          child: Container(
                            width: 45,
                            height: 45,
                            margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
                            decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(10)
                            ),
                            child: Icon(Icons.search, color: Colors.white,),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.all(10),
                      children: [
                        for(dynamic item in customerList)
                          InkWell(
                            onTap: () {
                              customer = {
                                'id': item['CustomerId'],
                                'name': item['FullName'],
                                'phone': item['PhoneNumber'],
                                'address': item['CustomerAdress']
                              };
                              Get.back();
                            },
                            child: renderItemCustomer(item),
                          )
                      ],
                    ),
                  )
                ],
              ),
            );
          },
        );
      }
    );
    return customer;
  }

  addCustomer() async {
    TextEditingController nameController = TextEditingController();
    TextEditingController phoneController = TextEditingController();
    TextEditingController dobController = TextEditingController();
    TextEditingController addressController = TextEditingController();
    int gender = 1;
    DateTime? dateOfBirth;
    dynamic customer;
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        useSafeArea: true,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, StateSetter setStateIn) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                child: Column(
                  children: [
                    CustomHeaderModal(
                        title: 'Thêm khách hàng',
                        append: CustomSaveButton(onTap: () async{
                          int customerId = await customerController.postCreate(
                              nameController.text,
                              phoneController.text,
                              dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth!) : null,
                              gender,
                              addressController.text
                          );
                          if (customerId != 0) {
                            customer = {
                              'id': customerId,
                              'name': nameController.text,
                              'phone': phoneController.text,
                              'address': addressController.text
                            };
                            Get.back();
                          }
                        }, hasPadding: false,)
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          CustomTextField(
                            controller: nameController,
                            label: 'Họ tên',
                            required: true,
                          ),
                          CustomTextField(
                            controller: phoneController,
                            label: 'Số điện thoại',
                          ),
                          CustomTextField(
                            controller: dobController,
                            label: 'Ngày sinh',
                            readOnly: true,
                            onTap: () async {
                              dateOfBirth = await selectDateOfBirth(context);
                              if (dateOfBirth != null) {
                                dobController.text = AppFunction.formatDateNoTime(dateOfBirth);
                              }
                            },
                          ),
                          Padding(
                            padding: EdgeInsets.fromLTRB(0, 0, 0, 2.5),
                            child: CustomText(text: 'Giới tính',),
                          ),
                          DecoratedBox(
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(7.5),
                                border: Border.all(
                                    width: 0.5,
                                    color: Colors.black.withValues(alpha: 0.2)
                                )
                            ),
                            child: DropdownButton(
                              value: gender,
                              items: [
                                for (dynamic item in genders)
                                  DropdownMenuItem(
                                    value: item['id'],
                                    child: CustomText(text: item['name']),
                                  )
                              ],
                              onChanged: (value) {
                                gender = int.parse(value.toString());
                                setStateIn(() {

                                });
                              },
                              underline: Container(),
                              isExpanded: true,
                              padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                            ),
                          ),
                          SizedBox(height: 10,),
                          CustomTextField(
                            controller: addressController,
                            label: 'Địa chỉ',
                            maxLines: 3,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              );
            },
          );
        }
    );
    return customer;
  }

  Future<DateTime?> selectDateOfBirth(BuildContext context) async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: today,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (date != null) {
      DateTime dateOfBirth = DateTime(
        date.year,
        date.month,
        date.day,
      );
      return dateOfBirth;
    }
    return null;
  }

  Future<void> selectDate(BuildContext context) async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: today,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (date != null) {
      final TimeOfDay? time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(today),
      );
      if (time != null) {
        final newDateTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );
        receiptDate = newDateTime;
        dateController.text = AppFunction.formatDateWithTime(receiptDate);
      }
    }
  }

  save() async {
    AppFunction.showLoading();
    int receiptId = await receiptController.postCreateOrUpdate(
        widget.item != null ? widget.item['Id'] : null,
        UserInfo.user != null ? UserInfo.user['ObjectId'] : 0,
        DateFormat('yyyy-MM-ddTHH:mm:ss').format(receiptDate),
        customerId,
        groupSelected,
        currencyTypeController.text,
        rateController.text,
        itemSelected,
        toNumericString(amountController.text),
        paymentMethodSelected,
        noteController.text,
      customerName,
      customerPhone,
      customerAddress,
      widget.type
    );
    if (receiptId != 0) {
      Get.back(result: receiptId);
    }
    AppFunction.hideLoading();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: widget.type == 'THU' ? 'Tạo phiếu thu' : 'Tạo phiếu chi'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: userController,
                    label: 'Nhân viên',
                    readOnly: true,
                    disabled: true,
                  ),
                ),
              ],
            ),
            if (widget.item != null)
              CustomTextField(
                controller: receiptCodeController,
                label: 'Số phiếu',
                readOnly: true,
                disabled: true,
              ),
            CustomTextField(
              controller: dateController,
              label: 'Ngày thu',
              readOnly: true,
              onTap: () {
                selectDate(context);
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: customerNameController,
                    label: widget.type == 'THU' ? 'Khách hàng' : 'Người nhận',
                    readOnly: true,
                  ),
                ),
                if (customerNameController.text.isNotEmpty)
                InkWell(
                  onTap: () {
                    customerId = 0;
                    customerName = '';
                    customerPhone = '';
                    customerAddress = '';
                    customerNameController.text = '';
                    setState(() {

                    });
                  },
                  child: Container(
                    width: 30,
                    height: 30,
                    margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                    decoration: BoxDecoration(
                        color: AppColors.shadow,
                        borderRadius: BorderRadius.circular(10)
                    ),
                    child: Icon(Icons.close, color: Colors.white,),
                  ),
                ),
                if (customerNameController.text.isEmpty)
                InkWell(
                  onTap: () async {
                    dynamic customer = await pickCustomer();
                    customerId = customer['id'] ?? 0;
                    customerName = customer['name'] ?? '';
                    customerPhone = customer['phone'] ?? '';
                    customerAddress = customer['address'] ?? '';
                    customerNameController.text = customerName;
                    setState(() {

                    });
                  },
                  child: Container(
                    width: 30,
                    height: 30,
                    margin: EdgeInsets.fromLTRB(5, 0, 0, 15),
                    decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(10)
                    ),
                    child: Icon(Icons.search, color: Colors.white,),
                  ),
                )
              ],
            ),
            CustomTextField(
              controller: groupController,
              label: 'Danh mục',
              readOnly: true,
              required: true,
              isSelect: true,
              onTap: () {
                pickGroup();
              },
            ),
            CustomTextField(
              controller: currencyTypeController,
              label: 'Loại tiền',
              readOnly: true,
              onTap: () {

              },
            ),
            // CustomTextField(
            //   controller: rateController,
            //   label: 'Tỉ giá:',
            //   keyboard: TextInputType.number,
            // ),
            CustomTextField(
              controller: itemController,
              label: 'Khoản mục',
              readOnly: true,
              required: true,
              isSelect: true,
              onTap: () {
                pickItem();
              },
            ),
            CustomMoneyField(
              controller: amountController,
              label: widget.type == 'THU' ? 'Tiền thu' : 'Tiền chi',
              required: true,
            ),
            CustomTextField(
              controller: paymentMethodController,
              label: 'Phương thức',
              readOnly: true,
              required: true,
              isSelect: true,
              onTap: () {
                pickPaymentMethod();
              },
            ),
            CustomTextField(
              controller: noteController,
              label: 'Lý do',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }
}
