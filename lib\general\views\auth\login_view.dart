import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/classes/local_auth.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/validators/auth_validator.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';
import 'package:gls_self_order/general/views/account/apply_evoucher_view.dart';
import 'package:gls_self_order/general/views/auth/config_printer_view.dart';
import 'package:gls_self_order/general/views/auth/register_view.dart';
import 'package:gls_self_order/general/views/home_main_view.dart';

class LoginView extends StatefulWidget {
  final bool autoLogin;

  const LoginView({super.key, this.autoLogin = false});

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  //variable
  AuthController authController = Get.find<AuthController>();
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController branchController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool rememberMe = false;
  List branches = [];
  int branchSelected = 0;
  int organizeId = 0;

  //function
  @override
  void initState() {
    super.initState();
    getData();

    if (widget.autoLogin) {
      Future.delayed(const Duration(milliseconds: 300), () {
        login();
      });
    }
  }

  getData() async {
    await getRememberAccount();
  }

  getRememberAccount() async {
    dynamic account = await authController.getRememberAccount();
    usernameController.text = account['username'];
    passwordController.text = account['password'];
    bool useBio = await authController.checkUseBio();
    if (useBio) {
      passwordController.text = '';
      dynamic dataBio = await authController.getDataBio();
    }
    if (account['username'] != '' &&
        account['password'] != '' &&
        account['branch_id'] != 0) {
      rememberMe = true;
      setState(() {});
      for (dynamic item in branches) {
        if (item['BranchId'] == account['branch_id']) {
          organizeId = item['OrgId'];
          branchSelected = account['branch_id'];
          branchController.text = item['BranchName'];
          break;
        }
      }
    } else {
      if (branches.isNotEmpty) {
        organizeId = branches[0]['OrgId'];
        branchSelected = branches[0]['BranchId'];
        branchController.text = branches[0]['BranchName'];
      }
    }
  }

  pickBranch() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for (dynamic item in branches)
                  InkWell(
                    onTap: () {
                      organizeId = item['OrgId'];
                      branchSelected = item['BranchId'];
                      branchController.text = item['BranchName'];
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color:
                                      AppColors.shadow.withValues(alpha: 0.1))),
                          color: item['BranchId'] == branchSelected
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : Colors.white),
                      padding: const EdgeInsets.all(15),
                      child: CustomText(text: item['BranchName']),
                    ),
                  )
              ],
            ),
          );
        });
  }

  login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    AppFunction.showLoading();
    dynamic result = await authController.login(
        usernameController.text, passwordController.text, rememberMe);

    // Kiểm tra nếu result là Map (có lỗi license)
    if (result is Map && result['code'] == 1403) {
      AppFunction.hideLoading();
      _showLicenseErrorDialog(result['message']);
      return;
    }

    if (result == true) {
      await authController.getUserInfo();
      UserInfo.username = usernameController.text;
      UserInfo.password = passwordController.text;
      bool useBio = await authController.checkUseBio();
      if (useBio) {
        dynamic data = await authController.getDataBio();
        if (UserInfo.username != data['username'] ||
            UserInfo.password != data['password']) {
          await authController.disableBio();
          await authController.removeDataBio();
        }
      }
      Get.to(() => const HomeMainView());
    }
    AppFunction.hideLoading();
  }

  loginWithBio(bool showError) async {
    bool success = await authController.checkUseBio();
    if (success) {
      final authenticate = await LocalAuth.authenticate();
      if (authenticate) {
        AppFunction.showLoading();
        dynamic data = await authController.getDataBio();
        bool success = await authController.login(
            data['username'], data['password'], true);
        if (success) {
          UserInfo.username = data['username'];
          UserInfo.password = data['password'];
          await authController.saveDataBio(data['username'], data['password']);
          await authController.getUserInfo();
          Get.to(() => const HomeMainView());
        }
        AppFunction.hideLoading();
      }
    } else {
      if (showError) {
        AppFunction.showError(
            'Vui lòng kích hoạt tính năng đăng nhập bằng sinh trắc học');
      }
    }
  }

  _showLicenseErrorDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const CustomText(text: 'Lỗi License', size: 20, bold: true),
          content: CustomText(text: message),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Get.back();
              },
              child: const CustomText(text: 'Hủy'),
            ),
            TextButton(
              onPressed: () {
                Get.back();
                Get.to(() => const ApplyEvoucherView());
              },
              child: const CustomText(
                  text: 'Kích hoạt key', color: AppColors.primary),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            Expanded(
              child: SizedBox(
                width: width > GlobalVar.maxWidth
                    ? GlobalVar.maxWidth
                    : double.infinity,
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Container(
                    width: double.infinity,
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height -
                          MediaQuery.of(context).padding.top -
                          MediaQuery.of(context).padding.bottom,
                    ),
                    padding: const EdgeInsets.all(20),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/general/banner.png',
                            width: 250,
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          CustomTextField(
                            controller: usernameController,
                            label: 'Tên đăng nhập',
                            validator: AuthValidator.validatorUsername,
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          CustomTextField(
                            controller: passwordController,
                            label: 'Mật khẩu',
                            secure: true,
                            validator: AuthValidator.validatorPassword,
                          ),
                          InkWell(
                            onTap: () {
                              setState(() {
                                rememberMe = !rememberMe;
                              });
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 25,
                                  height: 25,
                                  margin: const EdgeInsets.fromLTRB(0, 0, 5, 0),
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          width: 2,
                                          color: rememberMe
                                              ? AppColors.primary
                                              : AppColors.shadow),
                                      borderRadius: BorderRadius.circular(5),
                                      color: rememberMe
                                          ? AppColors.primary
                                          : Colors.white),
                                  child: rememberMe
                                      ? const Center(
                                          child: Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 22,
                                        ))
                                      : null,
                                ),
                                const CustomText(
                                  text: 'Ghi nhớ tài khoản',
                                  bold: true,
                                )
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: SizedBox(
                                  height: 50,
                                  child: CustomButton(
                                    text: 'Đăng nhập',
                                    color: AppColors.primary,
                                    onTap: () {
                                      login();
                                    },
                                  ),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  loginWithBio(true);
                                },
                                child: Container(
                                  width: 47,
                                  height: 47,
                                  margin:
                                      const EdgeInsets.fromLTRB(10, 0, 10, 0),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                          width: 1, color: AppColors.primary)),
                                  child: const Center(
                                    child: Icon(
                                      Icons.fingerprint,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  Get.to(() => const ConfigPrinterView());
                                },
                                child: Container(
                                  width: 47,
                                  height: 47,
                                  margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                  decoration: BoxDecoration(
                                      color: AppColors.shadow,
                                      borderRadius: BorderRadius.circular(10)),
                                  child: const Icon(
                                    Icons.settings,
                                    color: Colors.white,
                                  ),
                                ),
                              )
                            ],
                          ),
                          const SizedBox(
                            height: 15,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              InkWell(
                                onTap: () {
                                  Get.to(
                                      () => const RegisterView(type: 'free'));
                                },
                                child: const CustomText(
                                  text: 'Đăng ký tài khoản',
                                  bold: true,
                                  color: AppColors.primary,
                                  size: 18,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
