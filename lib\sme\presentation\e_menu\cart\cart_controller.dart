// cart_controller.dart
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class CartSMEController extends GetxController {
  var cartItems = <Map<String, dynamic>>[].obs;
  var totalPrice = 0.0.obs;
  var selectedChoices = <int, Map<String, dynamic>>{}.obs;

  double get totalItemCount {
    return cartItems.fold(0.0, (sum, item) => sum + (item['quantity'] as double));
  }

  void addToCart({
    required Map<String, dynamic> product,
    required double quantity,
    String? note,
  }) {
    final itemSignature = product['itemSignature'];
    if (itemSignature == null) {
      throw Exception('Product must have itemSignature');
    }

    final productCopy = Map<String, dynamic>.from(product);

    // Tìm item trùng signature trong giỏ hàng
    final existingIndex = cartItems.indexWhere(
            (item) => item['product']['itemSignature'] == itemSignature
    );

    if (existingIndex >= 0) {
      // Nếu tìm thấy item trùng signature thì cộng dồn số lượng
      cartItems[existingIndex]['quantity'] += quantity;
      if (note != null && note.isNotEmpty) {
        cartItems[existingIndex]['note'] = note;
      }
    } else {
      // Nếu không tìm thấy thì thêm mới
      cartItems.add({
        'product': productCopy,
        'quantity': quantity,
        'note': note ?? '',
      });
    }

    calculateTotal();
  }

  void replaceCartItem({
    required int index,
    required Map<String, dynamic> product,
    required double quantity,
    String? note,
  }) {
    if (index < 0 || index >= cartItems.length) return;

    cartItems[index] = {
      'product': product,
      'quantity': quantity,
      'note': note ?? '',
    };

    calculateTotal();
  }

  void updateNote(int index, String note) {
    if (index >= 0 && index < cartItems.length) {
      cartItems[index]['note'] = note;
      update();
    }
  }

  bool _mapEquals(Map<String, dynamic> a, Map<String, dynamic> b) {
    if (a.length != b.length) return false;
    for (final key in a.keys) {
      final aVal = a[key];
      final bVal = b[key];
      if (aVal is List && bVal is List) {
        if (!_listEquals(aVal, bVal)) return false;
      } else if (aVal != bVal) {
        return false;
      }
    }
    return true;
  }

  bool _listEquals(List a, List b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] is Map && b[i] is Map) {
        if (!_mapEquals(a[i] as Map<String, dynamic>, b[i] as Map<String, dynamic>)) return false;
      } else if (a[i] != b[i]) {
        return false;
      }
    }
    return true;
  }

  void removeFromCart(int index) {
    cartItems.removeAt(index);
    calculateTotal();
  }

  void updateQuantity(int index, double newQuantity) {
    if (newQuantity > 0) {
      cartItems[index]['quantity'] = newQuantity;
      calculateTotal();
    }
  }

  void updateItemQuantity(int index, double newQuantity) {
    if (index >= 0.0 && index < cartItems.length && newQuantity > 0.0) {
      cartItems[index]['quantity'] = newQuantity;
      calculateTotal();
      update();
    }
  }




  void calculateTotal() {
    totalPrice.value = cartItems.fold(0.0, (sum, item) {
      final product = item['product'];
      final quantity = item['quantity'];

      // Sử dụng giá hiện tại (có thể là giá tạm thời hoặc giá gốc)
      final basePrice = (product['Price'] as num?)?.toDouble() ?? 0.0;

      double extraChoicesPrice = 0.0;

      final selectedChoices = product['SelectedChoices'];
      if (selectedChoices != null && selectedChoices is Map<String, dynamic>) {
        for (var choiceItems in selectedChoices.values) {
          if (choiceItems is List) {
            for (var choiceItem in choiceItems) {
              if (choiceItem is Map<String, dynamic>) {
                final extraPrice = (choiceItem['Price'] as num?)?.toDouble() ?? 0.0;
                extraChoicesPrice += extraPrice;
              }
            }
          }
        }
      }

      final totalItemPrice = (basePrice + extraChoicesPrice) * quantity;
      return sum + totalItemPrice;
    });
  }

  void clearCart() {
    cartItems.clear();
    totalPrice.value = 0.0;

  }

  Map<String, dynamic>? getCartItem(int index) {
    if (index >= 0 && index < cartItems.length) {
      final item = cartItems[index];
      debugPrint('Getting cart item at index $index: $item');

      // Đảm bảo product có SelectedChoices nếu nó tồn tại trong item gốc
      final product = Map<String, dynamic>.from(item['product'] ?? {});
      if (item['product']?['SelectedChoices'] != null) {
        product['SelectedChoices'] = Map<String, dynamic>.from(item['product']['SelectedChoices']);
      }

      return {
        'product': product,
        'quantity': item['quantity'] ?? 1,
        'note': item['note'] ?? '',
      };
    }
    return null;
  }
}
