import 'package:dartz/dartz.dart';
import '../../../core/usecase/usecase.dart';
import '../entities/report.dart';
import '../repositories/report.dart';

class ReportParams {
  final List<int> listBrandId;
  final String dateFrom;
  final String dateTo;


  ReportParams({
    required this.listBrandId,
    required this.dateFrom,
    required this.dateTo,
  });
}

// Dashboard
class GetOverviewRevenueUseCase extends UseCase<Either<String, OverviewRevenueEntity>, ReportParams> {
  final OverviewRevenueRepository repository;

  GetOverviewRevenueUseCase({required this.repository});

  @override
  Future<Either<String, OverviewRevenueEntity>> call({required ReportParams params}) async {
    return await repository.getOverviewRevenues(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

class GetTopBottomBranchesRevenueUseCase extends UseCase<Either<String, TopBottomBranchesRevenueEntity>, ReportParams> {
  final TopBottomBranchesRevenueRepository repository;

  GetTopBottomBranchesRevenueUseCase({required this.repository});

  @override
  Future<Either<String, TopBottomBranchesRevenueEntity>> call({required ReportParams params}) async {
    return await repository.getTopBottomBranchesRevenues(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

class GetRevenue7DaysUseCase extends UseCase<Either<String, List<Revenue7DaysEntity>>, List<int>> {
  final Revenue7DaysRepository repository;

  GetRevenue7DaysUseCase({required this.repository});

  @override
  Future<Either<String, List<Revenue7DaysEntity>>> call({required List<int> params}) async {
    return await repository.getRevenue7Days(listBrandId: params);
  }
}

class GetTopBranchesRevenueByDateUseCase extends UseCase<Either<String, List<TopBranchesRevenueByDateEntity>>, ReportParams> {
  final TopBranchesRevenueByDateRepository repository;

  GetTopBranchesRevenueByDateUseCase({required this.repository});

  @override
  Future<Either<String, List<TopBranchesRevenueByDateEntity>>> call({required ReportParams params}) async {
    return await repository.getTopBranchesRevenueByDates(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

// By Item
class GetTopAndBottomItemsRevenueUseCase extends UseCase<Either<String, TopAndBottomItemsRevenueEntity>, ReportParams> {
  final TopAndBottomItemsRevenueRepository repository;

  GetTopAndBottomItemsRevenueUseCase({required this.repository});

  @override
  Future<Either<String, TopAndBottomItemsRevenueEntity>> call({required ReportParams params}) async {
    return await repository.getTopAndBottomItemsRevenues(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

class GetTopItemsRevenueByDateUseCase extends UseCase<Either<String, List<TopItemsRevenueByDateEntity>>, ReportParams> {
  final TopItemsRevenueByDateRepository repository;

  GetTopItemsRevenueByDateUseCase({required this.repository});

  @override
  Future<Either<String, List<TopItemsRevenueByDateEntity>>> call({required ReportParams params}) async {
    return await repository.getTopItemsRevenueByDates(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

// By Category
class GetTopAndBottomCategoriesByAmountUseCase extends UseCase<Either<String, TopAndBottomCategoriesByAmountEntity>, ReportParams> {
  final TopAndBottomCategoriesByAmountRepository repository;

  GetTopAndBottomCategoriesByAmountUseCase({required this.repository});

  @override
  Future<Either<String, TopAndBottomCategoriesByAmountEntity>> call({required ReportParams params}) async {
    return await repository.getTopAndBottomCategoriesByAmounts(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

class GetTopCatsRevenueByDateUseCase extends UseCase<Either<String, List<TopCatsRevenueByDateEntity>>, ReportParams> {
  final TopCatsRevenueByDateRepository repository;

  GetTopCatsRevenueByDateUseCase({required this.repository});

  @override
  Future<Either<String, List<TopCatsRevenueByDateEntity>>> call({required ReportParams params}) async {
    return await repository.getTopCatsRevenueByDates(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

// By Payment
class GetTotalPaymentAmountUseCase extends UseCase<Either<String, List<TotalPaymentAmountEntity>>, ReportParams> {
  final TotalPaymentAmountRepository repository;

  GetTotalPaymentAmountUseCase({required this.repository});

  @override
  Future<Either<String, List<TotalPaymentAmountEntity>>> call({required ReportParams params}) async {
    return await repository.getTotalPaymentAmounts(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}

class GetTotalPaymentAmountByDateUseCase extends UseCase<Either<String, List<TotalPaymentAmountByDateEntity>>, ReportParams> {
  final TotalPaymentAmountByDateRepository repository;

  GetTotalPaymentAmountByDateUseCase({required this.repository});

  @override
  Future<Either<String, List<TotalPaymentAmountByDateEntity>>> call({required ReportParams params}) async {
    return await repository.getTotalPaymentAmountByDates(
      listBrandId: params.listBrandId,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
    );
  }
}
