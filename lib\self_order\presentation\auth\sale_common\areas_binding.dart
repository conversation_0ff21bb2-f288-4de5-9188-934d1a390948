import 'package:get/get.dart';

import '../../../data/datasources/sale_common/areas_remote_data_source.dart';
import '../../../data/repositories/sale_common/areas_repository.dart';
import '../../../domain/repositories/sale_common/areas_repository.dart';
import '../../../domain/usecases/sale_common/areas_usecase.dart';
import 'areas_controller.dart';

class AreasBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AreasRemoteDataSource>(
          () => AreasRemoteDataSourceImpl(dioClient: Get.find()),
    );

    Get.lazyPut<AreasRepository>(
          () => AreasRepositoryImpl(remoteDataSource: Get.find()),
    );

    Get.lazyPut<GetAreasUseCase>(
          () => GetAreasUseCase(repository: Get.find()),
    );

    Get.lazyPut<AreasController>(
          () => AreasController(getAreasUseCase: Get.find()),
    );
  }
}
