import 'package:get/get.dart';

import '../../../domain/entities/sale_common/areas_reponse_entity.dart';
import '../../../domain/usecases/sale_common/areas_usecase.dart';
import 'branches_controller.dart';
import 'counters_controller.dart';

class AreasController extends GetxController {
  final GetAreasUseCase getAreasUseCase;

  AreasController({required this.getAreasUseCase});

  var areas = <AreasResponseEntity>[].obs;
  var selectedArea = Rxn<AreasResponseEntity>();
  var isLoading = false.obs;
  var error = ''.obs;

  @override
  void onInit() {
    super.onInit();

    final branchController = Get.find<BranchesController>();
    final counterController = Get.find<CounterController>();

    everAll([
      branchController.selectedBranch,
      counterController.selectedCounter,
    ], (_) {
      final branch = branchController.selectedBranch.value;
      final counter = counterController.selectedCounter.value;

      if (branch != null && counter != null) {
        fetchAreas(branch.branchId ?? 0, counter.counterId ?? 0);
      }
    });
  }

  Future<void> fetchAreas(int branchId, int counterId) async {
    try {
      isLoading(true);
      final response = await getAreasUseCase(branchId, counterId);
      areas.value = response;
      selectedArea.value = null;
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading(false);
    }
  }
}
