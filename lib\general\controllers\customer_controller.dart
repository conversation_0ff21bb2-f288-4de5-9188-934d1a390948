import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';

class CustomerController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();

  //function
  getList(page, perPage, from, to, keySearch) async {
    List list = [];
    dynamic pagination;

    dynamic body = {
      "PageNumber": page,
      "PageSize": perPage,
      "CreationDateFrom": from,
      "CreationDateTo": to,
      "Keyword": keySearch == '' ? null : keySearch,
    };

    try {
      final response = await dioClient.post(
          SmeUrl.customerList,
          data: body
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        list = data['Result']['Data'];
        pagination = data['Result']['Pagination'];
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
    }

    return [list, pagination];
  }

  getDetail(customerNo) async {
    dynamic item;
    try {
      final response = await dioClient.get(
        '${SmeUrl.customerDetail}?customerNo=$customerNo',
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        item = data['Result'];
      }
    } catch (e) {

    }
    return item;
  }

  postCreate(
      name,
      phone,
      dob,
      gender,
      address) async {
    int customerId = 0;
    if (name == '') {
      AppFunction.showError('Vui lòng nhập tên khách hàng');
      return customerId;
    }

    if (phone == '') {
      AppFunction.showError('Vui lòng nhập số điện thoại');
      return customerId;
    }

    dynamic body = {
      "FullName": name,
      "PhoneNumber": phone,
      "Gender": gender,
      "Birthday": dob,
      "Address": address
    };

    try {
      final response = await dioClient.post(
          SmeUrl.customerCreate,
          data: body
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        customerId = data['Result']['CustomerId'];
        AppFunction.showSuccess(data['Message']);
        return customerId;
      }
      else {
        AppFunction.showError(data['Message']);
        return customerId;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return customerId;
    }
  }

  postUpdate(
      id,
      name,
      phone,
      dob,
      gender,
      address) async {

    if (name == '') {
      AppFunction.showError('Vui lòng nhập tên khách hàng');
      return false;
    }

    if (phone == '') {
      AppFunction.showError('Vui lòng nhập số điện thoại');
      return false;
    }

    dynamic body = {
      "CustomerId": id,
      "FullName": name,
      "PhoneNumber": phone,
      "Gender": gender,
      "Birthday": dob,
      "Address": address
    };

    try {
      final response = await dioClient.put(
          SmeUrl.customerUpdate,
          data: body
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        AppFunction.showSuccess(data['Message']);
        return true;
      }
      else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  postDelete(id) async {
    try {
      final response = await dioClient.delete(
        '${SmeUrl.customerDelete}?Id=$id',
      );
      dynamic data = response;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      }
      else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

}