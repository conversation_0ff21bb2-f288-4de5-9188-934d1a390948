class FormatHelper {
  static String formatCurrency(num value) {
    if (value >= 1e9) {
      return '${_removeTrailingZero(value / 1e9)}T';
    } else if (value >= 1e6) {
      return '${_removeTrailingZero(value / 1e6)}Tr';
    } else if (value >= 1e3) {
      return '${_removeTrailingZero(value / 1e3)}K';
    }
    return value.toInt().toString();
  }

  static String _removeTrailingZero(double number) {
    return number % 1 == 0 ? number.toInt().toString() : number.toStringAsFixed(1);
  }
}
