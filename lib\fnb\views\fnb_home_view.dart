import 'package:flutter/material.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/fnb/views/fnb_detail_view.dart';

class FnbHomeView extends StatefulWidget {
  const FnbHomeView({super.key});

  @override
  State<FnbHomeView> createState() => _FnbHomeViewState();
}

class _FnbHomeViewState extends State<FnbHomeView> {
  //variable
  List floors = [
    {'id': 0, 'name': 'Tầng trệt'},
    {'id': 1, 'name': 'Tầng 1'},
    {'id': 2, 'name': 'Tầng 2'},
    {'id': 3, 'name': 'Tầng 3'},
    {'id': 4, 'name': 'Tầng 4'},
  ];

  int floorSelected = 0;

  List tables = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18];

  String viewType = 'table';

  //function
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double statusBarHeight = MediaQuery.of(context).viewPadding.top;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            Container(
              width: double.infinity,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primary
              ),
              padding: EdgeInsets.fromLTRB(10, statusBarHeight + 10, 10, 10),
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(Icons.arrow_back_ios_new, color: Colors.white,),
                      ],
                    ),
                  ),
                  SizedBox(width: 10,),
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.zero,
                      scrollDirection: Axis.horizontal,
                      children: [
                        for (dynamic item in floors)
                          InkWell(
                            onTap: () {
                              floorSelected = item['id'];
                              setState(() {

                              });
                            },
                            child: Container(
                              width: 120,
                              height: 50,
                              decoration: BoxDecoration(
                                  color: floorSelected == item['id'] ? AppColors.button : Colors.white,
                                  border: Border.all(
                                      width: 1,
                                      color: floorSelected == item['id'] ? AppColors.button : Colors.black54
                                  ),
                                  borderRadius: BorderRadius.circular(5)
                              ),
                              margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                              child: Center(
                                child: CustomText(text: item['name'], bold: true, color: floorSelected == item['id'] ? Colors.white : AppColors.primary,),
                              ),
                            ),
                          )
                      ],
                    ),
                  )
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        width: 1,
                        color: AppColors.primary
                      )
                    ),
                  ),
                  CustomText(text: ' Bàn trống', color: Colors.black, size: 13,),
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                        color: AppColors.primary,
                        border: Border.all(
                            width: 1,
                            color: AppColors.primary
                        )
                    ),
                    margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                  ),
                  CustomText(text: ' Bàn có khách', color: AppColors.primary, size: 13,),
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                        color: AppColors.danger,
                        border: Border.all(
                            width: 1,
                            color: AppColors.danger
                        )
                    ),
                    margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                  ),
                  CustomText(text: ' Bàn đặt trước', color: AppColors.danger, size: 13,),
                ],
              ),
            ),
            Container(
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        viewType = 'table';
                        setState(() {

                        });
                      },
                      child: Container(
                        width: double.infinity,
                        height: 50,
                        margin: EdgeInsets.fromLTRB(2, 0, 2, 0),
                        decoration: BoxDecoration(
                            color: viewType == 'table' ? AppColors.primary : Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20)
                            ),
                            border: Border.all(
                                width: 1,
                                color: AppColors.primary
                            )
                        ),
                        child: Center(
                          child: CustomText(text: 'Sơ đồ bàn\n(15)', bold: true, color: viewType == 'table' ? Colors.white : AppColors.primary, size: 13, maxLines: 2, textAlign: TextAlign.center,),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        viewType = 'dine_in';
                        setState(() {

                        });
                      },
                      child: Container(
                        width: double.infinity,
                        height: 50,
                        margin: EdgeInsets.fromLTRB(2, 0, 2, 0),
                        decoration: BoxDecoration(
                            color: viewType == 'dine_in' ? AppColors.button : Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20)
                            ),
                            border: Border.all(
                                width: 1,
                                color: AppColors.button
                            )
                        ),
                        child: Center(
                          child: CustomText(text: 'Tại chỗ\n(5)', maxLines: 2, bold: true, size: 13, color: viewType == 'dine_in' ? Colors.white : AppColors.button, textAlign: TextAlign.center,),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        viewType = 'take';
                        setState(() {

                        });
                      },
                      child: Container(
                        width: double.infinity,
                        height: 50,
                        margin: EdgeInsets.fromLTRB(2, 0, 2, 0),
                        decoration: BoxDecoration(
                            color: viewType == 'take' ? AppColors.warning : Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20)
                            ),
                            border: Border.all(
                                width: 1,
                                color: AppColors.warning
                            )
                        ),
                        child: Center(
                          child: CustomText(text: 'Đến lấy\n(15)', bold: true, size: 13, color: viewType == 'take' ? Colors.white : AppColors.warning, textAlign: TextAlign.center, maxLines: 2,),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Container(
                      width: double.infinity,
                      height: 50,
                      margin: EdgeInsets.fromLTRB(2, 0, 2, 0),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20)
                          ),
                          border: Border.all(
                              width: 1,
                              color: AppColors.lightBrown
                          )
                      ),
                      child: Center(
                        child: CustomText(text: 'Giao hàng\n(1)', bold: true, size: 13, color: AppColors.lightBrown, textAlign: TextAlign.center, maxLines: 2,),
                      ),
                    ),
                  )
                ],
              ),
            ),
            if (viewType == 'table')
            Expanded(
              child: Container(
                child: GridView.builder(
                  padding: EdgeInsets.all(5),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 5,
                      mainAxisSpacing: 5.0,
                  ),
                  itemCount: tables.length,
                  itemBuilder: (BuildContext context, int index) {
                    final table = tables[index];
                    return Container(
                      width: double.infinity,
                      child: Stack(
                        children: [
                          Image.asset('assets/images/fnb/table.jpg', fit: BoxFit.fitWidth,),
                          Align(
                            alignment: Alignment.center,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CustomText(text: 'Bàn ${table}', bold: true, color: AppColors.primary,),
                                CustomText(text: '👤2   14:45', bold: true, color: AppColors.primary, size: 14,),
                                CustomText(text: 'Ms. Dao', bold: true, color: AppColors.primary, size: 14,),
                                CustomText(text: '250K', bold: true, color: AppColors.primary, size: 14,),
                              ],
                            )
                          )
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            if (viewType == 'dine_in')
              Expanded(
                child: Container(
                  child: GridView.builder(
                    padding: EdgeInsets.all(10),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 10.0,
                      mainAxisSpacing: 10.0,
                      childAspectRatio: 0.9
                    ),
                    itemCount: tables.length,
                    itemBuilder: (BuildContext context, int index) {
                      final table = tables[index];
                      return Container(
                        // color: Colors.blue,
                        child: Column(
                          children: [
                            Container(
                              color: AppColors.button,
                              padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      CustomText(text: 'Bill: 2805', bold: true, color: Colors.white,),
                                      CustomText(text: '12:09', color: Colors.white,)
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      CustomText(text: 'Bàn: 3', bold: true, color: Colors.white,),
                                      // CustomText(text: 'MS.DAO', color: Colors.white,)
                                    ],
                                  )
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                              child: Column(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                width: 1,
                                                color: AppColors.shadow
                                            )
                                        )
                                    ),
                                    padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(text: 'Khách hàng', size: 13,),
                                        CustomText(text: 'MS. Dao', size: 13, bold: true,)
                                      ],
                                    ),
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                width: 1,
                                                color: AppColors.shadow
                                            )
                                        )
                                    ),
                                    padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(text: 'Tổng tiền', size: 13,),
                                        CustomText(text: '125.000', size: 13, bold: true, color: AppColors.danger,)
                                      ],
                                    ),
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                width: 1,
                                                color: AppColors.shadow
                                            )
                                        )
                                    ),
                                    padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(text: 'Tổng món', size: 13,),
                                        CustomText(text: '10 món', size: 13, bold: true, color: AppColors.primary,)
                                      ],
                                    ),
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                width: 1,
                                                color: AppColors.shadow
                                            )
                                        )
                                    ),
                                    padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(text: 'SL khách', size: 13,),
                                        CustomText(text: '04', size: 13, bold: true,)
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 5,),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          Get.to(() => FnbDetailView());
                                        },
                                        child: Container(
                                          width: 35,
                                          height: 35,
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  width: 1,
                                                  color: AppColors.primary
                                              )
                                          ),
                                          child: Center(
                                            child: Icon(Icons.edit),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 10,),
                                      Container(
                                        width: 35,
                                        height: 35,
                                        decoration: BoxDecoration(
                                            border: Border.all(
                                                width: 1,
                                                color: AppColors.primary
                                            )
                                        ),
                                        child: Center(
                                          child: Icon(Icons.view_list_outlined),
                                        ),
                                      ),
                                      SizedBox(width: 10,),
                                      Container(
                                        width: 35,
                                        height: 35,
                                        decoration: BoxDecoration(
                                            border: Border.all(
                                                width: 1,
                                                color: AppColors.danger
                                            )
                                        ),
                                        child: Center(
                                          child: Icon(Icons.attach_money),
                                        ),
                                      )
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ],
                        )
                      );
                    },
                  ),
                ),
              ),
            if (viewType == 'take')
              Expanded(
                child: Container(
                  child: GridView.builder(
                    padding: EdgeInsets.all(10),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 10.0,
                        mainAxisSpacing: 10.0,
                        childAspectRatio: 0.9
                    ),
                    itemCount: tables.length,
                    itemBuilder: (BuildContext context, int index) {
                      final table = tables[index];
                      return Container(
                          child: Column(
                            children: [
                              Container(
                                color: AppColors.warning,
                                padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(text: 'Bill: 2805', bold: true, color: Colors.white,),
                                        CustomText(text: '12:09', color: Colors.white,)
                                      ],
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(text: 'Bàn: 3', bold: true, color: Colors.white,),
                                        // CustomText(text: 'MS.DAO', color: Colors.white,)
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                child: Column(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                                  width: 1,
                                                  color: AppColors.shadow
                                              )
                                          )
                                      ),
                                      padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          CustomText(text: 'Khách hàng', size: 13,),
                                          CustomText(text: 'MS. Dao', size: 13, bold: true,)
                                        ],
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                                  width: 1,
                                                  color: AppColors.shadow
                                              )
                                          )
                                      ),
                                      padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          CustomText(text: 'Tổng tiền', size: 13,),
                                          CustomText(text: '125.000', size: 13, bold: true, color: AppColors.danger,)
                                        ],
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                                  width: 1,
                                                  color: AppColors.shadow
                                              )
                                          )
                                      ),
                                      padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          CustomText(text: 'Tổng món', size: 13,),
                                          CustomText(text: '10 món', size: 13, bold: true, color: AppColors.primary,)
                                        ],
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                                  width: 1,
                                                  color: AppColors.shadow
                                              )
                                          )
                                      ),
                                      padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          CustomText(text: 'SL khách', size: 13,),
                                          CustomText(text: '04', size: 13, bold: true,)
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 5,),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            Get.to(() => FnbDetailView());
                                          },
                                          child: Container(
                                            width: 35,
                                            height: 35,
                                            decoration: BoxDecoration(
                                                border: Border.all(
                                                    width: 1,
                                                    color: AppColors.primary
                                                )
                                            ),
                                            child: Center(
                                              child: Icon(Icons.edit),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10,),
                                        Container(
                                          width: 35,
                                          height: 35,
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  width: 1,
                                                  color: AppColors.primary
                                              )
                                          ),
                                          child: Center(
                                            child: Icon(Icons.view_list_outlined),
                                          ),
                                        ),
                                        SizedBox(width: 10,),
                                        Container(
                                          width: 35,
                                          height: 35,
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  width: 1,
                                                  color: AppColors.danger
                                              )
                                          ),
                                          child: Center(
                                            child: Icon(Icons.attach_money),
                                          ),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ],
                          )
                      );
                    },
                  ),
                ),
              ),
            if (viewType == 'table')
            Container(
              height: 150,
              color: AppColors.primary,
              padding: EdgeInsets.fromLTRB(10, 10, 10, 20),
              child: Row(
                children: [
                  Expanded(
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.zero,
                      children: [
                        Container(
                          width: 150,
                          height: double.infinity,
                          decoration: BoxDecoration(
                              color: Colors.white
                          ),
                          margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                          child: Column(
                            children: [
                              Container(
                                width: double.infinity,
                                color: AppColors.button,
                                padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                child: CustomText(text: 'Bàn: 3', color: Colors.white, bold: true,),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                  child: Column(
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Tổng đơn hàng', size: 13,),
                                            CustomText(text: '2', size: 13, bold: true, color: AppColors.primary,)
                                          ],
                                        ),
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Tổng SL món', size: 13,),
                                            CustomText(text: '5 món', size: 13, bold: true,)
                                          ],
                                        ),
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Hoàn thành', size: 13,),
                                            CustomText(text: '3/5', size: 13, bold: true,)
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        Container(
                          width: 150,
                          height: double.infinity,
                          decoration: BoxDecoration(
                              color: Colors.white
                          ),
                          margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                          child: Column(
                            children: [
                              Container(
                                width: double.infinity,
                                color: AppColors.warning,
                                padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                child: CustomText(text: 'Bill: 424', color: Colors.white, bold: true,),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                  child: Column(
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Tổng đơn hàng', size: 13,),
                                            CustomText(text: '2', size: 13, bold: true, color: AppColors.primary,)
                                          ],
                                        ),
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Tổng SL món', size: 13,),
                                            CustomText(text: '5 món', size: 13, bold: true,)
                                          ],
                                        ),
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Hoàn thành', size: 13,),
                                            CustomText(text: '3/5', size: 13, bold: true,)
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        Container(
                          width: 150,
                          height: double.infinity,
                          decoration: BoxDecoration(
                              color: Colors.white
                          ),
                          child: Column(
                            children: [
                              Container(
                                width: double.infinity,
                                color: AppColors.lightBrown,
                                padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                child: CustomText(text: 'Bill: 574', color: Colors.white, bold: true,),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                                  child: Column(
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Tổng đơn hàng', size: 13,),
                                            CustomText(text: '2', size: 13, bold: true, color: AppColors.primary,)
                                          ],
                                        ),
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Tổng SL món', size: 13,),
                                            CustomText(text: '5 món', size: 13, bold: true,)
                                          ],
                                        ),
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1,
                                                    color: AppColors.shadow
                                                )
                                            )
                                        ),
                                        padding: EdgeInsets.fromLTRB(0, 2.5, 0, 2.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(text: 'Hoàn thành', size: 13,),
                                            CustomText(text: '3/5', size: 13, bold: true,)
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
