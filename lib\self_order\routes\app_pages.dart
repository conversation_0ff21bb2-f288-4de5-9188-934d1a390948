import 'package:get/get.dart';
import '../presentation/auth/login/auth_binding.dart';
import '../presentation/auth/login/login_page.dart';
import '../presentation/auth/sale_common/areas_binding.dart';
import '../presentation/auth/sale_common/branches_binding.dart';
import '../presentation/auth/sale_common/counters_binding.dart';
import '../presentation/auth/sale_common/tables_binding.dart';
import '../presentation/banner/banner_binding.dart';
import '../presentation/banner/banner_controller.dart';
import '../presentation/banner/banner_page.dart';
import '../presentation/check_out/check_out_binding.dart';
import '../presentation/check_out/check_out_page.dart';
import '../presentation/e_menu/cart/cart_binding.dart';
import '../presentation/e_menu/e_menu_binding.dart';
import '../presentation/e_menu/e_menu_page.dart';
import '../presentation/e_menu/product_detail/product_detail_binding.dart';
import '../presentation/e_menu/product_detail/product_detail_page.dart';
import '../presentation/splash/splash_page.dart';
import '../presentation/start_order/start_order_page.dart';
import 'app_routes.dart';

class AppPages {
  static final pages = [
    GetPage(
      name: AppRoutes.splash,
      page: () => SplashPage(),
      binding: AuthSOBinding(),
    ),
    GetPage(
      name: AppRoutes.login,
      page: () => LoginPage(),
      bindings: [
        AuthSOBinding(),
        BranchesBinding(),
        CounterBinding(),
        AreasBinding(),
        TablesBinding(),
      ],
    ),
    GetPage(
      name: AppRoutes.banner,
      page: () => BannerPage(),
      bindings: [
        AuthSOBinding(),
        BannerBinding(),
      ]
    ),
    GetPage(
      name: AppRoutes.startOrder,
      page: () => StartOrderPage(),
    ),
    GetPage(
      name: AppRoutes.menu,
      page: () => EMenuPage(),
      binding: EMenuBinding(),
    ),

    GetPage(
      name: AppRoutes.productDetail,
      page: () => const ProductDetailPage(),
      binding: ProductDetailBinding(),
    ),
    GetPage(
      name: AppRoutes.checkout,
      page: () => CheckoutPage(),
      bindings: [
        CartBinding(),
        CheckoutBinding(),
      ]
    ),
  ];
} 