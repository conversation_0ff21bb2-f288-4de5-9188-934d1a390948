import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/emenu/views/emenu_home_view.dart';
import 'package:gls_self_order/self_order/presentation/splash/splash_page.dart';

import '../../core/components/custom_text.dart';
import '../../sme/presentation/e_menu/e_menu_page.dart';


class AppsView extends StatefulWidget {
  const AppsView({super.key});

  @override
  State<AppsView> createState() => _AppsViewState();
}

class _AppsViewState extends State<AppsView> {
  //variable

  //function
  @override
  void initState() {
    super.initState();
  }

  Widget renderItemMainButton(title, image, view) {
    return Expanded(
      flex: 1,
      child: InkWell(
        onTap: () {
          if (view == 'emenu') {
            Get.to(() => EmenuHomeView());
          }
          else if (view == 'self-order') {
            Get.to(() => SplashPage());
          }
          else if (view == 'sme') {
            Get.to(() => EMenuPage());
          }
        },
        child: Container(
          padding: EdgeInsets.all(20),
          margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(image, width: 100,),
              CustomText(text: title, bold: true,)
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Bán hàng'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Column(
        children: [
          Row(
            children: [
              renderItemMainButton('Self-order', 'assets/images/general/ban_hang.png', 'self-order'),
              renderItemMainButton('SME', 'assets/images/general/ban_hang.png', 'sme'),
            ],
          ),
          Row(
            children: [
              renderItemMainButton('Emenu', 'assets/images/general/ban_hang.png', 'emenu'),
              renderItemMainButton('POS', 'assets/images/general/ban_hang.png', 'pos'),
            ],
          )
        ],
      ),
    );
  }
}
